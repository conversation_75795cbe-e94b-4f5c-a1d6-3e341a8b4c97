/**
 * @file Facebook Custom Audiences Interfaces
 * 
 * <PERSON><PERSON><PERSON> nghĩa các interfaces cho Facebook Custom Audiences integration
 * Theo patterns từ Make.com chuẩn
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

import {
    IBaseIntegrationParameters,
    ITriggerParameters,
    IActionParameters,
    ISearchParameters,
    IBaseIntegrationInput,
    IBaseIntegrationOutput,
    EIntegrationOperationType,
    EIntegrationErrorHandling,
    IBaseIntegrationCredential
} from '../../base/base-integration.interface';

import {
    ECredentialName,
    ENodeAuthType,
    EPropertyType,
    ELoadOptionsResource,
    ELoadOptionsMethod,
    INodeProperty
} from '../../../node-manager.interface';

import {
    ITypedNodeExecution
} from '../../../execute.interface';

import {
    EFacebookCustomAudiencesOperation,
    EFacebookCustomAudienceType,
    EFacebookCustomAudienceSubtype,
    EFacebookLookalikeSpec,
    EFacebookAudienceMatchKey,
    EFacebookAudienceOperationType,
    EFacebookLookalikeAudienceType,
    EFacebookAudienceStatus
} from './facebook-custom-audiences.types';

// =================================================================
// SECTION 1: INPUT/OUTPUT INTERFACES
// =================================================================

/**
 * Facebook Custom Audiences input interface
 */
export interface IFacebookCustomAudiencesInput extends IBaseIntegrationInput {
    /** Operation type - Loại thao tác */
    operation: EFacebookCustomAudiencesOperation;
    
    /** Parameters specific to the operation - Tham số cụ thể cho thao tác */
    parameters: IFacebookCustomAudiencesParameters;
}

/**
 * Facebook Custom Audiences output interface
 */
export interface IFacebookCustomAudiencesOutput extends IBaseIntegrationOutput {
    /** Operation result data - Dữ liệu kết quả thao tác */
    data: {
        /** Custom audience data - Dữ liệu đối tượng tùy chỉnh */
        custom_audience?: any;
        /** Lookalike audience data - Dữ liệu đối tượng tương tự */
        lookalike_audience?: any;
        /** Audience members data - Dữ liệu thành viên đối tượng */
        audience_members?: any[];
        /** Operation result - Kết quả thao tác */
        result?: {
            success: boolean;
            message?: string;
            audience_id?: string;
            members_added?: number;
            members_removed?: number;
        };
    };
}

// =================================================================
// SECTION 2: PARAMETER INTERFACES
// =================================================================

/**
 * Create Custom Audience parameters - Tham số tạo đối tượng tùy chỉnh
 */
export interface ICreateCustomAudienceParameters extends IActionParameters {
    operation: EFacebookCustomAudiencesOperation.CREATE_CUSTOM_AUDIENCE;

    /** Business Manager - Business Manager ID */
    business_manager: string;

    /** Name - Tên đối tượng tùy chỉnh */
    name: string;

    /** Description - Mô tả đối tượng tùy chỉnh */
    description?: string;
}

/**
 * Add Emails to Custom Audience parameters - Tham số thêm email vào đối tượng tùy chỉnh
 */
export interface IAddEmailsToCustomAudienceParameters extends IActionParameters {
    operation: EFacebookCustomAudiencesOperation.ADD_EMAILS_TO_CUSTOM_AUDIENCE;

    /** Business Manager - Business Manager ID */
    business_manager: string;

    /** Emails - Danh sách email cần thêm */
    emails: string[];
}

/**
 * User data for Add Users to Custom Audience - Dữ liệu người dùng để thêm vào custom audience
 */
export interface IUserData {
    /** Email - Địa chỉ email */
    email?: string;

    /** Phone - Số điện thoại (loại bỏ ký hiệu, chữ cái, số 0 đầu) */
    phone?: string;

    /** Gender - Giới tính */
    gender?: string;

    /** Birth year - Năm sinh (định dạng YYYY từ 1900 đến hiện tại) */
    birth_year?: string;

    /** Birth month - Tháng sinh (định dạng MM: 01-12) */
    birth_month?: string;

    /** Birth day - Ngày sinh (định dạng DD: 01-31) */
    birth_day?: string;

    /** First name - Tên (chỉ a-z, không dấu câu) */
    first_name?: string;

    /** Last name - Họ (chỉ a-z, không dấu câu) */
    last_name?: string;

    /** First name initial - Chữ cái đầu tên (chỉ a-z) */
    first_name_initial?: string;

    /** State - Bang/Tỉnh (mã ANSI 2 ký tự) */
    state?: string;

    /** City - Thành phố (chỉ a-z, không dấu câu) */
    city?: string;

    /** ZIP - Mã bưu điện (5 số đầu cho US) */
    zip?: string;

    /** Country - Quốc gia (mã ISO 3166-1 alpha-2) */
    country?: string;

    /** Mobile advertiser ID - ID quảng cáo di động (lowercase, giữ dấu gạch ngang) */
    mobile_advertiser_id?: string;

    /** External ID - ID bên ngoài */
    external_id?: string;

    /** Lookalike Value - Giá trị lookalike */
    lookalike_value?: string;

    /** Page Scoped User ID - ID người dùng trong phạm vi trang */
    page_scoped_user_id?: string;

    /** Page ID - ID trang (bắt buộc nếu có Page Scoped User ID) */
    page_id?: string;
}

/**
 * Add Users to Custom Audience parameters - Tham số thêm người dùng vào đối tượng tùy chỉnh
 */
export interface IAddUsersToCustomAudienceParameters extends IActionParameters {
    operation: EFacebookCustomAudiencesOperation.ADD_USERS_TO_CUSTOM_AUDIENCE;

    /** Business Manager - Business Manager ID */
    business_manager: string;

    /** Data - Danh sách dữ liệu người dùng */
    data: IUserData[];
}

/**
 * Remove Audience Members parameters - Tham số xóa thành viên khỏi đối tượng tùy chỉnh
 */
export interface IRemoveAudienceMembersParameters extends IActionParameters {
    operation: EFacebookCustomAudiencesOperation.REMOVE_AUDIENCE_MEMBERS;

    /** Business Manager - Business Manager ID */
    business_manager: string;

    /** Remove members by - Cách thức xóa thành viên (email hoặc external ID) */
    remove_members_by: string;
}

/**
 * Source specification for lookalike - Thông số nguồn cho lookalike
 */
export interface ISourceSpecification {
    /** Type - Loại (ratio) */
    type: 'ratio';

    /** Starting ratio - Tỷ lệ bắt đầu */
    starting_ratio?: number;

    /** Ratio - Tỷ lệ (0.01-0.20 incremented by 0.01) */
    ratio: number;
}

/**
 * Geo locations specification - Thông số vị trí địa lý
 */
export interface IGeoLocationsSpecification {
    /** Countries - Danh sách quốc gia */
    countries?: string[];

    /** Country groups - Nhóm quốc gia */
    country_groups?: string[];
}

/**
 * Location specification for lookalike - Thông số vị trí cho lookalike
 */
export interface ILocationSpecification {
    /** Geo locations - Vị trí địa lý */
    geo_locations: IGeoLocationsSpecification;

    /** Excluded geo locations - Vị trí địa lý loại trừ */
    excluded_geo_locations?: IGeoLocationsSpecification;
}

/**
 * Simple location specification - Thông số vị trí đơn giản
 */
export interface ISimpleLocationSpecification {
    /** Type - Loại location */
    type: 'simple_country';

    /** Country - Quốc gia */
    country: string;
}

/**
 * Complex location specification - Thông số vị trí phức tạp
 */
export interface IComplexLocationSpecification {
    /** Type - Loại location */
    type: 'location_specification';

    /** Location specification - Thông số vị trí chi tiết */
    location_specification: ILocationSpecification;
}

/**
 * Union type for location - Union type cho location
 */
export type ILocationUnion = ISimpleLocationSpecification | IComplexLocationSpecification;

/**
 * Lookalike specification - Thông số lookalike audience
 */
export interface ILookalikeSpecification {
    /** Source - Nguồn audience */
    source: ISourceSpecification;

    /** Allow international seeds - Cho phép seeds quốc tế */
    allow_international_seeds?: 'Yes' | 'No' | 'Empty';

    /** Location - Vị trí (có thể là simple country hoặc location specification) */
    location: ILocationUnion;
}

/**
 * Create Lookalike Audience parameters - Tham số tạo đối tượng tương tự
 */
export interface ICreateLookalikeAudienceParameters extends IActionParameters {
    operation: EFacebookCustomAudiencesOperation.CREATE_LOOKALIKE_AUDIENCE;

    /** Business Manager - Business Manager ID */
    business_manager: string;

    /** Name - Tên lookalike audience */
    name: string;

    /** Lookalike specification - Thông số lookalike */
    lookalike_specification: ILookalikeSpecification;
}

/**
 * Page Fan Lookalike specification - Thông số lookalike audience dựa trên fan trang
 */
export interface IPageFanLookalikeSpecification {
    /** Page - Page ID */
    page: string;

    /** Country - Quốc gia */
    country: string;

    /** Allow international seeds - Cho phép seeds quốc tế */
    allow_international_seeds?: 'Yes' | 'No' | 'Empty';

    /** Starting ratio - Tỷ lệ bắt đầu (optional) */
    starting_ratio?: number;

    /** Ratio - Tỷ lệ (0.01-0.20) */
    ratio: number;
}

/**
 * Create Page Fan Lookalike Audience parameters - Tham số tạo đối tượng tương tự dựa trên fan trang
 */
export interface ICreatePageFanLookalikeAudienceParameters extends IActionParameters {
    operation: EFacebookCustomAudiencesOperation.CREATE_PAGE_FAN_LOOKALIKE_AUDIENCE;

    /** Business Manager - Business Manager ID */
    business_manager: string;

    /** Lookalike specification - Thông số lookalike */
    lookalike_specification: IPageFanLookalikeSpecification;
}

/**
 * Campaign/Ad Set Conversion Lookalike specification - Thông số lookalike audience dựa trên conversion
 */
export interface ICampaignConversionLookalikeSpecification {
    /** Country - Quốc gia */
    country: string;

    /** Allow international seeds - Cho phép seeds quốc tế */
    allow_international_seeds?: 'Yes' | 'No' | 'Empty';

    /** Starting ratio - Tỷ lệ bắt đầu (optional) */
    starting_ratio?: number;

    /** Ratio - Tỷ lệ (0.01-0.20) */
    ratio: number;
}

/**
 * Create Campaign or Ad Set Conversion Lookalikes parameters - Tham số tạo đối tượng tương tự dựa trên conversion
 */
export interface ICreateCampaignConversionLookalikesParameters extends IActionParameters {
    operation: EFacebookCustomAudiencesOperation.CREATE_CAMPAIGN_CONVERSION_LOOKALIKES;

    /** Business Manager - Business Manager ID */
    business_manager: string;

    /** Lookalike specification - Thông số lookalike */
    lookalike_specification: ICampaignConversionLookalikeSpecification;
}

/**
 * Create Value-Based Custom Audience parameters - Tham số tạo đối tượng tùy chỉnh dựa trên giá trị
 */
export interface ICreateValueBasedCustomAudienceParameters extends IActionParameters {
    operation: EFacebookCustomAudiencesOperation.CREATE_VALUE_BASED_CUSTOM_AUDIENCE;

    /** Business Manager - Business Manager ID */
    business_manager: string;

    /** Name - Tên của value-based custom audience */
    name: string;
}

/**
 * Seed audience data - Dữ liệu seed audience với lookalike value
 */
export interface ISeedAudienceData {
    /** Lookalike value - Giá trị lookalike (required) */
    lookalike_value: string;

    /** Email - Địa chỉ email */
    email?: string;

    /** Phone - Số điện thoại (loại bỏ ký hiệu, chữ cái, số 0 đầu) */
    phone?: string;

    /** Gender - Giới tính */
    gender?: string;

    /** Birth year - Năm sinh (định dạng YYYY từ 1900 đến hiện tại) */
    birth_year?: string;

    /** Birth month - Tháng sinh (định dạng MM: 01-12) */
    birth_month?: string;

    /** Birth day - Ngày sinh (định dạng DD: 01-31) */
    birth_day?: string;

    /** First name - Tên (chỉ a-z, không dấu câu, ký tự đặc biệt UTF8) */
    first_name?: string;

    /** Last name - Họ (chỉ a-z, không dấu câu, ký tự đặc biệt UTF8) */
    last_name?: string;

    /** First name initial - Chữ cái đầu tên (chỉ a-z, ký tự đặc biệt UTF8) */
    first_name_initial?: string;

    /** State - Bang/Tỉnh (mã ANSI 2 ký tự) */
    state?: string;

    /** City - Thành phố (chỉ a-z, không dấu câu, không ký tự đặc biệt) */
    city?: string;

    /** ZIP - Mã bưu điện (5 số đầu cho US) */
    zip?: string;

    /** Country - Quốc gia (mã 2 chữ cái ISO 3166-1 alpha-2) */
    country?: string;

    /** Mobile advertiser ID - ID quảng cáo di động (lowercase, giữ dấu gạch ngang) */
    mobile_advertiser_id?: string;

    /** External ID - ID bên ngoài */
    external_id?: string;
}

/**
 * Populate Seed Audience parameters - Tham số populate seed audience
 */
export interface IPopulateSeedAudienceParameters extends IActionParameters {
    operation: EFacebookCustomAudiencesOperation.POPULATE_SEED_AUDIENCE;

    /** Business Manager - Business Manager ID */
    business_manager: string;

    /** Data - Danh sách dữ liệu seed audience */
    data: ISeedAudienceData[];
}

/**
 * Value-Based Lookalike specification - Thông số lookalike audience dựa trên giá trị
 */
export interface IValueBasedLookalikeSpecification {
    /** Ratio - Tỷ lệ (0.01-0.20) */
    ratio: number;

    /** Country - Quốc gia */
    country: string;
}

/**
 * Create Value-Based Lookalike parameters - Tham số tạo đối tượng tương tự dựa trên giá trị
 */
export interface ICreateValueBasedLookalikeParameters extends IActionParameters {
    operation: EFacebookCustomAudiencesOperation.CREATE_VALUE_BASED_LOOKALIKE;

    /** Business Manager - Business Manager ID */
    business_manager: string;

    /** Name - Tên của value-based lookalike audience */
    name: string;

    /** Lookalike specification - Thông số lookalike */
    lookalike_specification: IValueBasedLookalikeSpecification;
}

/**
 * Union type cho tất cả Facebook Custom Audiences parameters
 */
export type IFacebookCustomAudiencesParameters =
    | ICreateCustomAudienceParameters
    | IAddEmailsToCustomAudienceParameters
    | IAddUsersToCustomAudienceParameters
    | IRemoveAudienceMembersParameters
    | ICreateLookalikeAudienceParameters
    | ICreatePageFanLookalikeAudienceParameters
    | ICreateCampaignConversionLookalikesParameters
    | ICreateValueBasedCustomAudienceParameters
    | IPopulateSeedAudienceParameters
    | ICreateValueBasedLookalikeParameters;
