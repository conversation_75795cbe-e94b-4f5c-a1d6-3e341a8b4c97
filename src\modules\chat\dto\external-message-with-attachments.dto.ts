import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsBoolean, IsEnum, IsOptional, IsUUID, ValidateNested } from 'class-validator';
import { MessageRole } from '@/shared';
import { ExternalMessageAttachmentDto } from './external-message-attachment.dto';
import { Type } from 'class-transformer';

/**
 * External Message Response DTO with full data including attachments
 */
export class ExternalMessageWithAttachmentsDto {
  @ApiProperty({
    description: 'Unique identifier of the message',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsUUID()
  messageId: string;

  @ApiProperty({
    description: 'Text content of the message',
    example: 'Hello, I need help with my order',
  })
  @IsString()
  messageText: string;

  @ApiProperty({
    description: 'Message creation timestamp in milliseconds',
    example: 1671234567890,
  })
  @IsNumber()
  messageCreatedAt: number;

  @ApiProperty({
    description: 'Whether the message has attachments',
    example: true,
  })
  @IsBoolean()
  hasAttachments: boolean;

  @ApiProperty({
    description: 'Role of the message sender',
    enum: MessageRole,
    example: MessageRole.USER,
  })
  @IsEnum(MessageRole)
  role: MessageRole;

  @ApiPropertyOptional({
    description: 'Array of message attachments (images and files)',
    type: [ExternalMessageAttachmentDto],
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => ExternalMessageAttachmentDto)
  attachments?: ExternalMessageAttachmentDto[];

  @ApiPropertyOptional({
    description: 'ID of the message this is replying to',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID()
  replyMessageId?: string;

  @ApiPropertyOptional({
    description: 'Active run ID for streaming responses',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID()
  runId?: string;

  constructor(data: {
    messageId: string;
    messageText: string;
    messageCreatedAt: number;
    hasAttachments: boolean;
    role: MessageRole;
    attachments?: ExternalMessageAttachmentDto[];
    replyMessageId?: string;
    runId?: string;
  }) {
    this.messageId = data.messageId;
    this.messageText = data.messageText;
    this.messageCreatedAt = data.messageCreatedAt;
    this.hasAttachments = data.hasAttachments;
    this.role = data.role;
    this.attachments = data.attachments;
    this.replyMessageId = data.replyMessageId;
    this.runId = data.runId;
  }
}