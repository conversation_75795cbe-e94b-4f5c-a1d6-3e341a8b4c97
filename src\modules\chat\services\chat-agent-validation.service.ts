import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, IsNull } from 'typeorm';
import { AppException } from '@/common';
import { CHAT_ERROR_CODES } from '../exceptions';
import { Agent } from '../../agent/entities/agent.entity';
import { ModelFeature } from '../interfaces/system-agent-config.interface';

/**
 * Chat Agent Validation Service
 * Handles agent validation for chat functionality using only repositories
 * Does not depend on other module services
 */
@Injectable()
export class ChatAgentValidationService {
  private readonly logger = new Logger(ChatAgentValidationService.name);

  constructor(
    @InjectRepository(Agent)
    private readonly agentRepository: Repository<Agent>,
  ) {}

  /**
   * Validate that user owns the agent
   *
   * @param agentId - Agent ID to validate
   * @param userId - User ID for ownership check
   * @throws AppException if agent not found or not owned by user
   */
  async validateAgentOwnership(agentId: string, userId: number): Promise<void> {
    this.logger.debug('Validating agent ownership', { agentId, userId });

    const agent = await this.agentRepository.findOne({
      where: {
        id: agentId,
        userId: userId,
        deletedAt: IsNull(), // Only active agents
      },
    });

    if (!agent) {
      this.logger.warn('Agent not found or not owned by user', {
        agentId,
        userId,
      });
      throw new AppException(
        CHAT_ERROR_CODES.INVALID_INPUT,
        `Agent ${agentId} not found or not accessible`,
      );
    }

    this.logger.debug('Agent ownership validation successful', {
      agentId,
      agentName: agent.name,
      userId,
    });
  }

  /**
   * Validate agent capabilities for web search
   * Implements finetune-base selection logic like the worker
   *
   * @param agentId - Agent ID to validate
   * @param userId - User ID for ownership check
   * @throws AppException if agent doesn't support web search
   */
  async validateWebSearchCapability(
    agentId: string,
    userId: number,
  ): Promise<void> {
    this.logger.debug('Validating web search capability', { agentId, userId });

    // Get agent with model capabilities using double join
    const result = await this.agentRepository
      .createQueryBuilder('agent')
      .leftJoin('models', 'model', 'agent.modelId = model.id')
      .leftJoin(
        'model_registry',
        'registry',
        'model.modelRegistryId = registry.id',
      )
      .select([
        'agent.id',
        'agent.name',
        'agent.userId',
        'model.isFineTune as "isFineTune"',
        'registry.features_base as "featuresBase"',
        'registry.features_fine_tune as "featuresFineTune"',
      ])
      .where('agent.id = :agentId', { agentId })
      .andWhere('agent.userId = :userId', { userId })
      .andWhere('agent.deletedAt IS NULL')
      .getRawOne();

    if (!result) {
      this.logger.warn('Agent not found or not owned by user', {
        agentId,
        userId,
      });
      throw new AppException(
        CHAT_ERROR_CODES.INVALID_INPUT,
        `Agent ${agentId} not found or not accessible`,
      );
    }

    // Apply finetune-base selection logic like the worker
    const isFineTune = result.isFineTune;
    const features = isFineTune
      ? result.featuresFineTune || []
      : result.featuresBase || [];

    // Check if model supports tool calling (required for web search)
    const hasToolCall =
      features.includes(ModelFeature.TOOL_CALL) ||
      features.includes(ModelFeature.FORCED_TOOL_CALL);

    if (!hasToolCall) {
      this.logger.warn(
        'Agent model does not support tool calling for web search',
        {
          agentId,
          agentName: result.agent_name,
          isFineTune,
          features,
        },
      );
      throw new AppException(
        CHAT_ERROR_CODES.UNSUPPORTED_CONTENT_TYPE,
        `Agent's model does not support web search functionality`,
      );
    }

    this.logger.debug('Web search capability validation successful', {
      agentId,
      agentName: result.agent_name,
      isFineTune,
      features,
    });
  }

  /**
   * Validate agent capabilities for multi-agent processing
   *
   * @param agentId - Agent ID to validate
   * @param userId - User ID for ownership check
   * @throws AppException if agent doesn't support multi-agent
   */
  async validateMultiAgentCapability(
    agentId: string,
    userId: number,
  ): Promise<void> {
    this.logger.debug('Validating multi-agent capability', { agentId, userId });

    // First validate ownership
    await this.validateAgentOwnership(agentId, userId);

    // TODO: Add specific multi-agent capability validation
    // This could check:
    // 1. Agent type configuration (SUPERVISOR type)
    // 2. Subscription level
    // 3. Feature flags
    // For now, we'll allow all agents to use multi-agent

    this.logger.debug('Multi-agent capability validation successful', {
      agentId,
      userId,
    });
  }

  /**
   * Get agent basic information for validation
   *
   * @param agentId - Agent ID
   * @param userId - User ID for ownership check
   * @returns Agent information
   */
  async getAgentInfo(
    agentId: string,
    userId: number,
  ): Promise<{
    id: string;
    name: string;
    typeId?: number;
  }> {
    this.logger.debug('Getting agent info', { agentId, userId });

    const agent = await this.agentRepository.findOne({
      where: {
        id: agentId,
        userId: userId,
        deletedAt: IsNull(),
      },
      select: ['id', 'name', 'typeId'],
    });

    if (!agent) {
      throw new AppException(
        CHAT_ERROR_CODES.INVALID_INPUT,
        `Agent ${agentId} not found or not accessible`,
      );
    }

    return {
      id: agent.id,
      name: agent.name,
      typeId: agent.typeId || undefined,
    };
  }

  /**
   * Get agent with model capabilities using double join
   * Implements finetune-base selection logic like the worker
   *
   * @param agentId - Agent ID
   * @param userId - User ID for ownership check
   * @returns Agent with model capabilities
   */
  async getAgentWithModelCapabilities(
    agentId: string,
    userId: number,
  ): Promise<{
    agentId: string;
    agentName: string;
    features: string[];
    inputModalities: string[];
    outputModalities: string[];
  } | null> {
    this.logger.debug('Getting agent with model capabilities', {
      agentId,
      userId,
    });

    const result = await this.agentRepository
      .createQueryBuilder('agent')
      .leftJoin('models', 'model', 'agent.modelId = model.id')
      .leftJoin(
        'model_registry',
        'registry',
        'model.modelRegistryId = registry.id',
      )
      .select([
        'agent.id as "agentId"',
        'agent.name as "agentName"',
        'model.isFineTune as "isFineTune"',
        'registry.features_base as "featuresBase"',
        'registry.features_fine_tune as "featuresFineTune"',
        'registry.input_modalities_base as "inputModalitiesBase"',
        'registry.input_modalities_fine_tune as "inputModalitiesFineTune"',
        'registry.output_modalities_base as "outputModalitiesBase"',
        'registry.output_modalities_fine_tune as "outputModalitiesFineTune"',
      ])
      .where('agent.id = :agentId', { agentId })
      .andWhere('agent.userId = :userId', { userId })
      .andWhere('agent.deletedAt IS NULL')
      .getRawOne();

    if (!result) {
      this.logger.warn('Agent not found or not owned by user', {
        agentId,
        userId,
      });
      return null;
    }

    // Apply finetune-base selection logic like the worker
    const isFineTune = result.isFineTune;
    const features = isFineTune
      ? result.featuresFineTune || []
      : result.featuresBase || [];
    const inputModalities = isFineTune
      ? result.inputModalitiesFineTune || []
      : result.inputModalitiesBase || [];
    const outputModalities = isFineTune
      ? result.outputModalitiesFineTune || []
      : result.outputModalitiesBase || [];

    this.logger.debug('Selected capabilities based on finetune flag', {
      agentId: result.agentId,
      isFineTune,
      featuresCount: features.length,
      inputModalitiesCount: inputModalities.length,
      outputModalitiesCount: outputModalities.length,
    });

    return {
      agentId: result.agentId,
      agentName: result.agentName,
      features,
      inputModalities,
      outputModalities,
    };
  }
}
