[{"typeName": "facebook-page", "version": 1, "displayName": "Facebook Pages", "description": "Manage Facebook Pages posts, videos, photos, comments, and page information. Create, update, delete content and interact with page data.", "groupName": "integration", "icon": "facebook", "properties": [{"name": "operation", "displayName": "Operation", "type": "options", "required": true, "default": "listPosts", "description": "<PERSON><PERSON><PERSON> thao tác cần thực hiện", "options": [{"name": "Watch Posts", "value": "watchPosts"}, {"name": "Watch Posts (public page)", "value": "watchPostsPublic"}, {"name": "List Posts", "value": "listPosts"}, {"name": "Get a Post", "value": "getPost"}, {"name": "Get Post Reactions", "value": "getPostReactions"}, {"name": "Create a Post", "value": "createPost"}, {"name": "Create a Post with Photos", "value": "createPostWithPhotos"}, {"name": "Update a Post", "value": "updatePost"}, {"name": "Delete a Post", "value": "deletePost"}, {"name": "Like a Post", "value": "likePost"}, {"name": "Unlike a Post", "value": "unlikePost"}, {"name": "Watch Videos", "value": "watchVideos"}, {"name": "List Videos", "value": "listVideos"}, {"name": "Get a Video", "value": "getVideo"}, {"name": "Upload a Video", "value": "uploadVideo"}, {"name": "Update a Video", "value": "updateVideo"}, {"name": "Delete a Video", "value": "deleteVideo"}, {"name": "Watch Photos", "value": "watchPhotos"}, {"name": "List Photos", "value": "listPhotos"}, {"name": "Get a Photo", "value": "getPhoto"}, {"name": "Upload a Photo", "value": "uploadPhoto"}, {"name": "Delete a Photo", "value": "deletePhoto"}, {"name": "Watch Comments", "value": "watchComments"}, {"name": "List Comments", "value": "listComments"}, {"name": "Get a Comment", "value": "getComment"}, {"name": "Create a Comment", "value": "createComment"}, {"name": "Update a Comment", "value": "updateComment"}, {"name": "Delete a Comment", "value": "deleteComment"}, {"name": "Get a Page", "value": "getPage"}, {"name": "Update a Page", "value": "updatePage"}, {"name": "Publish a Reel", "value": "publishReel"}]}, {"name": "page_id", "displayName": "Page", "type": "string", "required": true, "description": "<PERSON><PERSON><PERSON> t<PERSON>", "displayOptions": {"show": {"operation": ["listPosts", "getPost", "getPostReactions", "createPost", "createPostWithPhotos", "updatePost", "deletePost", "likePost", "unlikePost", "listVideos", "getVideo", "uploadVideo", "updateVideo", "deleteVideo", "listPhotos", "getPhoto", "uploadPhoto", "deletePhoto", "listComments", "getComment", "createComment", "updateComment", "deleteComment", "getPage", "updatePage", "publishReel"]}}}, {"name": "post_id", "displayName": "Post ID", "type": "string", "required": true, "description": "ID của bài đăng", "displayOptions": {"show": {"operation": ["getPost", "getPostReactions", "updatePost", "deletePost", "likePost", "unlikePost", "listComments"]}}}, {"name": "message", "displayName": "Message", "type": "string", "description": "<PERSON><PERSON><PERSON> dung bài đăng", "displayOptions": {"show": {"operation": ["createPost", "updatePost"]}}}, {"name": "message_photos", "displayName": "Message", "type": "string", "description": "<PERSON>ội dung bài đăng với <PERSON>nh", "displayOptions": {"show": {"operation": ["createPostWithPhotos"]}}}, {"name": "link", "displayName": "Link", "type": "string", "description": "<PERSON><PERSON><PERSON> kết đ<PERSON> k<PERSON>m", "displayOptions": {"show": {"operation": ["createPost", "updatePost"]}}}, {"name": "published", "displayName": "Published", "type": "boolean", "default": true, "description": "<PERSON><PERSON><PERSON><PERSON> thái xu<PERSON> bản", "displayOptions": {"show": {"operation": ["createPost", "createPostWithPhotos", "updatePost"]}}}, {"name": "scheduled_publish_time", "displayName": "Scheduled Publish Time", "type": "dateTime", "description": "<PERSON><PERSON><PERSON><PERSON> gian xu<PERSON>t bản theo l<PERSON>ch", "displayOptions": {"show": {"operation": ["createPost", "createPostWithPhotos", "updatePost"]}}}, {"name": "privacy", "displayName": "Privacy", "type": "options", "description": "<PERSON><PERSON>i đặt quyền riêng tư", "options": [{"name": "Public", "value": "EVERYONE"}, {"name": "Friends", "value": "ALL_FRIENDS"}, {"name": "Only Me", "value": "SELF"}], "displayOptions": {"show": {"operation": ["createPost", "createPostWithPhotos", "updatePost"]}}}, {"name": "photos", "displayName": "Photos", "type": "array", "required": true, "description": "<PERSON><PERSON> s<PERSON><PERSON> cần đ<PERSON>ng", "default": {}, "properties": [{"name": "url", "displayName": "Photo URL", "type": "string", "required": true, "description": "URL của <PERSON>"}, {"name": "caption", "displayName": "Caption", "type": "string", "description": "<PERSON><PERSON> thích cho <PERSON>nh"}], "displayOptions": {"show": {"operation": ["createPostWithPhotos"]}}}, {"name": "type", "displayName": "Type", "type": "options", "required": true, "default": "uploaded", "description": "Loại video", "options": [{"name": "Uploaded", "value": "uploaded"}], "displayOptions": {"show": {"operation": ["listVideos"]}}}, {"name": "video_id", "displayName": "Video ID", "type": "string", "required": true, "description": "ID của video", "displayOptions": {"show": {"operation": ["getVideo", "updateVideo", "deleteVideo"]}}}, {"name": "title", "displayName": "Title", "type": "string", "description": "<PERSON>i<PERSON><PERSON> đề video", "displayOptions": {"show": {"operation": ["uploadVideo", "updateVideo", "publishReel"]}}}, {"name": "description", "displayName": "Description", "type": "string", "description": "<PERSON><PERSON> tả video", "displayOptions": {"show": {"operation": ["uploadVideo", "updateVideo", "publishReel"]}}}, {"name": "file_video", "displayName": "File", "type": "collection", "required": true, "description": "File video cần upload", "properties": [{"name": "filename", "displayName": "File Name", "type": "string", "required": true, "description": "Tên file video"}, {"name": "data", "displayName": "File Data", "type": "string", "required": true, "description": "Dữ liệu file video (base64 hoặc binary)"}], "displayOptions": {"show": {"operation": ["uploadVideo"]}}}, {"name": "photo_id", "displayName": "Photo ID", "type": "string", "required": true, "description": "<PERSON> c<PERSON><PERSON>", "displayOptions": {"show": {"operation": ["getPhoto", "deletePhoto"]}}}, {"name": "file_photo", "displayName": "File", "type": "collection", "required": true, "description": "File <PERSON><PERSON> cần upload", "properties": [{"name": "filename", "displayName": "File Name", "type": "string", "required": true, "description": "<PERSON><PERSON><PERSON> file <PERSON>nh"}, {"name": "data", "displayName": "File Data", "type": "string", "required": true, "description": "Dữ liệu file ảnh (base64 hoặc binary)"}], "displayOptions": {"show": {"operation": ["uploadPhoto"]}}}, {"name": "comment_id", "displayName": "Comment ID", "type": "string", "required": true, "description": "<PERSON> của comment", "displayOptions": {"show": {"operation": ["getComment", "updateComment", "deleteComment"]}}}, {"name": "message_comment", "displayName": "Message", "type": "string", "description": "<PERSON><PERSON><PERSON> dung comment", "displayOptions": {"show": {"operation": ["createComment", "updateComment"]}}}, {"name": "attachment_type", "displayName": "Attachment type", "type": "string", "description": "<PERSON><PERSON><PERSON> k<PERSON>", "displayOptions": {"show": {"operation": ["createComment"]}}}, {"name": "is_hidden", "displayName": "Is hidden", "type": "boolean", "description": "Ẩn comment hay không", "displayOptions": {"show": {"operation": ["updateComment"]}}}, {"name": "about", "displayName": "About", "type": "string", "description": "Th<PERSON>ng tin về trang", "displayOptions": {"show": {"operation": ["updatePage"]}}}, {"name": "bio", "displayName": "Bio", "type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> sử", "displayOptions": {"show": {"operation": ["updatePage"]}}}, {"name": "phone", "displayName": "Phone", "type": "string", "description": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "displayOptions": {"show": {"operation": ["updatePage"]}}}, {"name": "website", "displayName": "Website", "type": "string", "description": "Website", "displayOptions": {"show": {"operation": ["updatePage"]}}}, {"name": "upload_method", "displayName": "Upload method", "type": "string", "required": true, "description": "<PERSON><PERSON><PERSON><PERSON> thức upload", "displayOptions": {"show": {"operation": ["publishReel"]}}}, {"name": "limit", "displayName": "Limit", "type": "number", "default": 10, "minValue": 1, "maxValue": 100, "description": "Số lượng kết quả tối đa", "displayOptions": {"show": {"operation": ["listPosts", "getPostReactions", "listVideos", "listPhotos", "listComments"]}}}, {"name": "include_hidden", "displayName": "Include Hidden", "type": "boolean", "default": false, "description": "<PERSON><PERSON> g<PERSON>m bài đ<PERSON>ng <PERSON>n", "displayOptions": {"show": {"operation": ["listPosts"]}}}], "inputs": ["main"], "outputs": ["main"], "credentials": [{"provider": "facebook", "name": "facebookOAuth", "displayName": "Facebook Pages OAuth2", "description": "OAuth2 authentication for Facebook Pages", "required": true, "authType": "oauth2", "testable": true, "testUrl": "/api/integrations/test-connection"}]}]