/**
 * @file Facebook Ads Campaign Management Interfaces
 * 
 * Đ<PERSON><PERSON> nghĩa các interfaces cho Facebook Ads Campaign Management integration
 * Theo patterns từ Make.com chuẩn
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

import {
    IBaseIntegrationParameters,
    ITriggerParameters,
    IActionParameters,
    ISearchParameters,
    IBaseIntegrationInput,
    IBaseIntegrationOutput,
    EIntegrationOperationType,
    EIntegrationErrorHandling,
    IBaseIntegrationCredential
} from '../../base/base-integration.interface';

import {
    ECredentialName,
    ENodeAuthType,
    EPropertyType,
    ELoadOptionsResource,
    ELoadOptionsMethod,
    INodeProperty
} from '../../../node-manager.interface';

import {
    ITypedNodeExecution
} from '../../../execute.interface';

import {
    EFacebookCampaignManagementOperation,
    EFacebookCampaignObjective,
    EFacebookStatus,
    EFacebookOptimizationGoal,
    EFacebookBillingEvent,
    EFacebookSpecialAdCategory,
    EFacebookEffectiveStatus,
    EFacebookLocationType
} from './facebook-campaign-management.types';

// =================================================================
// SECTION 1: INPUT/OUTPUT INTERFACES
// =================================================================

/**
 * Facebook Campaign Management input interface
 */
export interface IFacebookCampaignManagementInput extends IBaseIntegrationInput {
    /** Operation type - Loại thao tác */
    operation: EFacebookCampaignManagementOperation;
    
    /** Parameters specific to the operation - Tham số cụ thể cho thao tác */
    parameters: IFacebookCampaignManagementParameters;
}

/**
 * Facebook Campaign Management output interface
 */
export interface IFacebookCampaignManagementOutput extends IBaseIntegrationOutput {
    /** Operation result data - Dữ liệu kết quả thao tác */
    data: {
        /** Campaigns data - Dữ liệu chiến dịch */
        campaigns?: any[];
        /** Ad sets data - Dữ liệu nhóm quảng cáo */
        adsets?: any[];
        /** Ads data - Dữ liệu quảng cáo */
        ads?: any[];
        /** Reach estimate data - Dữ liệu ước tính tiếp cận */
        reach_estimate?: any;
        /** Ad interests data - Dữ liệu sở thích quảng cáo */
        ad_interests?: any[];
        /** Locations data - Dữ liệu địa điểm */
        locations?: any[];
    };
}

// =================================================================
// SECTION 2: PARAMETER INTERFACES
// =================================================================

/**
 * List Campaigns parameters - Tham số liệt kê chiến dịch
 */
export interface IListCampaignsParameters extends ISearchParameters {
    operation: EFacebookCampaignManagementOperation.LIST_CAMPAIGNS;

    /** Business ID - ID doanh nghiệp */
    business_id: string;

    /** Maximum number of results to be worked with during one execution cycle - Số lượng kết quả tối đa */
    limit?: number;
}

/**
 * Update Campaign parameters - Tham số cập nhật chiến dịch
 */
export interface IUpdateCampaignParameters extends IActionParameters {
    operation: EFacebookCampaignManagementOperation.UPDATE_CAMPAIGN;

    /** Business ID - ID doanh nghiệp */
    business_id: string;

    /** Campaign name - Tên chiến dịch */
    name?: string;

    /** Daily budget multiplied by currency multiplier - Ngân sách hàng ngày nhân với hệ số tiền tệ */
    daily_budget?: number;

    /** Lifetime budget multiplied by currency multiplier - Ngân sách trọn đời nhân với hệ số tiền tệ */
    lifetime_budget?: number;

    /** Spend cap multiplied by currency multiplier - Giới hạn chi tiêu nhân với hệ số tiền tệ */
    spend_cap?: number;

    /** Campaign start time - Thời gian bắt đầu chiến dịch */
    start_time?: string;

    /** Campaign stop time - Thời gian kết thúc chiến dịch */
    stop_time?: string;

    /** Campaign status - Trạng thái chiến dịch */
    status?: EFacebookStatus;

    /** Bid strategy - Chiến lược đấu giá */
    bid_strategy?: string;

    /** Advanced settings - Cài đặt nâng cao */
    advanced_settings?: {
        /** Ad Set Bid Amounts - Số tiền đấu giá của Ad Set */
        ad_set_bid_amounts?: Array<{
            ad_set_id: string;
            bid_amount: number;
        }>;

        /** Ad Set Budgets - Ngân sách Ad Set */
        ad_set_budgets?: Array<{
            ad_set_id: string;
            budget: number;
        }>;

        /** Budget Rebalance Flag - Cờ cân bằng lại ngân sách */
        budget_rebalance_flag?: boolean;

        /** Campaign Optimization Type - Loại tối ưu hóa chiến dịch */
        campaign_optimization_type?: string;

        /** Is SKAdNetwork Attribution - Có phải là SKAdNetwork Attribution */
        is_skadnetwork_attribution?: boolean;

        /** Is Using L3 Schedule - Có sử dụng lịch trình L3 */
        is_using_l3_schedule?: boolean;

        /** Objective - Mục tiêu chiến dịch */
        objective?: EFacebookCampaignObjective;

        /** Smart Promotion Type - Loại khuyến mãi thông minh */
        smart_promotion_type?: string;

        /** Special Ad Categories - Danh mục quảng cáo đặc biệt */
        special_ad_categories?: EFacebookSpecialAdCategory[];

        /** Special Ad Category Country - Quốc gia danh mục quảng cáo đặc biệt */
        special_ad_category_country?: string[];
    };
}

/**
 * List Ad Sets parameters - Tham số liệt kê nhóm quảng cáo
 */
export interface IListAdSetsParameters extends ISearchParameters {
    operation: EFacebookCampaignManagementOperation.LIST_AD_SETS;

    /** Business ID - ID doanh nghiệp */
    business_id: string;

    /** Effective Status - Trạng thái hiệu lực */
    effective_status?: EFacebookEffectiveStatus[];

    /** Maximum number of results to be worked with during one execution cycle - Số lượng kết quả tối đa */
    limit?: number;
}

/**
 * Update Ad Set parameters - Tham số cập nhật nhóm quảng cáo
 */
export interface IUpdateAdSetParameters extends IActionParameters {
    operation: EFacebookCampaignManagementOperation.UPDATE_AD_SET;

    /** Business ID - ID doanh nghiệp */
    business_id: string;

    /** Ad set name - Tên nhóm quảng cáo */
    name?: string;

    /** Ad set status - Trạng thái nhóm quảng cáo */
    status?: EFacebookStatus;

    /** Ad Set Schedule - Lịch trình nhóm quảng cáo */
    ad_set_schedule?: Array<{
        day_of_week: number;
        start_minute: number;
        end_minute: number;
    }>;

    /** Attribution Spec - Thông số attribution */
    attribution_spec?: Array<{
        event_type: string;
        window_days: number;
    }>;

    /** Bid Amounts - Số tiền đấu giá (nhân với hệ số tiền tệ) */
    bid_amounts?: number;

    /** Bid Strategy - Chiến lược đấu giá */
    bid_strategy?: string;

    /** Billing Event - Sự kiện thanh toán */
    billing_event?: string;

    /** Daily Impressions - Lượt hiển thị hàng ngày (chỉ cho campaigns với fixed CPM) */
    daily_impressions?: number;

    /** Daily Budget - Ngân sách hàng ngày (nhân với hệ số tiền tệ) */
    daily_budget?: number;

    /** Daily Min Spend Target - Mục tiêu chi tiêu tối thiểu hàng ngày (nhân với hệ số tiền tệ) */
    daily_min_spend_target?: number;

    /** Daily Spend Cap - Giới hạn chi tiêu hàng ngày (nhân với hệ số tiền tệ) */
    daily_spend_cap?: number;

    /** Lifetime Budget - Ngân sách trọn đời (nhân với hệ số tiền tệ) */
    lifetime_budget?: number;

    /** Lifetime Impressions - Lượt hiển thị trọn đời (chỉ cho campaigns với fixed CPM) */
    lifetime_impressions?: number;

    /** Lifetime Min Spend Target - Mục tiêu chi tiêu tối thiểu trọn đời (nhân với hệ số tiền tệ) */
    lifetime_min_spend_target?: number;

    /** Lifetime Spend Cap - Giới hạn chi tiêu trọn đời (nhân với hệ số tiền tệ) */
    lifetime_spend_cap?: number;

    /** Start Time - Thời gian bắt đầu (Time zone: Asia/Bangkok) */
    start_time?: string;

    /** End Time - Thời gian kết thúc (Time zone: Asia/Bangkok) */
    end_time?: string;
}

/**
 * List Ads parameters - Tham số liệt kê quảng cáo
 */
export interface IListAdsParameters extends ISearchParameters {
    operation: EFacebookCampaignManagementOperation.LIST_ADS;

    /** Business ID - ID doanh nghiệp */
    business_id: string;

    /** Effective Status - Trạng thái hiệu lực */
    effective_status?: EFacebookEffectiveStatus[];

    /** Maximum number of results to be worked with during one execution cycle - Số lượng kết quả tối đa */
    limit?: number;
}

/**
 * Update Ad parameters - Tham số cập nhật quảng cáo
 */
export interface IUpdateAdParameters extends IActionParameters {
    operation: EFacebookCampaignManagementOperation.UPDATE_AD;

    /** Business ID - ID doanh nghiệp */
    business_id: string;

    /** Ad name - Tên quảng cáo */
    name?: string;

    /** Ad status - Trạng thái quảng cáo */
    status?: EFacebookStatus;

    /** Bid Amount - Số tiền đấu giá (nhân với hệ số tiền tệ) */
    bid_amount?: number;
}

/**
 * Get Reach Estimate parameters - Tham số lấy ước tính tiếp cận
 */
export interface IGetReachEstimateParameters extends ISearchParameters {
    operation: EFacebookCampaignManagementOperation.GET_REACH_ESTIMATE;

    /** Business ID - ID doanh nghiệp */
    business_id: string;

    /** Advanced settings - Cài đặt nâng cao (không bắt buộc) */
    advanced_settings?: {
        /** Object Store URL - URL của ứng dụng trong app store (dùng cho mobile app campaign) */
        object_store_url?: string;
    };
}

/**
 * Search Ad Interests parameters - Tham số tìm kiếm sở thích quảng cáo
 */
export interface ISearchAdInterestsParameters extends ISearchParameters {
    operation: EFacebookCampaignManagementOperation.SEARCH_AD_INTERESTS;

    /** Name - Tên sở thích để tìm kiếm */
    name: string;

    /** Locale - Ngôn ngữ/vùng miền (ví dụ: en_US) */
    locale?: string;

    /** Maximum number of results to be worked with during one execution cycle - Số lượng kết quả tối đa */
    limit?: number;
}

/**
 * Search Locations parameters - Tham số tìm kiếm địa điểm
 */
export interface ISearchLocationsParameters extends ISearchParameters {
    operation: EFacebookCampaignManagementOperation.SEARCH_LOCATIONS;

    /** Location Types - Loại địa điểm (bắt buộc) */
    location_types: EFacebookLocationType[];

    /** Query - Từ khóa tìm kiếm địa điểm */
    query?: string;

    /** Maximum number of results to be worked with during one execution cycle - Số lượng kết quả tối đa */
    limit?: number;
}

/**
 * Union type cho tất cả Facebook Campaign Management parameters
 */
export type IFacebookCampaignManagementParameters = 
    | IListCampaignsParameters
    | IUpdateCampaignParameters
    | IListAdSetsParameters
    | IUpdateAdSetParameters
    | IListAdsParameters
    | IUpdateAdParameters
    | IGetReachEstimateParameters
    | ISearchAdInterestsParameters
    | ISearchLocationsParameters;
