/**
 * @file Facebook Ads Campaign Management Node Properties
 * 
 * Đ<PERSON><PERSON> nghĩa node properties cho Facebook Ads Campaign Management integration
 * Theo patterns từ Make.com chuẩn
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

import {
    EPropertyType,
    INodeProperty
} from '../../../node-manager.interface';

import {
    EFacebookCampaignManagementOperation,
    EFacebookCampaignObjective,
    EFacebookStatus,
    EFacebookSpecialAdCategory,
    EFacebookEffectiveStatus,
    EFacebookLocationType
} from './facebook-campaign-management.types';

// =================================================================
// FACEBOOK CAMPAIGN MANAGEMENT NODE PROPERTIES
// =================================================================

/**
 * Facebook Campaign Management node properties definition
 */
export const FACEBOOK_CAMPAIGN_MANAGEMENT_PROPERTIES: INodeProperty[] = [
    // Operation Selection
    {
        name: 'operation',
        displayName: 'Operation',
        type: EPropertyType.Options,
        required: true,
        default: EFacebookCampaignManagementOperation.LIST_CAMPAIGNS,
        description: 'Chọn thao tác cần thực hiện',
        options: [
            // Campaign Operations - Các thao tác chiến dịch
            { name: 'List Campaigns', value: EFacebookCampaignManagementOperation.LIST_CAMPAIGNS },
            { name: 'Update a Campaign', value: EFacebookCampaignManagementOperation.UPDATE_CAMPAIGN },
            
            // Ad Set Operations - Các thao tác nhóm quảng cáo
            { name: 'List Ad Sets', value: EFacebookCampaignManagementOperation.LIST_AD_SETS },
            { name: 'Update an Ad Set', value: EFacebookCampaignManagementOperation.UPDATE_AD_SET },
            
            // Ad Operations - Các thao tác quảng cáo
            { name: 'List Ads', value: EFacebookCampaignManagementOperation.LIST_ADS },
            { name: 'Update an Ad', value: EFacebookCampaignManagementOperation.UPDATE_AD },
            
            // Estimate Operations - Các thao tác ước tính
            { name: 'Get a Reach Estimate', value: EFacebookCampaignManagementOperation.GET_REACH_ESTIMATE },
            { name: 'Search Ad Interests', value: EFacebookCampaignManagementOperation.SEARCH_AD_INTERESTS },
            
            // Other Operations - Các thao tác khác
            { name: 'Search Locations', value: EFacebookCampaignManagementOperation.SEARCH_LOCATIONS }
        ]
    },

    // Business ID (Required for most operations)
    {
        name: 'business_id',
        displayName: 'Business ID',
        type: EPropertyType.String,
        required: true,
        displayOptions: {
            show: {
                operation: [
                    EFacebookCampaignManagementOperation.LIST_CAMPAIGNS,
                    EFacebookCampaignManagementOperation.UPDATE_CAMPAIGN,
                    EFacebookCampaignManagementOperation.LIST_AD_SETS,
                    EFacebookCampaignManagementOperation.UPDATE_AD_SET,
                    EFacebookCampaignManagementOperation.LIST_ADS,
                    EFacebookCampaignManagementOperation.UPDATE_AD,
                    EFacebookCampaignManagementOperation.GET_REACH_ESTIMATE
                ]
            }
        }
    },

    // Name field (for Search Ad Interests and Update operations)
    {
        name: 'name',
        displayName: 'Name',
        type: EPropertyType.String,
        required: true,
        description: 'Tên sở thích để tìm kiếm',
        displayOptions: {
            show: {
                operation: [
                    EFacebookCampaignManagementOperation.SEARCH_AD_INTERESTS
                ]
            }
        }
    },

    // Name field for Update operations
    {
        name: 'name_update',
        displayName: 'Name',
        type: EPropertyType.String,
        description: 'Tên chiến dịch/ad set/ad',
        displayOptions: {
            show: {
                operation: [
                    EFacebookCampaignManagementOperation.UPDATE_CAMPAIGN,
                    EFacebookCampaignManagementOperation.UPDATE_AD_SET,
                    EFacebookCampaignManagementOperation.UPDATE_AD
                ]
            }
        }
    },

    // Location Types (Required for Search Locations)
    {
        name: 'location_types',
        displayName: 'Location Types',
        type: EPropertyType.Options,
        required: true,
        multipleValues: true,
        description: 'Loại địa điểm cần tìm kiếm',
        options: [
            { name: 'Country', value: EFacebookLocationType.COUNTRY },
            { name: 'Region', value: EFacebookLocationType.REGION },
            { name: 'City', value: EFacebookLocationType.CITY },
            { name: 'Zip', value: EFacebookLocationType.ZIP },
            { name: 'DMA', value: EFacebookLocationType.DMA },
            { name: 'Electoral District', value: EFacebookLocationType.ELECTORAL_DISTRICT },
            { name: 'Country Group', value: EFacebookLocationType.COUNTRY_GROUP }
        ],
        displayOptions: {
            show: {
                operation: [
                    EFacebookCampaignManagementOperation.SEARCH_LOCATIONS
                ]
            }
        }
    },

    // Query field (for Search Locations)
    {
        name: 'query',
        displayName: 'Query',
        type: EPropertyType.String,
        description: 'Từ khóa tìm kiếm địa điểm',
        displayOptions: {
            show: {
                operation: [
                    EFacebookCampaignManagementOperation.SEARCH_LOCATIONS
                ]
            }
        }
    },

    // Locale field (for Search Ad Interests)
    {
        name: 'locale',
        displayName: 'Locale',
        type: EPropertyType.String,
        description: 'Ngôn ngữ/vùng miền. Ví dụ: en_US',
        displayOptions: {
            show: {
                operation: [
                    EFacebookCampaignManagementOperation.SEARCH_AD_INTERESTS
                ]
            }
        }
    },

    // Effective Status (for List operations)
    {
        name: 'effective_status',
        displayName: 'Effective Status',
        type: EPropertyType.Options,
        description: 'Trạng thái hiệu lực của ad sets/ads',
        multipleValues: true,
        options: [
            { name: 'Active', value: EFacebookEffectiveStatus.ACTIVE },
            { name: 'Paused', value: EFacebookEffectiveStatus.PAUSED },
            { name: 'Pending Review', value: EFacebookEffectiveStatus.PENDING_REVIEW },
            { name: 'Disapproved', value: EFacebookEffectiveStatus.DISAPPROVED },
            { name: 'Preapproved', value: EFacebookEffectiveStatus.PREAPPROVED },
            { name: 'Pending Billing Info', value: EFacebookEffectiveStatus.PENDING_BILLING_INFO },
            { name: 'Campaign Paused', value: EFacebookEffectiveStatus.CAMPAIGN_PAUSED },
            { name: 'Archived', value: EFacebookEffectiveStatus.ARCHIVED },
            { name: 'Adset Paused', value: EFacebookEffectiveStatus.ADSET_PAUSED },
            { name: 'In Process', value: EFacebookEffectiveStatus.IN_PROCESS },
            { name: 'With Issues', value: EFacebookEffectiveStatus.WITH_ISSUES }
        ],
        displayOptions: {
            show: {
                operation: [
                    EFacebookCampaignManagementOperation.LIST_AD_SETS,
                    EFacebookCampaignManagementOperation.LIST_ADS
                ]
            }
        }
    },

    // Status field (for Update operations)
    {
        name: 'status',
        displayName: 'Status',
        type: EPropertyType.Options,
        description: 'Trạng thái chiến dịch/ad set/ad',
        options: [
            { name: 'Active', value: EFacebookStatus.ACTIVE },
            { name: 'Paused', value: EFacebookStatus.PAUSED },
            { name: 'Deleted', value: EFacebookStatus.DELETED },
            { name: 'Archived', value: EFacebookStatus.ARCHIVED }
        ],
        displayOptions: {
            show: {
                operation: [
                    EFacebookCampaignManagementOperation.UPDATE_CAMPAIGN,
                    EFacebookCampaignManagementOperation.UPDATE_AD_SET,
                    EFacebookCampaignManagementOperation.UPDATE_AD
                ]
            }
        }
    },

    // Limit fields (separate for each operation)
    {
        name: 'limit_list_campaigns',
        displayName: 'Limit',
        type: EPropertyType.Number,
        default: 10,
        minValue: 1,
        maxValue: 100,
        description: 'The maximum number of results to be worked with during one execution cycle',
        displayOptions: {
            show: {
                operation: [
                    EFacebookCampaignManagementOperation.LIST_CAMPAIGNS
                ]
            }
        }
    },

    {
        name: 'limit_list_ad_sets',
        displayName: 'Limit',
        type: EPropertyType.Number,
        default: 10,
        minValue: 1,
        maxValue: 100,
        description: 'The maximum number of results to be worked with during one execution cycle',
        displayOptions: {
            show: {
                operation: [
                    EFacebookCampaignManagementOperation.LIST_AD_SETS
                ]
            }
        }
    },

    {
        name: 'limit_list_ads',
        displayName: 'Limit',
        type: EPropertyType.Number,
        default: 10,
        minValue: 1,
        maxValue: 100,
        description: 'The maximum number of results to be worked with during one execution cycle',
        displayOptions: {
            show: {
                operation: [
                    EFacebookCampaignManagementOperation.LIST_ADS
                ]
            }
        }
    },

    {
        name: 'limit_search_ad_interests',
        displayName: 'Limit',
        type: EPropertyType.Number,
        default: 10,
        minValue: 1,
        maxValue: 100,
        description: 'The maximum number of results to be worked with during one execution cycle',
        displayOptions: {
            show: {
                operation: [
                    EFacebookCampaignManagementOperation.SEARCH_AD_INTERESTS
                ]
            }
        }
    },

    {
        name: 'limit_search_locations',
        displayName: 'Limit',
        type: EPropertyType.Number,
        default: 10,
        minValue: 1,
        maxValue: 100,
        description: 'The maximum number of results to be worked with during one execution cycle',
        displayOptions: {
            show: {
                operation: [
                    EFacebookCampaignManagementOperation.SEARCH_LOCATIONS
                ]
            }
        }
    },

    // Budget fields for Update Campaign
    {
        name: 'daily_budget',
        displayName: 'Daily Budget',
        type: EPropertyType.Number,
        description: 'Ngân sách hàng ngày nhân với hệ số tiền tệ. Ví dụ: nhập "10000" cho ngân sách 100 USD',
        displayOptions: {
            show: {
                operation: [
                    EFacebookCampaignManagementOperation.UPDATE_CAMPAIGN
                ]
            }
        }
    },

    {
        name: 'lifetime_budget',
        displayName: 'Lifetime Budget',
        type: EPropertyType.Number,
        description: 'Ngân sách trọn đời nhân với hệ số tiền tệ. Ví dụ: nhập "10000" cho ngân sách 100 USD',
        displayOptions: {
            show: {
                operation: [
                    EFacebookCampaignManagementOperation.UPDATE_CAMPAIGN
                ]
            }
        }
    },

    {
        name: 'spend_cap',
        displayName: 'Spend Cap',
        type: EPropertyType.Number,
        description: 'Giới hạn chi tiêu nhân với hệ số tiền tệ. Ví dụ: nhập "10000" cho giới hạn 100 USD',
        displayOptions: {
            show: {
                operation: [
                    EFacebookCampaignManagementOperation.UPDATE_CAMPAIGN
                ]
            }
        }
    },

    // Time fields for Update Campaign
    {
        name: 'start_time',
        displayName: 'Start Time',
        type: EPropertyType.DateTime,
        description: 'Thời gian bắt đầu chiến dịch (Time zone: Asia/Bangkok)',
        displayOptions: {
            show: {
                operation: [
                    EFacebookCampaignManagementOperation.UPDATE_CAMPAIGN
                ]
            }
        }
    },

    {
        name: 'stop_time',
        displayName: 'Stop Time',
        type: EPropertyType.DateTime,
        description: 'Thời gian kết thúc chiến dịch (Time zone: Asia/Bangkok)',
        displayOptions: {
            show: {
                operation: [
                    EFacebookCampaignManagementOperation.UPDATE_CAMPAIGN
                ]
            }
        }
    },

    // Bid fields
    {
        name: 'bid_strategy',
        displayName: 'Bid Strategy',
        type: EPropertyType.String,
        description: 'Chiến lược đấu giá',
        displayOptions: {
            show: {
                operation: [
                    EFacebookCampaignManagementOperation.UPDATE_CAMPAIGN
                ]
            }
        }
    },

    {
        name: 'bid_amount_ad',
        displayName: 'Bid Amount',
        type: EPropertyType.Number,
        description: 'Số tiền đấu giá nhân với hệ số tiền tệ. Ví dụ: nhập "1" cho 0.01 USD hoặc 1 JPY',
        displayOptions: {
            show: {
                operation: [
                    EFacebookCampaignManagementOperation.UPDATE_AD
                ]
            }
        }
    },

    // Advanced settings for Get Reach Estimate
    {
        name: 'object_store_url',
        displayName: 'Object Store URL',
        type: EPropertyType.String,
        description: 'URL của ứng dụng trong app store (dùng cho mobile app campaign)',
        displayOptions: {
            show: {
                operation: [
                    EFacebookCampaignManagementOperation.GET_REACH_ESTIMATE
                ]
            }
        }
    }
];
