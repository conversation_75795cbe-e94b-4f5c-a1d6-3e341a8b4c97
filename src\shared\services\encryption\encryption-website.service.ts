import { ConfigService, ConfigType, LivechatConfig } from '@/config';
import { Injectable, Logger } from '@nestjs/common';
import * as crypto from 'crypto';
import { PayloadLiveChatKey } from '../../../modules/integration/interfaces/website.interface';

/**
 * Service xử lý mã hóa/giải mã dữ liệu nhạy cảm cho website và live chat
 * Sử dụng AES-256-GCM để mã hóa thông tin nhạy cảm
 */
@Injectable()
export class EncryptionWebsiteService {
  private readonly logger = new Logger(EncryptionWebsiteService.name);
  private readonly algorithm = 'aes-256-gcm';
  private readonly secretKey: Buffer;

  constructor(private readonly configService: ConfigService) {
    // Lấy secret key từ biến môi trường
    const livechatConfig = this.configService.getConfig<LivechatConfig>(ConfigType.Livechat);
    const secretKey = livechatConfig?.encryptionKey;

    if (!secretKey) {
      this.logger.error('Livechat encryption key không được định nghĩa trong config');
      throw new Error('Livechat encryption key không được định nghĩa');
    }

    // Tạo key 32 bytes (256 bits) từ secret key bằng SHA-256
    this.secretKey = crypto
      .createHash('sha256')
      .update(String(secretKey))
      .digest();
  }

  /**
   * Mã hóa payload live chat key
   * @param payload PayloadLiveChatKey object
   * @returns Chuỗi đã mã hóa (base64)
   */
  encryptLiveChatPayload(payload: PayloadLiveChatKey): string {
    try {
      this.logger.debug(`Encrypting live chat payload for website ${payload.websiteId}, user ${payload.userId}`);

      // Chuyển đổi payload thành JSON string
      const text = JSON.stringify(payload);

      // Tạo IV (Initialization Vector) ngẫu nhiên
      const iv = crypto.randomBytes(16);

      // Tạo cipher
      const cipher = crypto.createCipheriv(this.algorithm, this.secretKey, iv);
      cipher.setAAD(Buffer.from('livechat-payload', 'utf8'));

      // Mã hóa dữ liệu
      let encrypted = cipher.update(text, 'utf8', 'hex');
      encrypted += cipher.final('hex');

      // Lấy authentication tag
      const authTag = cipher.getAuthTag();

      // Kết hợp IV, authTag và encrypted data
      const result = {
        iv: iv.toString('hex'),
        authTag: authTag.toString('hex'),
        encrypted: encrypted
      };

      // Trả về dưới dạng base64
      const encryptedPayload = Buffer.from(JSON.stringify(result)).toString('base64');

      this.logger.debug(`Successfully encrypted live chat payload`);
      return encryptedPayload;
    } catch (error) {
      this.logger.error(`Error encrypting live chat payload: ${error.message}`, error.stack);
      throw new Error('Failed to encrypt live chat payload');
    }
  }

  /**
   * Giải mã payload live chat key
   * @param encryptedPayload Chuỗi đã mã hóa (base64)
   * @returns PayloadLiveChatKey object
   */
  decryptLiveChatPayload(encryptedPayload: string): PayloadLiveChatKey {
    try {
      this.logger.debug(`Decrypting live chat payload`);

      // Giải mã base64
      const dataStr = Buffer.from(encryptedPayload, 'base64').toString('utf8');
      const data = JSON.parse(dataStr);

      // Lấy các thành phần
      const iv = Buffer.from(data.iv, 'hex');
      const authTag = Buffer.from(data.authTag, 'hex');
      const encrypted = data.encrypted;

      // Tạo decipher
      const decipher = crypto.createDecipheriv(this.algorithm, this.secretKey, iv);
      decipher.setAAD(Buffer.from('livechat-payload', 'utf8'));
      decipher.setAuthTag(authTag);

      // Giải mã
      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      // Parse JSON và validate structure
      const payload = JSON.parse(decrypted) as PayloadLiveChatKey;

      // Validate payload structure
      if (!payload.websiteId || !payload.userId) {
        throw new Error('Invalid payload structure');
      }

      this.logger.debug(`Successfully decrypted live chat payload for website ${payload.websiteId}, user ${payload.userId}`);
      return payload;
    } catch (error) {
      this.logger.error(`Error decrypting live chat payload: ${error.message}`, error.stack);
      throw new Error('Failed to decrypt live chat payload');
    }
  }
}
