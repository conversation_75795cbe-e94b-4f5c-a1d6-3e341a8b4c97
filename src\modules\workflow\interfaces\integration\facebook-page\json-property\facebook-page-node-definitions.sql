-- Facebook Pages Node Definition SQL Insert Script
-- This script inserts the Facebook Pages integration module into the node_definitions table

-- Insert Facebook Pages Node Definition
INSERT INTO node_definitions (
    type_name,
    version,
    display_name,
    description,
    group_name,
    icon,
    properties,
    inputs,
    outputs,
    credentials,
    created_at,
    updated_at
) VALUES (
    'facebook-page',
    1,
    'Facebook Pages',
    'Manage Facebook Pages posts, videos, photos, comments, and page information. Create, update, delete content and interact with page data.',
    'integration',
    'facebook',
    '[
        {
            "name": "operation",
            "displayName": "Operation",
            "type": "options",
            "required": true,
            "default": "listPosts",
            "description": "<PERSON><PERSON>n thao tác cần thực hiện",
            "options": [
                { "name": "Watch Posts", "value": "watchPosts" },
                { "name": "Watch Posts (public page)", "value": "watchPostsPublic" },
                { "name": "List Posts", "value": "listPosts" },
                { "name": "Get a Post", "value": "getPost" },
                { "name": "Get Post Reactions", "value": "getPostReactions" },
                { "name": "Create a Post", "value": "createPost" },
                { "name": "Create a Post with Photos", "value": "createPostWithPhotos" },
                { "name": "Update a Post", "value": "updatePost" },
                { "name": "Delete a Post", "value": "deletePost" },
                { "name": "Like a Post", "value": "likePost" },
                { "name": "Unlike a Post", "value": "unlikePost" },
                { "name": "Watch Videos", "value": "watchVideos" },
                { "name": "List Videos", "value": "listVideos" },
                { "name": "Get a Video", "value": "getVideo" },
                { "name": "Upload a Video", "value": "uploadVideo" },
                { "name": "Update a Video", "value": "updateVideo" },
                { "name": "Delete a Video", "value": "deleteVideo" },
                { "name": "Watch Photos", "value": "watchPhotos" },
                { "name": "List Photos", "value": "listPhotos" },
                { "name": "Get a Photo", "value": "getPhoto" },
                { "name": "Upload a Photo", "value": "uploadPhoto" },
                { "name": "Delete a Photo", "value": "deletePhoto" },
                { "name": "Watch Comments", "value": "watchComments" },
                { "name": "List Comments", "value": "listComments" },
                { "name": "Get a Comment", "value": "getComment" },
                { "name": "Create a Comment", "value": "createComment" },
                { "name": "Update a Comment", "value": "updateComment" },
                { "name": "Delete a Comment", "value": "deleteComment" },
                { "name": "Get a Page", "value": "getPage" },
                { "name": "Update a Page", "value": "updatePage" },
                { "name": "Publish a Reel", "value": "publishReel" }
            ]
        },
        {
            "name": "page_id",
            "displayName": "Page",
            "type": "string",
            "required": true,
            "description": "Chọn trang Facebook",
            "displayOptions": {
                "show": {
                    "operation": [
                        "listPosts", "getPost", "getPostReactions", "createPost", "createPostWithPhotos", 
                        "updatePost", "deletePost", "likePost", "unlikePost", "listVideos", "getVideo", 
                        "uploadVideo", "updateVideo", "deleteVideo", "listPhotos", "getPhoto", 
                        "uploadPhoto", "deletePhoto", "listComments", "getComment", "createComment", 
                        "updateComment", "deleteComment", "getPage", "updatePage", "publishReel"
                    ]
                }
            }
        },
        {
            "name": "post_id",
            "displayName": "Post ID",
            "type": "string",
            "required": true,
            "description": "ID của bài đăng",
            "displayOptions": {
                "show": {
                    "operation": ["getPost", "getPostReactions", "updatePost", "deletePost", "likePost", "unlikePost", "listComments"]
                }
            }
        },
        {
            "name": "message",
            "displayName": "Message",
            "type": "string",
            "description": "Nội dung bài đăng",
            "displayOptions": {
                "show": {
                    "operation": ["createPost", "updatePost"]
                }
            }
        },
        {
            "name": "message_photos",
            "displayName": "Message",
            "type": "string",
            "description": "Nội dung bài đăng với ảnh",
            "displayOptions": {
                "show": {
                    "operation": ["createPostWithPhotos"]
                }
            }
        },
        {
            "name": "link",
            "displayName": "Link",
            "type": "string",
            "description": "Liên kết đính kèm",
            "displayOptions": {
                "show": {
                    "operation": ["createPost", "updatePost"]
                }
            }
        },
        {
            "name": "published",
            "displayName": "Published",
            "type": "boolean",
            "default": true,
            "description": "Trạng thái xuất bản",
            "displayOptions": {
                "show": {
                    "operation": ["createPost", "createPostWithPhotos", "updatePost"]
                }
            }
        },
        {
            "name": "scheduled_publish_time",
            "displayName": "Scheduled Publish Time",
            "type": "dateTime",
            "description": "Thời gian xuất bản theo lịch",
            "displayOptions": {
                "show": {
                    "operation": ["createPost", "createPostWithPhotos", "updatePost"]
                }
            }
        },
        {
            "name": "privacy",
            "displayName": "Privacy",
            "type": "options",
            "description": "Cài đặt quyền riêng tư",
            "options": [
                { "name": "Public", "value": "EVERYONE" },
                { "name": "Friends", "value": "ALL_FRIENDS" },
                { "name": "Only Me", "value": "SELF" }
            ],
            "displayOptions": {
                "show": {
                    "operation": ["createPost", "createPostWithPhotos", "updatePost"]
                }
            }
        },
        {
            "name": "photos",
            "displayName": "Photos",
            "type": "array",
            "required": true,
            "description": "Danh sách ảnh cần đăng",
            "default": {},
            "properties": [
                {
                    "name": "url",
                    "displayName": "Photo URL",
                    "type": "string",
                    "required": true,
                    "description": "URL của ảnh"
                },
                {
                    "name": "caption",
                    "displayName": "Caption",
                    "type": "string",
                    "description": "Chú thích cho ảnh"
                }
            ],
            "displayOptions": {
                "show": {
                    "operation": ["createPostWithPhotos"]
                }
            }
        },
        {
            "name": "limit",
            "displayName": "Limit",
            "type": "number",
            "default": 10,
            "minValue": 1,
            "maxValue": 100,
            "description": "Số lượng kết quả tối đa",
            "displayOptions": {
                "show": {
                    "operation": ["listPosts", "getPostReactions", "listVideos", "listPhotos", "listComments"]
                }
            }
        },
        {
            "name": "include_hidden",
            "displayName": "Include Hidden",
            "type": "boolean",
            "default": false,
            "description": "Bao gồm bài đăng ẩn",
            "displayOptions": {
                "show": {
                    "operation": ["listPosts"]
                }
            }
        },
        {
            "name": "video_id",
            "displayName": "Video ID",
            "type": "string",
            "required": true,
            "description": "ID của video",
            "displayOptions": {
                "show": {
                    "operation": ["getVideo", "updateVideo", "deleteVideo"]
                }
            }
        },
        {
            "name": "title",
            "displayName": "Title",
            "type": "string",
            "description": "Tiêu đề video",
            "displayOptions": {
                "show": {
                    "operation": ["uploadVideo", "updateVideo", "publishReel"]
                }
            }
        },
        {
            "name": "description",
            "displayName": "Description",
            "type": "string",
            "description": "Mô tả video",
            "displayOptions": {
                "show": {
                    "operation": ["uploadVideo", "updateVideo", "publishReel"]
                }
            }
        },
        {
            "name": "photo_id",
            "displayName": "Photo ID",
            "type": "string",
            "required": true,
            "description": "ID của ảnh",
            "displayOptions": {
                "show": {
                    "operation": ["getPhoto", "deletePhoto"]
                }
            }
        },
        {
            "name": "comment_id",
            "displayName": "Comment ID",
            "type": "string",
            "required": true,
            "description": "ID của comment",
            "displayOptions": {
                "show": {
                    "operation": ["getComment", "updateComment", "deleteComment"]
                }
            }
        },
        {
            "name": "message_comment",
            "displayName": "Message",
            "type": "string",
            "description": "Nội dung comment",
            "displayOptions": {
                "show": {
                    "operation": ["createComment", "updateComment"]
                }
            }
        },
        {
            "name": "about",
            "displayName": "About",
            "type": "string",
            "description": "Thông tin về trang",
            "displayOptions": {
                "show": {
                    "operation": ["updatePage"]
                }
            }
        },
        {
            "name": "phone",
            "displayName": "Phone",
            "type": "string",
            "description": "Số điện thoại",
            "displayOptions": {
                "show": {
                    "operation": ["updatePage"]
                }
            }
        },
        {
            "name": "website",
            "displayName": "Website",
            "type": "string",
            "description": "Website",
            "displayOptions": {
                "show": {
                    "operation": ["updatePage"]
                }
            }
        }
    ]'::jsonb,
    '["main"]'::jsonb,
    '["main"]'::jsonb,
    '[
        {
            "provider": "facebook",
            "name": "facebookOAuth",
            "displayName": "Facebook Pages OAuth2",
            "description": "OAuth2 authentication for Facebook Pages",
            "required": true,
            "authType": "oauth2",
            "testable": true,
            "testUrl": "/api/integrations/test-connection"
        }
    ]'::jsonb,
    EXTRACT(epoch FROM now()) * 1000,
    EXTRACT(epoch FROM now()) * 1000
);

-- Verify the insertion
SELECT 
    type_name,
    version,
    display_name,
    group_name,
    icon,
    created_at
FROM node_definitions 
WHERE type_name = 'facebook-page'
ORDER BY type_name;
