import { Injectable, CanActivate, ExecutionContext, Logger } from '@nestjs/common';
import { AppException, ErrorCode } from '@/common';
import { Request } from 'express';
import { EncryptionWebsiteService } from '@shared/services/encryption/encryption-website.service';
import { PayloadLiveChatKey } from '@modules/integration/interfaces/website.interface';

/**
 * Website Key Guard
 * 
 * Authenticates website chat requests using encrypted website keys instead of JWT tokens.
 * Validates and decrypts keys from the 'x-website-key' header.
 * 
 * Key format: "redai_<encrypted_payload>"
 * Decrypted payload: {websiteId: string, userId: number}
 */
@Injectable()
export class WebsiteKeyGuard implements CanActivate {
  private readonly logger = new Logger(WebsiteKeyGuard.name);

  constructor(
    private readonly encryptionWebsiteService: EncryptionWebsiteService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();
    
    try {
      // Extract website key from header
      const websiteKey = this.extractWebsiteKeyFromHeader(request);
      
      // Remove 'redai_' prefix
      const encryptedPayload = this.stripRedaiPrefix(websiteKey);
      
      // Decrypt and validate payload
      const payload = await this.decryptAndValidatePayload(encryptedPayload);
      
      // Attach payload to request for controllers to use
      request['websitePayload'] = payload;
      
      this.logger.debug('Website key authentication successful', {
        websiteId: payload.websiteId,
        userId: payload.userId,
      });
      
      return true;
    } catch (error) {
      this.logger.warn('Website key authentication failed', {
        error: error.message,
        headers: {
          'x-website-key': request.headers['x-website-key'] ? '[REDACTED]' : 'missing',
        },
      });
      
      throw new AppException(
        ErrorCode.FORBIDDEN,
        'Website authentication failed',
      );
    }
  }

  /**
   * Extract website key from request headers
   */
  private extractWebsiteKeyFromHeader(request: Request): string {
    const websiteKey = request.headers['x-website-key'] as string;
    
    if (!websiteKey) {
      throw new Error('Missing x-website-key header');
    }
    
    if (typeof websiteKey !== 'string' || websiteKey.trim().length === 0) {
      throw new Error('Invalid x-website-key header format');
    }
    
    return websiteKey.trim();
  }

  /**
   * Remove 'redai_' prefix from website key
   */
  private stripRedaiPrefix(websiteKey: string): string {
    const PREFIX = 'redai_';
    
    if (!websiteKey.startsWith(PREFIX)) {
      throw new Error('Website key must start with "redai_" prefix');
    }
    
    const encryptedPayload = websiteKey.substring(PREFIX.length);
    
    if (encryptedPayload.length === 0) {
      throw new Error('Empty encrypted payload after removing prefix');
    }
    
    return encryptedPayload;
  }

  /**
   * Decrypt and validate the payload structure
   */
  private async decryptAndValidatePayload(encryptedPayload: string): Promise<PayloadLiveChatKey> {
    try {
      // Use the encryption service to decrypt
      const payload = this.encryptionWebsiteService.decryptLiveChatPayload(encryptedPayload);
      
      // Additional validation (encryption service already validates basic structure)
      this.validatePayloadStructure(payload);
      
      return payload;
    } catch (error) {
      this.logger.error('Failed to decrypt website key payload', {
        error: error.message,
        encryptedPayloadLength: encryptedPayload.length,
      });
      
      throw new Error('Failed to decrypt website key');
    }
  }

  /**
   * Validate the decrypted payload structure
   */
  private validatePayloadStructure(payload: PayloadLiveChatKey): void {
    if (!payload.websiteId || typeof payload.websiteId !== 'string') {
      throw new Error('Invalid websiteId in payload');
    }
    
    if (!payload.userId || typeof payload.userId !== 'number' || payload.userId <= 0) {
      throw new Error('Invalid userId in payload');
    }
    
    // Validate websiteId format (should be UUID)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(payload.websiteId)) {
      throw new Error('websiteId must be a valid UUID');
    }
  }
}

/**
 * Decorator to extract website payload from request
 * Use this in controllers to get the decrypted website payload
 */
export const WebsitePayload = (): ParameterDecorator => {
  return (target: any, propertyKey: string | symbol | undefined, parameterIndex: number) => {
    const existingMetadata = Reflect.getMetadata('custom:website_payload', target) || [];
    existingMetadata.push(parameterIndex);
    Reflect.defineMetadata('custom:website_payload', existingMetadata, target);
  };
};