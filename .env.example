PORT=

# Database
DB_HOST=
DB_URL=
DB_PORT=
DB_USERNAME=
DB_PASSWORD=
DB_DATABASE=
DB_SSL=

# CloudFlare R2
CF_R2_ACCESS_KEY=
CF_R2_SECRET_KEY=
CF_R2_ENDPOINT=
CF_BUCKET_NAME=
CF_R2_REGION=

#cdn
CDN_URL=
CDN_SECRET_KEY=

#openai
OPENAI_API_KEY=

# Zalo SDK Configuration
ZALO_APP_ID=
ZALO_APP_SECRET=
ZALO_OA_ACCESS_TOKEN=
ZALO_BASE_URL=
ZALO_TIMEOUT=
ZALO_DEBUG=

# JWT Authentication
JWT_SECRET=
JWT_ACCESS_TOKEN_EXPIRATION_TIME=
JWT_REFRESH_TOKEN_EXPIRATION_TIME=
JWT_OTP_EXPIRATION_TIME=

EXTERNAL_EMAIL_API_URL=

# SMTP Email Configuration
EMAIL_SMTP_HOST=
EMAIL_SMTP_PORT=
EMAIL_SMTP_USER=
EMAIL_SMTP_PASS=
EMAIL_FROM=

# Redis Connection URL
REDIS_URL=

# PDF Editing API URL
PDF_EDIT_API_URL=

# SMS Service Configuration
SERVICE_HOST_SMS=
SERVICE_SMS_API_KEY=

# Google reCAPTCHA
RECAPTCHA_SECRET_KEY=

# Google OAuth Configuration
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
GOOGLE_REDIRECT_URI=
GOOGLE_APP_ID=

# SEPAY HUB CONFIG
SEPAY_HUB_API_KEY=
SEPAY_HUB_CLIENT_ID=
SEPAY_HUB_CLIENT_SECRET=
SEPAY_HUB_API_URL=

# SEPAY Webhook API Key
SEPAY_WEBHOOK_API_KEY=

NODE_ENV=

# Swagger Configuration
SWAGGER_LOCAL_URL=
SWAGGER_DEV_URL=
SWAGGER_TEST_URL=
SWAGGER_STAGING_URL=
SWAGGER_PROD_URL=

# Agent API Key
AGENT_API_KEY=

# Referral Configuration
REFERRAL_BASE_URL=
REFERRAL_PATH=

# Default Tracking Link Configuration
DEFAULT_TRACKING_BASE_URL=

# Encryption Service
ENCRYPTION_SECRET_KEY=

# Automation Web Configuration
AUTOMATION_WEB_API_URL=http://localhost:8002
AUTOMATION_WEB_API_KEY=
AUTOMATION_WEB_TIMEOUT=30000

# System Configuration Encryption (riêng biệt với ENCRYPTION_SECRET_KEY)
SYSTEM_CONFIG_ENCRYPTION_KEY=

# Key Pair Encryption (cho mã hóa với public key riêng cho từng hàng)
KEY_PAIR_PRIVATE_KEY=

# Zalo API Configuration
ZALO_APP_ID=
ZALO_APP_SECRET=
ZALO_WEBHOOK_SECRET=
ZALO_WEBHOOK_URL=

# Google OAuth Configuration
GOOGLE_API_KEY=
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
GOOGLE_REDIRECT_URI=
GOOGLE_APP_ID=
GOOGLE_ANALYTICS_PROPERTY_ID=
GOOGLE_DRIVE_FOLDER_ID=

# Các URI chuyển hướng khác (nếu cần)
# GOOGLE_REDIRECT_URI_PRODUCTION=
# GOOGLE_REDIRECT_URI_STAGING=

# Google Cloud Configuration
GOOGLE_APPLICATION_CREDENTIALS=
GOOGLE_CLOUD_PROJECT_ID=
GOOGLE_CLOUD_STORAGE_BUCKET=

# Google Ads API Configuration
GOOGLE_ADS_CLIENT_ID=
GOOGLE_ADS_CLIENT_SECRET=
GOOGLE_ADS_DEVELOPER_TOKEN=
GOOGLE_ADS_REFRESH_TOKEN=
GOOGLE_ADS_LOGIN_CUSTOMER_ID=

FPT_SMS_CLIENT_ID=
FPT_SMS_CLIENT_SECRET=
FPT_SMS_SCOPE=
FPT_SMS_API_URL=
FPT_SMS_BRANDNAME=

# Twilio SMS Configuration
TWILIO_ACCOUNT_SID=
TWILIO_AUTH_TOKEN=
TWILIO_PHONE_NUMBER=
TWILIO_MESSAGING_SERVICE_SID=
TWILIO_WEBHOOK_URL=
TWILIO_STATUS_CALLBACK_URL=

ADMIN_SECRECT_MODEL=
USER_SECRECT_MODEL=

FACEBOOK_APP_ID
FACEBOOK_APP_SECRET
FACEBOOK_REDIRECT_URI
FACEBOOK_GRAPH_API_VERSION

# Proxy Configuration for URL Crawling
# Format: [{"host": "proxy1.com", "port": 8080, "auth": {"username": "user", "password": "pass"}}, ...]
PROXY_LIST=

# Crawling Performance Settings
CRAWL_RATE_LIMIT_REQUESTS_PER_MINUTE=
CRAWL_MIN_DELAY_BETWEEN_REQUESTS=
CRAWL_CONCURRENCY_LIMIT=
CRAWL_DEFAULT_TIMEOUT=

# GHTK Shipping Configuration
# IMPORTANT: Test token may be expired. Get real token from: https://khachhang.ghtk.vn/ > Settings > API
# Replace with your actual GHTK token and partner code
GHTK_TOKEN=
GHTK_PARTNER_CODE=
GHTK_BASE_URL=
GHTK_TIMEOUT=
GHTK_TEST_MODE=

# GHN Shipping Configuration
# IMPORTANT: Get real token and shop ID from: https://dev-online-gateway.ghn.vn/ > Settings > API
# Replace with your actual GHN token and shop ID
GHN_TOKEN=
GHN_SHOP_ID=
GHN_BASE_URL=
GHN_TIMEOUT=
GHN_TEST_MODE=

# RAG API Configuration
RAG_API_URL=
SECRET_KEY=

