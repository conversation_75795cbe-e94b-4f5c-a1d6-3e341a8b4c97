import { Injectable, Logger } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@common/response/api-response-dto';
import { WorkflowRepository } from '../../repositories';
import { WorkflowValidationHelper } from '../../helpers';
import { WORKFLOW_ADMIN_ERROR_CODES } from '../../exceptions/workflow.exception';
import {
  CreateWorkflowDto,
  UpdateWorkflowDto,
  WorkflowResponseDto,
  QueryWorkflowDto,
  BulkDeleteWorkflowDto,
  BulkDeleteWorkflowResponseDto,
} from '../../dto';

/**
 * Service xử lý business logic cho Workflow của Admin
 */
@Injectable()
export class WorkflowAdminService {
  private readonly logger = new Logger(WorkflowAdminService.name);

  constructor(
    private readonly workflowRepository: WorkflowRepository,
    private readonly validationHelper: WorkflowValidationHelper,
  ) {}

  /**
   * <PERSON><PERSON>y danh sách tất cả workflows (admin có thể xem tất cả)
   * @param employeeId ID của employee
   * @param queryDto Query parameters
   * @returns Danh sách workflows với pagination
   */
  async getWorkflows(
    employeeId: number,
    queryDto: QueryWorkflowDto,
  ): Promise<PaginatedResult<WorkflowResponseDto>> {
    this.logger.log(`Admin ${employeeId} lấy danh sách tất cả workflows`);

    // Validate input
    this.validationHelper.validateEmployeeId(employeeId);
    this.validationHelper.validatePaginationParams(queryDto.page, queryDto.limit);

    try {
      const { workflows, total } = await this.workflowRepository.findAllWorkflows({
        page: queryDto.page,
        limit: queryDto.limit,
        search: queryDto.search,
        isActive: queryDto.isActive,
        sortBy: queryDto.sortBy,
        sortDirection: queryDto.sortDirection,
      });

      const workflowDtos = workflows.map(workflow =>
        plainToInstance(WorkflowResponseDto, workflow, { excludeExtraneousValues: true })
      );

      return {
        items: workflowDtos,
        meta: {
          totalItems: total,
          itemCount: workflowDtos.length,
          itemsPerPage: queryDto.limit || 10,
          totalPages: Math.ceil(total / (queryDto.limit || 10)),
          currentPage: queryDto.page || 1,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi admin ${employeeId} lấy danh sách workflows:`, error);
      throw new AppException(
        WORKFLOW_ADMIN_ERROR_CODES.WORKFLOW_FETCH_ERROR,
        'Lỗi khi lấy danh sách workflows'
      );
    }
  }

  /**
   * Lấy workflow theo ID (admin có thể xem tất cả, nhưng chỉ edit của mình)
   * @param employeeId ID của employee
   * @param workflowId ID của workflow
   * @returns Workflow details
   */
  async getWorkflowById(employeeId: number, workflowId: string): Promise<WorkflowResponseDto> {
    this.logger.log(`Admin ${employeeId} lấy workflow: ${workflowId}`);

    // Validate input
    this.validationHelper.validateEmployeeId(employeeId);
    this.validationHelper.validateWorkflowIds([workflowId]);

    try {
      const workflow = await this.workflowRepository.findWorkflowById(workflowId);
      
      // Admin có thể xem tất cả workflows
      this.validationHelper.validateAdminViewAccess(workflow);

      return plainToInstance(WorkflowResponseDto, workflow, { excludeExtraneousValues: true });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      
      this.logger.error(`Lỗi khi admin ${employeeId} lấy workflow ${workflowId}:`, error);
      throw new AppException(
        WORKFLOW_ADMIN_ERROR_CODES.WORKFLOW_FETCH_ERROR,
        'Lỗi khi lấy thông tin workflow'
      );
    }
  }

  /**
   * Tạo workflow mới cho admin
   * @param employeeId ID của employee
   * @param createDto Dữ liệu tạo workflow
   * @returns Workflow đã tạo
   */
  async createWorkflow(employeeId: number, createDto: CreateWorkflowDto): Promise<WorkflowResponseDto> {
    this.logger.log(`Admin ${employeeId} tạo workflow mới`);

    // Validate input
    this.validationHelper.validateCreateAdminWorkflowData(createDto, employeeId);

    try {
      const workflowData = {
        name: createDto.name,
        isActive: false,
        settings: createDto.settings || undefined,
        userId: null,
        employeeId: employeeId,
      };

      const workflow = await this.workflowRepository.createWorkflow(workflowData);

      return plainToInstance(WorkflowResponseDto, workflow, { excludeExtraneousValues: true });
    } catch (error) {
      this.logger.error(`Lỗi khi admin ${employeeId} tạo workflow:`, error);
      throw new AppException(
        WORKFLOW_ADMIN_ERROR_CODES.WORKFLOW_CREATION_ERROR,
        'Lỗi khi tạo workflow'
      );
    }
  }

  /**
   * Cập nhật workflow (chỉ được edit workflow của chính admin)
   * @param employeeId ID của employee
   * @param workflowId ID của workflow
   * @param updateDto Dữ liệu cập nhật
   * @returns Workflow đã cập nhật
   */
  async updateWorkflow(
    employeeId: number,
    workflowId: string,
    updateDto: UpdateWorkflowDto,
  ): Promise<WorkflowResponseDto> {
    this.logger.log(`Admin ${employeeId} cập nhật workflow: ${workflowId}`);

    // Validate input
    this.validationHelper.validateEmployeeId(employeeId);
    this.validationHelper.validateWorkflowIds([workflowId]);
    this.validationHelper.validateUpdateWorkflowData(updateDto);

    try {
      // Check ownership - chỉ được edit workflow của chính admin
      const existingWorkflow = await this.workflowRepository.findWorkflowByIdAndEmployeeId(workflowId, employeeId);
      this.validationHelper.validateAdminOwnership(existingWorkflow, employeeId);

      // Update workflow - convert null to undefined for TypeORM compatibility
      const updateData = {
        ...updateDto,
        settings: updateDto.settings === null ? undefined : updateDto.settings,
      };
      const updatedWorkflow = await this.workflowRepository.updateWorkflow(workflowId, updateData);

      if (!updatedWorkflow) {
        throw new AppException(
          WORKFLOW_ADMIN_ERROR_CODES.WORKFLOW_UPDATE_ERROR,
          'Không thể cập nhật workflow'
        );
      }

      return plainToInstance(WorkflowResponseDto, updatedWorkflow, { excludeExtraneousValues: true });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      
      this.logger.error(`Lỗi khi admin ${employeeId} cập nhật workflow ${workflowId}:`, error);
      throw new AppException(
        WORKFLOW_ADMIN_ERROR_CODES.WORKFLOW_UPDATE_ERROR,
        'Lỗi khi cập nhật workflow'
      );
    }
  }

  /**
   * Bulk delete workflows (chỉ được xóa workflow của chính admin)
   * @param employeeId ID của employee
   * @param bulkDeleteDto Danh sách IDs cần xóa
   * @returns Kết quả bulk delete
   */
  async bulkDeleteWorkflows(
    employeeId: number,
    bulkDeleteDto: BulkDeleteWorkflowDto,
  ): Promise<BulkDeleteWorkflowResponseDto> {
    this.logger.log(`Admin ${employeeId} bulk delete workflows: ${bulkDeleteDto.ids.join(', ')}`);

    // Validate input
    this.validationHelper.validateEmployeeId(employeeId);
    this.validationHelper.validateWorkflowIds(bulkDeleteDto.ids);

    try {
      // Validate ownership for all workflows - chỉ được xóa workflow của chính admin
      const validIds = await this.workflowRepository.validateAdminWorkflowIds(bulkDeleteDto.ids, employeeId);
      const invalidIds = bulkDeleteDto.ids.filter(id => !validIds.includes(id));

      // If no workflows can be deleted, throw ownership error
      if (validIds.length === 0) {
        this.logger.warn(`Admin ${employeeId} không thể xóa bất kỳ workflow nào trong danh sách: ${bulkDeleteDto.ids.join(', ')}`);
        throw new AppException(WORKFLOW_ADMIN_ERROR_CODES.ADMIN_WORKFLOW_BULK_DELETE_OWNERSHIP_ERROR);
      }

      const errors: Record<string, string> = {};
      invalidIds.forEach(id => {
        errors[id] = 'Workflow không tồn tại hoặc không thuộc về admin hiện tại';
      });

      let cascadeStats = {
        totalConnectionsDeleted: 0,
        totalNodesDeleted: 0,
        totalExecutionsDeleted: 0,
      };

      let deletedIds: string[] = [];

      // Perform cascade delete for valid workflows
      const cascadeResult = await this.workflowRepository.bulkCascadeDeleteWorkflows(validIds);

      cascadeStats = {
        totalConnectionsDeleted: cascadeResult.totalConnectionsDeleted,
        totalNodesDeleted: cascadeResult.totalNodesDeleted,
        totalExecutionsDeleted: cascadeResult.totalExecutionsDeleted,
      };

      deletedIds = validIds;

      // If some workflows failed to delete, throw partial failure error
      if (invalidIds.length > 0) {
        this.logger.warn(`Admin ${employeeId} chỉ xóa được ${deletedIds.length}/${bulkDeleteDto.ids.length} workflows`);
        throw new AppException(WORKFLOW_ADMIN_ERROR_CODES.ADMIN_WORKFLOW_BULK_OPERATION_PARTIAL_FAILURE);
      }

      // All workflows deleted successfully
      return {
        totalRequested: bulkDeleteDto.ids.length,
        deletedCount: deletedIds.length,
        failedCount: invalidIds.length,
        deletedIds,
        failedIds: invalidIds,
        errors,
        cascadeStats,
      };
    } catch (error) {
      // Re-throw AppException as-is
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Lỗi khi admin ${employeeId} bulk delete workflows:`, error);
      throw new AppException(
        WORKFLOW_ADMIN_ERROR_CODES.WORKFLOW_BULK_DELETE_ERROR,
        'Lỗi khi xóa workflows'
      );
    }
  }


}
