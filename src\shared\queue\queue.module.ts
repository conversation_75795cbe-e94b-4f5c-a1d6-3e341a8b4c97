import { Global, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { QueueService } from './queue.service';
import { EmailSystemQueueService } from './email-system-queue.service';
import { EmailMarketingQueueService } from './email-marketing.queue.service';
import { FineTuneQueueService } from './fine-tune-queue.service';
import { PageNavigationQueueService } from './page-navigation-queue.service';
import { ZaloArticleTrackingQueue } from './zalo-article-tracking.queue';
import { QueueName, DEFAULT_JOB_OPTIONS } from './queue.constants';
import { BullModule } from '@nestjs/bullmq';

/**
 * Module quản lý hệ thống queue của ứng dụng
 */
@Global()
@Module({
  imports: [
    BullModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        const redisUrl = configService.get<string>('REDIS_URL');

        return {
          // Cấu hình kết nối Redis dựa trên URL thay vì các tham số riêng lẻ
          connection: {
            url: redisUrl,
          },
          defaultJobOptions: DEFAULT_JOB_OPTIONS,
        };
      },
    }),
    /**
     * Đăng ký các queue cụ thể ở đây
     * Mỗi queue là một module con trong hệ thống
     */
    BullModule.registerQueue(
      {
        name: QueueName.EMAIL, // Queue xử lý email
      },
      {
        name: QueueName.SMS, // Queue xử lý SMS
      },
      {
        name: QueueName.NOTIFICATION, // Queue xử lý thông báo
      },
      {
        name: QueueName.DATA_PROCESS, // Queue xử lý dữ liệu
      },
      {
        name: QueueName.SEND_SYSTEM_EMAIL, // Queue xử lý email hệ thống
      },
      {
        name: QueueName.AGENT, // Queue xử lý agent
      },
      {
        name: QueueName.EMAIL_SYSTEM, // Queue xử lý email system
      },
      {
        name: QueueName.EMAIL_MARKETING, // Queue xử lý email marketing
      },
      {
        name: QueueName.SMS_MARKETING, // Queue xử lý SMS marketing
        defaultJobOptions: {
          ...DEFAULT_JOB_OPTIONS,
          removeOnComplete: 50, // Giữ lại 50 job hoàn thành
          removeOnFail: 100, // Giữ lại 100 job thất bại để debug
          attempts: 3, // Số lần retry
          backoff: {
            type: 'exponential',
            delay: 2000, // Delay 2 giây cho retry
          },
        },
      },
      {
        name: QueueName.ZALO_ZNS, // Queue xử lý Zalo ZNS
      },
      {
        name: QueueName.ZALO_CONSULTATION_SEQUENCE, // Queue xử lý Zalo Consultation Sequence
        defaultJobOptions: {
          ...DEFAULT_JOB_OPTIONS,
          removeOnComplete: 50, // Giữ lại 50 job hoàn thành
          removeOnFail: 100, // Giữ lại 100 job thất bại để debug
          attempts: 3, // Số lần thử lại cho consultation sequence
          backoff: {
            type: 'exponential',
            delay: 2000, // Delay 2 giây cho retry
          },
        },
      },
      {
        name: QueueName.ZALO_GROUP_MESSAGE_SEQUENCE, // Queue xử lý Zalo Group Message Sequence
        defaultJobOptions: {
          ...DEFAULT_JOB_OPTIONS,
          removeOnComplete: 50, // Giữ lại 50 job hoàn thành
          removeOnFail: 100, // Giữ lại 100 job thất bại để debug
          attempts: 3, // Số lần thử lại cho group message sequence
          backoff: {
            type: 'exponential',
            delay: 5000, // Delay 5s cho group message sequence retry
          },
        },
      },
      {
        name: QueueName.ZALO_VIDEO_TRACKING, // Queue xử lý Zalo Video Tracking
        defaultJobOptions: {
          ...DEFAULT_JOB_OPTIONS,
          removeOnComplete: 20, // Giữ lại 20 job hoàn thành gần nhất
          removeOnFail: 50, // Giữ lại 50 job thất bại để debug
          attempts: 5, // Số lần thử lại cho tracking
          backoff: {
            type: 'exponential',
            delay: 5000, // Delay 5s cho tracking retry
          },
        },
      },
      {
        name: QueueName.ZALO_ARTICLE_SCHEDULER, // Queue xử lý lên lịch xuất bản bài viết Zalo
        defaultJobOptions: {
          ...DEFAULT_JOB_OPTIONS,
          removeOnComplete: 10, // Giữ lại 10 job hoàn thành gần nhất
          removeOnFail: 20, // Giữ lại 20 job thất bại để debug
          attempts: 3, // Số lần thử lại cho scheduler
          backoff: {
            type: 'exponential',
            delay: 3000, // Delay 3s cho scheduler retry
          },
        },
      },
      {
        name: QueueName.ZALO_ARTICLE_TRACKING, // Queue xử lý tracking bài viết Zalo
        defaultJobOptions: {
          ...DEFAULT_JOB_OPTIONS,
          removeOnComplete: 20, // Giữ lại 20 job hoàn thành gần nhất
          removeOnFail: 50, // Giữ lại 50 job thất bại để debug
          attempts: 15, // Số lần thử lại cho tracking (khoảng 5-10 phút)
          backoff: {
            type: 'exponential',
            delay: 3000, // Delay 3s cho tracking retry
          },
        },
      },
      {
        name: QueueName.ZALO_WEBHOOK, // Queue xử lý Zalo Webhook
        defaultJobOptions: {
          ...DEFAULT_JOB_OPTIONS,
          removeOnComplete: 50, // Giữ lại 50 job hoàn thành
          removeOnFail: 100, // Giữ lại 100 job thất bại để debug
          attempts: 3, // Số lần retry
          backoff: {
            type: 'exponential',
            delay: 2000, // Delay 2 giây cho retry
          },
        },
      },
      {
        name: QueueName.CRAWL_URL, // Queue xử lý crawl URL cho user
        defaultJobOptions: {
          ...DEFAULT_JOB_OPTIONS,
          removeOnComplete: 10, // Giữ lại 10 job hoàn thành gần nhất
          removeOnFail: 50, // Giữ lại 50 job thất bại để debug
        },
      },
      {
        name: QueueName.CRAWL_URL_ADMIN, // Queue xử lý crawl URL cho admin
        defaultJobOptions: {
          ...DEFAULT_JOB_OPTIONS,
          removeOnComplete: 10, // Giữ lại 10 job hoàn thành gần nhất
          removeOnFail: 50, // Giữ lại 50 job thất bại để debug
        },
      },
      {
        name: QueueName.CALENDAR, // Queue xử lý calendar
        defaultJobOptions: {
          ...DEFAULT_JOB_OPTIONS,
          removeOnComplete: 20, // Giữ lại 20 job hoàn thành gần nhất
          removeOnFail: 100, // Giữ lại 100 job thất bại để debug
        },
      },
      {
        name: QueueName.FINE_TUNE, // Queue xử lý fine-tuning
        defaultJobOptions: {
          ...DEFAULT_JOB_OPTIONS,
          removeOnComplete: 50, // Giữ lại 50 job hoàn thành gần nhất
          removeOnFail: 100, // Giữ lại 100 job thất bại để debug
          attempts: 5, // Nhiều lần thử lại hơn cho fine-tuning
          backoff: {
            type: 'exponential',
            delay: 5000, // Delay lâu hơn cho fine-tuning
          },
        },
      },
      {
        name: QueueName.INTEGRATION, // Queue xử lý tích hợp
        defaultJobOptions: {
          ...DEFAULT_JOB_OPTIONS,
          removeOnComplete: 10, // Giữ lại 10 job hoàn thành gần nhất
          removeOnFail: 50, // Giữ lại 50 job thất bại để debug
          attempts: 3, // Số lần thử lại cho integration test
          backoff: {
            type: 'exponential',
            delay: 2000, // Delay 2s cho integration test
          },
        },
      },
      {
        name: QueueName.ZALO_AUDIENCE_SYNC, // Queue xử lý đồng bộ Zalo audience
        defaultJobOptions: {
          ...DEFAULT_JOB_OPTIONS,
          removeOnComplete: 20, // Giữ lại 20 job hoàn thành gần nhất
          removeOnFail: 100, // Giữ lại 100 job thất bại để debug
          attempts: 3, // Số lần thử lại cho sync
          backoff: {
            type: 'exponential',
            delay: 3000, // Delay 3s cho sync retry
          },
        },
      },
      {
        name: QueueName.IN_APP_AI, // Queue xử lý In-App AI processing
        defaultJobOptions: {
          ...DEFAULT_JOB_OPTIONS,
          removeOnComplete: 50, // Giữ lại 50 job hoàn thành
          removeOnFail: 100, // Giữ lại 100 job thất bại để debug
          attempts: 3, // Số lần retry
          backoff: {
            type: 'exponential',
            delay: 2000, // Delay 2 giây cho retry
          },
        },
      },
      {
        name: QueueName.WEBSITE_AI, // Queue xử lý Website AI processing
        defaultJobOptions: {
          ...DEFAULT_JOB_OPTIONS,
          removeOnComplete: 50, // Giữ lại 50 job hoàn thành
          removeOnFail: 100, // Giữ lại 100 job thất bại để debug
          attempts: 3, // Số lần retry
          backoff: {
            type: 'exponential',
            delay: 2000, // Delay 2 giây cho retry
          },
        },
      },
      {
        name: QueueName.ZALO_UPLOAD, // Queue xử lý Zalo Upload
        defaultJobOptions: {
          ...DEFAULT_JOB_OPTIONS,
          removeOnComplete: 30, // Giữ lại 30 job hoàn thành gần nhất
          removeOnFail: 50, // Giữ lại 50 job thất bại để debug
          attempts: 3, // Số lần thử lại cho upload
          backoff: {
            type: 'exponential',
            delay: 2000, // Delay 2s cho upload retry
          },
          // Note: timeout sẽ được set trong từng job riêng lẻ
        },
      },
      {
        name: QueueName.WEBHOOK, // Queue xử lý Webhook Events
        defaultJobOptions: {
          ...DEFAULT_JOB_OPTIONS,
          removeOnComplete: 100, // Giữ lại 100 job hoàn thành gần nhất
          removeOnFail: 200, // Giữ lại 200 job thất bại để debug
          attempts: 3, // Số lần thử lại cho webhook processing
          backoff: {
            type: 'exponential',
            delay: 1000, // Delay 1s cho webhook retry
          },
          priority: 10, // High priority cho webhook events
        },
      },
      {
        name: QueueName.EXTERNAL_WEBHOOK, // Queue xử lý External Webhooks
        defaultJobOptions: {
          ...DEFAULT_JOB_OPTIONS,
          removeOnComplete: 50, // Giữ lại 50 job hoàn thành gần nhất
          removeOnFail: 100, // Giữ lại 100 job thất bại để debug
          attempts: 5, // Nhiều lần thử lại hơn cho external webhooks
          backoff: {
            type: 'exponential',
            delay: 2000, // Delay 2s cho external webhook retry
          },
        },
      },
      {
        name: QueueName.WORKFLOW_EXECUTION, // Queue xử lý workflow execution
        defaultJobOptions: {
          ...DEFAULT_JOB_OPTIONS,
          removeOnComplete: 100, // Giữ lại 100 job hoàn thành để tracking
          removeOnFail: 200, // Giữ lại 200 job thất bại để debug workflow
          attempts: 3, // Số lần thử lại cho workflow execution
          backoff: {
            type: 'exponential',
            delay: 3000, // Delay 3s cho workflow retry
          },
        },
      },
      {
        name: QueueName.PAGE_NAVIGATION, // Queue xử lý page navigation từ app khác
        defaultJobOptions: {
          ...DEFAULT_JOB_OPTIONS,
          removeOnComplete: 50, // Giữ lại 50 job hoàn thành
          removeOnFail: 100, // Giữ lại 100 job thất bại để debug
          attempts: 2, // Số lần thử lại cho page navigation
          backoff: {
            type: 'fixed',
            delay: 1000, // Delay 1s cho retry
          },
        },
      },
      // Node test queue configuration removed - focusing on real execution only
    ),
  ],
  providers: [
    QueueService,
    EmailSystemQueueService,
    EmailMarketingQueueService,
    FineTuneQueueService,
    PageNavigationQueueService,
    ZaloArticleTrackingQueue,
  ],
  exports: [
    QueueService,
    EmailSystemQueueService,
    EmailMarketingQueueService,
    FineTuneQueueService,
    PageNavigationQueueService,
    ZaloArticleTrackingQueue,
    BullModule,
  ],
})
export class QueueModule {}
