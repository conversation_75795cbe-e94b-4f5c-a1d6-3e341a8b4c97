-- <PERSON><PERSON> để insert Schedule Node Definition vào database (Version 2)
-- Compatible với IScheduleParameters interface và delayed jobs system
-- Updated to match nested structure và job tracking fields

INSERT INTO node_definitions (
    id,
    type_name,
    version,
    display_name,
    description,
    group_name,
    icon,
    properties,
    inputs,
    outputs,
    credentials,
    created_at,
    updated_at
) VALUES (
    gen_random_uuid(),
    'schedule',
    2,
    'Schedule',
    'Schedule workflow execution using delayed jobs. Supports cron expressions, daily/weekly/monthly schedules, intervals, and one-time executions with automatic recurring logic.',
    'Utility',
    'calendar',
    '[
        {
            "name": "name",
            "displayName": "Schedule Name",
            "description": "Name for this schedule",
            "type": "string",
            "required": false,
            "placeholder": "Daily Report Generation"
        },
        {
            "name": "schedule_type",
            "displayName": "Schedule Type",
            "description": "Type of schedule to create",
            "type": "options",
            "required": true,
            "options": [
                {"name": "Once", "value": "once"},
                {"name": "Daily", "value": "daily"},
                {"name": "Weekly", "value": "weekly"},
                {"name": "Monthly", "value": "monthly"},
                {"name": "Interval", "value": "interval"},
                {"name": "Cron Expression", "value": "cron"}
            ],
            "default": "daily"
        },
        {
            "name": "is_active",
            "displayName": "Active",
            "description": "Enable/disable this schedule",
            "type": "boolean",
            "default": true
        },
        {
            "name": "execution_behavior",
            "displayName": "Execution Behavior",
            "description": "How to execute the workflow",
            "type": "options",
            "required": true,
            "options": [
                {"name": "Start Workflow", "value": "start_workflow"},
                {"name": "Start from Node", "value": "start_from_node"},
                {"name": "Trigger Event", "value": "trigger_event"}
            ],
            "default": "start_workflow"
        },
        {
            "name": "target_node_id",
            "displayName": "Target Node ID",
            "description": "Node ID to start execution from (for start_from_node behavior)",
            "type": "string",
            "required": false,
            "displayOptions": {
                "show": {
                    "execution_behavior": ["start_from_node"]
                }
            }
        },
        {
            "name": "event_name",
            "displayName": "Event Name",
            "description": "Name of event to trigger (for trigger_event behavior)",
            "type": "string",
            "required": false,
            "placeholder": "daily_report_trigger",
            "displayOptions": {
                "show": {
                    "execution_behavior": ["trigger_event"]
                }
            }
        },
        {
            "name": "overlap_behavior",
            "displayName": "Overlap Behavior",
            "description": "What to do if previous execution is still running",
            "type": "options",
            "required": true,
            "options": [
                {"name": "Allow", "value": "allow"},
                {"name": "Skip", "value": "skip"},
                {"name": "Replace", "value": "replace"},
                {"name": "Queue", "value": "queue"}
            ],
            "default": "skip"
        },
        {
            "name": "once_config",
            "displayName": "Once Configuration",
            "description": "Configuration for one-time execution",
            "type": "collection",
            "required": false,
            "displayOptions": {
                "show": {
                    "schedule_type": ["once"]
                }
            },
            "options": [
                {
                    "name": "datetime",
                    "displayName": "Date & Time",
                    "description": "When to run the schedule",
                    "type": "dateTime",
                    "required": true
                }
            ]
        },
        {
            "name": "daily_config",
            "displayName": "Daily Configuration",
            "description": "Configuration for daily execution",
            "type": "collection",
            "required": false,
            "displayOptions": {
                "show": {
                    "schedule_type": ["daily"]
                }
            },
            "options": [
                {
                    "name": "hour",
                    "displayName": "Hour",
                    "description": "Hour to run (0-23)",
                    "type": "number",
                    "required": true,
                    "minValue": 0,
                    "maxValue": 23,
                    "default": 9
                },
                {
                    "name": "minute",
                    "displayName": "Minute",
                    "description": "Minute to run (0-59)",
                    "type": "number",
                    "required": true,
                    "minValue": 0,
                    "maxValue": 59,
                    "default": 0
                },
                {
                    "name": "second",
                    "displayName": "Second",
                    "description": "Second to run (0-59)",
                    "type": "number",
                    "required": false,
                    "minValue": 0,
                    "maxValue": 59,
                    "default": 0
                },
                {
                    "name": "timezone",
                    "displayName": "Timezone",
                    "description": "Timezone for execution",
                    "type": "options",
                    "required": false,
                    "options": [
                        {"name": "UTC", "value": "UTC"},
                        {"name": "Asia/Ho_Chi_Minh", "value": "Asia/Ho_Chi_Minh"},
                        {"name": "America/New_York", "value": "America/New_York"},
                        {"name": "Europe/London", "value": "Europe/London"},
                        {"name": "Asia/Tokyo", "value": "Asia/Tokyo"}
                    ],
                    "default": "Asia/Ho_Chi_Minh"
                }
            ]
        },
        {
            "name": "weekly_config",
            "displayName": "Weekly Configuration",
            "description": "Configuration for weekly execution",
            "type": "collection",
            "required": false,
            "displayOptions": {
                "show": {
                    "schedule_type": ["weekly"]
                }
            },
            "options": [
                {
                    "name": "days_of_week",
                    "displayName": "Days of Week",
                    "description": "Select days of week to run (0=Sunday, 1=Monday, etc.)",
                    "type": "options",
                    "required": true,
                    "multipleValues": true,
                    "options": [
                        {"name": "Sunday", "value": 0},
                        {"name": "Monday", "value": 1},
                        {"name": "Tuesday", "value": 2},
                        {"name": "Wednesday", "value": 3},
                        {"name": "Thursday", "value": 4},
                        {"name": "Friday", "value": 5},
                        {"name": "Saturday", "value": 6}
                    ],
                    "default": [1, 2, 3, 4, 5]
                },
                {
                    "name": "hour",
                    "displayName": "Hour",
                    "description": "Hour to run (0-23)",
                    "type": "number",
                    "required": true,
                    "minValue": 0,
                    "maxValue": 23,
                    "default": 9
                },
                {
                    "name": "minute",
                    "displayName": "Minute",
                    "description": "Minute to run (0-59)",
                    "type": "number",
                    "required": true,
                    "minValue": 0,
                    "maxValue": 59,
                    "default": 0
                },
                {
                    "name": "timezone",
                    "displayName": "Timezone",
                    "description": "Timezone for execution",
                    "type": "string",
                    "required": false,
                    "default": "Asia/Ho_Chi_Minh"
                }
            ]
        },
        {
            "name": "monthly_config",
            "displayName": "Monthly Configuration",
            "description": "Configuration for monthly execution",
            "type": "collection",
            "required": false,
            "displayOptions": {
                "show": {
                    "schedule_type": ["monthly"]
                }
            },
            "options": [
                {
                    "name": "day_of_month",
                    "displayName": "Day of Month",
                    "description": "Day of month to run (1-31 or last)",
                    "type": "options",
                    "required": true,
                    "options": [
                        {"name": "1st", "value": 1},
                        {"name": "2nd", "value": 2},
                        {"name": "3rd", "value": 3},
                        {"name": "5th", "value": 5},
                        {"name": "10th", "value": 10},
                        {"name": "15th", "value": 15},
                        {"name": "20th", "value": 20},
                        {"name": "25th", "value": 25},
                        {"name": "Last Day", "value": "last"}
                    ],
                    "default": 1
                },
                {
                    "name": "hour",
                    "displayName": "Hour",
                    "description": "Hour to run (0-23)",
                    "type": "number",
                    "required": true,
                    "minValue": 0,
                    "maxValue": 23,
                    "default": 9
                },
                {
                    "name": "minute",
                    "displayName": "Minute",
                    "description": "Minute to run (0-59)",
                    "type": "number",
                    "required": true,
                    "minValue": 0,
                    "maxValue": 59,
                    "default": 0
                },
                {
                    "name": "timezone",
                    "displayName": "Timezone",
                    "description": "Timezone for execution",
                    "type": "string",
                    "required": false,
                    "default": "Asia/Ho_Chi_Minh"
                }
            ]
        },
        {
            "name": "interval_config",
            "displayName": "Interval Configuration",
            "description": "Configuration for interval execution",
            "type": "collection",
            "required": false,
            "displayOptions": {
                "show": {
                    "schedule_type": ["interval"]
                }
            },
            "options": [
                {
                    "name": "value",
                    "displayName": "Interval Value",
                    "description": "Interval value",
                    "type": "number",
                    "required": true,
                    "minValue": 1,
                    "default": 30
                },
                {
                    "name": "type",
                    "displayName": "Interval Type",
                    "description": "Interval time unit",
                    "type": "options",
                    "required": true,
                    "options": [
                        {"name": "Seconds", "value": "seconds"},
                        {"name": "Minutes", "value": "minutes"},
                        {"name": "Hours", "value": "hours"},
                        {"name": "Days", "value": "days"}
                    ],
                    "default": "minutes"
                }
            ]
        },
        {
            "name": "cron_config",
            "displayName": "Cron Configuration",
            "description": "Configuration for cron expression",
            "type": "collection",
            "required": false,
            "displayOptions": {
                "show": {
                    "schedule_type": ["cron"]
                }
            },
            "options": [
                {
                    "name": "expression",
                    "displayName": "Cron Expression",
                    "description": "Cron expression (e.g., 0 9 * * 1-5 for weekdays at 9AM)",
                    "type": "string",
                    "required": true,
                    "placeholder": "0 9 * * 1-5"
                },
                {
                    "name": "timezone",
                    "displayName": "Timezone",
                    "description": "Timezone for cron execution",
                    "type": "string",
                    "required": false,
                    "default": "Asia/Ho_Chi_Minh"
                }
            ]
        }
    ]'::jsonb,
    '["trigger"]'::jsonb,
    '["main", "error"]'::jsonb,
    '[]'::jsonb,
    EXTRACT(epoch FROM now()) * 1000,
    EXTRACT(epoch FROM now()) * 1000
);

-- Verify the insertion
SELECT 
    id,
    type_name,
    version,
    display_name,
    description,
    group_name,
    icon,
    jsonb_array_length(properties) as properties_count,
    inputs,
    outputs
FROM node_definitions 
WHERE type_name = 'schedule' AND version = 2;
