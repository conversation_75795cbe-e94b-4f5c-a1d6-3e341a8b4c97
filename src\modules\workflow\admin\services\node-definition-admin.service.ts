import { Injectable, Logger } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@common/response/api-response-dto';
import { NodeDefinitionRepository } from '../../repositories';
import { NodeDefinitionValidationHelper } from '../../helpers';
import { WORKFLOW_ADMIN_ERROR_CODES } from '../../exceptions/workflow.exception';
import { NodeGroupEnum } from '../../enums/node-group.enum';
import {
  QueryNodeDefinitionDto,
  NodeDefinitionResponseDto,
  NodeGroupResponseDto,
  UpdateNodeDefinitionDto,
} from '../../dto';

/**
 * Service xử lý business logic cho NodeDefinition của admin
 */
@Injectable()
export class NodeDefinitionAdminService {
  private readonly logger = new Logger(NodeDefinitionAdminService.name);

  constructor(
    private readonly nodeDefinitionRepository: NodeDefinitionRepository,
    private readonly validationHelper: NodeDefinitionValidationHelper,
  ) {}

  /**
   * Lấy danh sách node definitions với phân trang và filter (admin có thể xem tất cả)
   * @param employeeId ID của employee
   * @param queryDto Query parameters
   * @returns Danh sách node definitions với pagination
   */
  async getNodeDefinitions(
    employeeId: number,
    queryDto: QueryNodeDefinitionDto,
  ): Promise<PaginatedResult<NodeDefinitionResponseDto>> {
    this.logger.log(`Admin ${employeeId} lấy danh sách node definitions`);

    // Validate input
    this.validationHelper.validateEmployeeId(employeeId);

    try {
      const { nodeDefinitions, total } = await this.nodeDefinitionRepository.findAllNodeDefinitions({
        page: queryDto.page,
        limit: queryDto.limit,
        search: queryDto.search,
        groupName: queryDto.groupName,
        sortBy: queryDto.sortBy,
        sortDirection: queryDto.sortDirection,
      });

      const nodeDefinitionDtos = nodeDefinitions.map(nodeDefinition =>
        plainToInstance(NodeDefinitionResponseDto, nodeDefinition, { excludeExtraneousValues: true })
      );

      return {
        items: nodeDefinitionDtos,
        meta: {
          totalItems: total,
          itemCount: nodeDefinitionDtos.length,
          itemsPerPage: queryDto.limit || 10,
          totalPages: Math.ceil(total / (queryDto.limit || 10)),
          currentPage: queryDto.page || 1,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi admin ${employeeId} lấy danh sách node definitions:`, error);
      throw new AppException(
        WORKFLOW_ADMIN_ERROR_CODES.NODE_DEFINITION_FETCH_ERROR,
        'Lỗi khi lấy danh sách node definitions'
      );
    }
  }

  /**
   * Lấy danh sách unique group names
   * @param employeeId ID của employee
   * @returns Danh sách unique groups
   */
  async getUniqueGroups(employeeId: number): Promise<NodeGroupResponseDto[]> {
    this.logger.log(`Admin ${employeeId} lấy danh sách unique groups`);

    // Validate input
    this.validationHelper.validateEmployeeId(employeeId);

    try {
      const uniqueGroups = await this.nodeDefinitionRepository.findUniqueGroups();

      // Convert enum values to response DTOs with labels
      return uniqueGroups.map(group => {
        const label = this.getGroupLabel(group);
        return new NodeGroupResponseDto(group, label);
      });
    } catch (error) {
      this.logger.error(`Lỗi khi admin ${employeeId} lấy danh sách unique groups:`, error);
      throw new AppException(
        WORKFLOW_ADMIN_ERROR_CODES.NODE_DEFINITION_GROUPS_FETCH_ERROR,
        'Lỗi khi lấy danh sách nhóm node definitions'
      );
    }
  }

  /**
   * Cập nhật node definition (chỉ admin)
   * @param employeeId ID của employee
   * @param nodeDefinitionId ID của node definition
   * @param updateDto Dữ liệu cập nhật
   * @returns Node definition đã cập nhật
   */
  async updateNodeDefinition(
    employeeId: number,
    nodeDefinitionId: string,
    updateDto: UpdateNodeDefinitionDto,
  ): Promise<NodeDefinitionResponseDto> {
    this.logger.log(`Admin ${employeeId} cập nhật node definition: ${nodeDefinitionId}`);

    // Validate input
    this.validationHelper.validateEmployeeId(employeeId);
    this.validationHelper.validateNodeDefinitionId(nodeDefinitionId);
    this.validationHelper.validateUpdateNodeDefinitionData(updateDto);

    try {
      // Kiểm tra node definition có tồn tại không
      const existingNodeDefinition = await this.nodeDefinitionRepository.findNodeDefinitionById(nodeDefinitionId);
      if (!existingNodeDefinition) {
        throw new AppException(
          WORKFLOW_ADMIN_ERROR_CODES.NODE_DEFINITION_NOT_FOUND,
          'Không tìm thấy node definition'
        );
      }

      // Cập nhật node definition
      const updatedNodeDefinition = await this.nodeDefinitionRepository.updateNodeDefinition(
        nodeDefinitionId,
        updateDto
      );

      if (!updatedNodeDefinition) {
        throw new AppException(WORKFLOW_ADMIN_ERROR_CODES.NODE_DEFINITION_UPDATE_FAILED);
      }

      return plainToInstance(NodeDefinitionResponseDto, updatedNodeDefinition, { excludeExtraneousValues: true });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      
      this.logger.error(`Lỗi khi admin ${employeeId} cập nhật node definition ${nodeDefinitionId}:`, error);
      throw new AppException(WORKFLOW_ADMIN_ERROR_CODES.NODE_DEFINITION_UPDATE_FAILED);
    }
  }

  /**
   * Chuyển đổi group enum value thành label hiển thị
   * @param group Group enum value
   * @returns Label hiển thị
   */
  private getGroupLabel(group: NodeGroupEnum): string {
    const labelMap: Record<NodeGroupEnum, string> = {
      [NodeGroupEnum.TRANSFORM]: 'Transform',
      [NodeGroupEnum.TRIGGER]: 'Trigger',
      [NodeGroupEnum.UTILITY]: 'Utility',
      [NodeGroupEnum.INTEGRATION]: 'Integration',
      [NodeGroupEnum.AI]: 'AI',
      [NodeGroupEnum.LOGIC]: 'Logic',
      [NodeGroupEnum.HTTP]: 'HTTP',
    };

    return labelMap[group] || group;
  }
}
