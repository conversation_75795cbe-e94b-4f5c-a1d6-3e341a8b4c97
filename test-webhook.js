/**
 * Script test webhook Zalo Personal Integration
 * Chạy: node test-webhook.js
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api/v1';

async function testWebhookEndpoints() {
  console.log('🧪 Testing Zalo Personal Webhook Endpoints...\n');

  // 1. Test health check
  console.log('1️⃣ Testing health check...');
  try {
    const healthResponse = await axios.post(`${BASE_URL}/integration/zalo-personal/webhook/health`);
    console.log('✅ Health check:', healthResponse.data);
  } catch (error) {
    console.log('❌ Health check failed:', error.response?.data || error.message);
  }

  // 2. Test login success webhook
  console.log('\n2️⃣ Testing login success webhook...');
  try {
    const webhookData = {
      userId: 1,
      zaloUserId: 'test_zalo_uid_123',
      displayName: 'Test User',
      avatarUrl: 'https://example.com/avatar.jpg',
      integrationId: 'user_1_integration_test_123',
      sessionCookies: 'test_session_cookies',
      browserFingerprint: 'test_browser_fingerprint'
    };

    const webhookResponse = await axios.post(
      `${BASE_URL}/integration/zalo-personal/webhook/login-success`,
      webhookData,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
    
    console.log('✅ Login success webhook:', webhookResponse.data);
    
    // Lưu integration ID để test sau
    const integrationId = webhookResponse.data.data.id;
    console.log(`📝 Created integration ID: ${integrationId}`);
    
    return integrationId;
  } catch (error) {
    console.log('❌ Login success webhook failed:', error.response?.data || error.message);
    return null;
  }
}

async function testIntegrationEndpoints(integrationId) {
  if (!integrationId) {
    console.log('\n⚠️ Skipping integration tests - no integration ID');
    return;
  }

  console.log('\n3️⃣ Testing integration endpoints...');

  // Test lấy zalo_uid
  try {
    const zaloUidResponse = await axios.get(
      `${BASE_URL}/integration/zalo-personal/${integrationId}/zalo-uid`,
      {
        headers: {
          'Authorization': 'Bearer test_token' // Cần JWT token thật
        }
      }
    );
    console.log('✅ Get zalo_uid:', zaloUidResponse.data);
  } catch (error) {
    console.log('❌ Get zalo_uid failed (expected - need real JWT):', error.response?.status);
  }

  // Test tìm theo zalo_uid
  try {
    const findResponse = await axios.get(
      `${BASE_URL}/integration/zalo-personal/by-zalo-uid/test_zalo_uid_123`,
      {
        headers: {
          'Authorization': 'Bearer test_token' // Cần JWT token thật
        }
      }
    );
    console.log('✅ Find by zalo_uid:', findResponse.data);
  } catch (error) {
    console.log('❌ Find by zalo_uid failed (expected - need real JWT):', error.response?.status);
  }
}

async function testErrorWebhooks() {
  console.log('\n4️⃣ Testing error webhooks...');

  // Test login error webhook
  try {
    const errorData = {
      userId: 1,
      integrationId: 'test_integration_123',
      error: 'LOGIN_FAILED',
      errorMessage: 'QR code expired'
    };

    const errorResponse = await axios.post(
      `${BASE_URL}/integration/zalo-personal/webhook/login-error`,
      errorData
    );
    console.log('✅ Login error webhook:', errorResponse.data);
  } catch (error) {
    console.log('❌ Login error webhook failed:', error.response?.data || error.message);
  }

  // Test session expired webhook
  try {
    const expiredData = {
      userId: 1,
      integrationId: 'test_integration_123',
      sessionId: 'test_session_123'
    };

    const expiredResponse = await axios.post(
      `${BASE_URL}/integration/zalo-personal/webhook/session-expired`,
      expiredData
    );
    console.log('✅ Session expired webhook:', expiredResponse.data);
  } catch (error) {
    console.log('❌ Session expired webhook failed:', error.response?.data || error.message);
  }
}

async function testAutomationWebIntegration() {
  console.log('\n5️⃣ Testing automation-web integration...');

  // Simulate automation-web calling webhook
  try {
    const automationWebData = {
      userId: 2,
      zaloUserId: 'automation_test_456',
      displayName: 'Automation Test User',
      avatarUrl: 'https://example.com/automation-avatar.jpg',
      integrationId: 'user_2_integration_automation_456',
      sessionCookies: 'automation_session_cookies',
      browserFingerprint: 'automation_browser_fingerprint'
    };

    console.log('📤 Simulating automation-web webhook call...');
    const response = await axios.post(
      `${BASE_URL}/integration/zalo-personal/webhook/login-success`,
      automationWebData
    );
    
    console.log('✅ Automation-web simulation successful:', {
      integrationId: response.data.data.id,
      zaloUid: response.data.data.metadata.zalo_uid,
      displayName: response.data.data.metadata.displayName
    });
  } catch (error) {
    console.log('❌ Automation-web simulation failed:', error.response?.data || error.message);
  }
}

async function main() {
  try {
    console.log('🚀 Starting Zalo Personal Integration Webhook Tests\n');
    
    // Test webhook endpoints
    const integrationId = await testWebhookEndpoints();
    
    // Test integration endpoints (sẽ fail vì cần JWT)
    await testIntegrationEndpoints(integrationId);
    
    // Test error webhooks
    await testErrorWebhooks();
    
    // Test automation-web integration
    await testAutomationWebIntegration();
    
    console.log('\n🎉 Test completed!');
    console.log('\n📋 Summary:');
    console.log('- Webhook endpoints should work without authentication');
    console.log('- Integration endpoints need JWT token (expected to fail in test)');
    console.log('- Error webhooks should work');
    console.log('- Automation-web simulation should create integration');
    
  } catch (error) {
    console.error('💥 Test failed:', error.message);
  }
}

// Chạy test
main();
