/**
 * @file Interface cho Schedule node
 * 
 * Định nghĩa type-safe interface cho node Schedule bao gồm:
 * - Parameters structure với validation
 * - Properties definition
 * - Input/Output data types
 * - Cron expressions và scheduling options
 * - Timezone support và advanced scheduling features
 */

import {
    IBaseNodeInput,
    IBaseNodeOutput,
    ITypedNodeExecution
} from '../../execute.interface';

// =================================================================
// SECTION 1: ENUMS & TYPES
// Định nghĩa các enum và type cho Schedule node
// =================================================================

/**
 * Schedule types - các loại schedule được hỗ trợ
 */
export enum EScheduleType {
    /** Chạy một lần tại thời điểm cụ thể */
    ONCE = 'once',

    /** Chạy theo cron expression */
    CRON = 'cron',

    /** Chạy theo interval cố định */
    INTERVAL = 'interval',

    /** Chạy theo lịch hàng ngày */
    DAILY = 'daily',

    /** Chạy theo lịch hàng tuần */
    WEEKLY = 'weekly',

    /** Chạy theo lịch hàng tháng */
    MONTHLY = 'monthly'
}

/**
 * Interval types cho INTERVAL schedule
 */
export enum EIntervalType {
    SECONDS = 'seconds',
    MINUTES = 'minutes',
    HOURS = 'hours',
    DAYS = 'days'
}

/**
 * Days of week cho WEEKLY schedule
 */
export enum EDayOfWeek {
    SUNDAY = 0,
    MONDAY = 1,
    TUESDAY = 2,
    WEDNESDAY = 3,
    THURSDAY = 4,
    FRIDAY = 5,
    SATURDAY = 6
}

/**
 * Schedule status
 */
export enum EScheduleStatus {
    /** Schedule đang active */
    ACTIVE = 'active',

    /** Schedule bị pause */
    PAUSED = 'paused',

    /** Schedule đã completed */
    COMPLETED = 'completed',

    /** Schedule bị cancelled */
    CANCELLED = 'cancelled',

    /** Schedule có lỗi */
    ERROR = 'error'
}

/**
 * Execution behavior khi schedule trigger
 */
export enum EExecutionBehavior {
    /** Chạy workflow từ đầu */
    START_WORKFLOW = 'start_workflow',

    /** Chạy từ node cụ thể */
    START_FROM_NODE = 'start_from_node',

    /** Trigger event cho workflow đang chạy */
    TRIGGER_EVENT = 'trigger_event'
}

/**
 * Overlap behavior khi execution trước chưa hoàn thành
 */
export enum EOverlapBehavior {
    /** Cho phép chạy song song */
    ALLOW = 'allow',

    /** Skip execution mới */
    SKIP = 'skip',

    /** Cancel execution cũ và chạy mới */
    REPLACE = 'replace',

    /** Queue execution mới */
    QUEUE = 'queue'
}

/**
 * Timezone types
 */
export enum ETimezoneType {
    /** Sử dụng UTC */
    UTC = 'UTC',

    /** Sử dụng timezone của server */
    SERVER = 'server',

    /** Sử dụng timezone tùy chỉnh */
    CUSTOM = 'custom'
}

// =================================================================
// SECTION 2: CONFIGURATION STRUCTURES
// =================================================================

/**
 * Configuration cho ONCE schedule
 */
export interface IOnceConfig {
    /** Thời điểm chạy (ISO string hoặc timestamp) */
    datetime: string;

    /** Timezone */
    timezone?: string;
}

/**
 * Configuration cho CRON schedule
 */
export interface ICronConfig {
    /** Cron expression (e.g., "0 9 * * 1-5") */
    expression: string;

    /** Timezone */
    timezone?: string;

    /** Mô tả human-readable của cron */
    description?: string;
}

/**
 * Configuration cho INTERVAL schedule
 */
export interface IIntervalConfig {
    /** Interval value */
    value: number;

    /** Interval type */
    type: EIntervalType;

    /** Thời điểm bắt đầu (optional) */
    start_time?: string;

    /** Thời điểm kết thúc (optional) */
    end_time?: string;
}

/**
 * Configuration cho DAILY schedule
 */
export interface IDailyConfig {
    /** Giờ chạy (0-23) */
    hour: number;

    /** Phút chạy (0-59) */
    minute: number;

    /** Giây chạy (0-59) */
    second?: number;

    /** Timezone */
    timezone?: string;
}

/**
 * Configuration cho WEEKLY schedule
 */
export interface IWeeklyConfig {
    /** Các ngày trong tuần */
    days_of_week: EDayOfWeek[];

    /** Giờ chạy (0-23) */
    hour: number;

    /** Phút chạy (0-59) */
    minute: number;

    /** Giây chạy (0-59) */
    second?: number;

    /** Timezone */
    timezone?: string;
}

/**
 * Configuration cho MONTHLY schedule
 */
export interface IMonthlyConfig {
    /** Ngày trong tháng (1-31) hoặc 'last' cho ngày cuối tháng */
    day_of_month: number | 'last';

    /** Giờ chạy (0-23) */
    hour: number;

    /** Phút chạy (0-59) */
    minute: number;

    /** Giây chạy (0-59) */
    second?: number;

    /** Timezone */
    timezone?: string;

    /** Các tháng cụ thể (1-12), nếu không có thì chạy tất cả tháng */
    months?: number[];
}

/**
 * Schedule execution metadata
 */
export interface IScheduleExecution {
    /** Execution ID */
    execution_id: string;

    /** Scheduled time */
    scheduled_time: number;

    /** Actual execution time */
    executed_time: number;

    /** Execution status */
    status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';

    /** Execution duration (milliseconds) */
    duration?: number;

    /** Error message nếu có */
    error_message?: string;

    /** Workflow execution ID */
    workflow_execution_id?: string;
}

// =================================================================
// SECTION 3: PARAMETERS INTERFACE
// =================================================================

/**
 * Interface cho parameters của Schedule node
 */
export interface IScheduleParameters {
    /** Schedule type */
    schedule_type: EScheduleType;

    /** Schedule name/description */
    name: string;

    /** Schedule configuration dựa trên type */
    once_config?: IOnceConfig;
    cron_config?: ICronConfig;
    interval_config?: IIntervalConfig;
    daily_config?: IDailyConfig;
    weekly_config?: IWeeklyConfig;
    monthly_config?: IMonthlyConfig;

    /** Execution behavior */
    execution_behavior: EExecutionBehavior;

    /** Target node ID (cho START_FROM_NODE behavior) */
    target_node_id?: string;

    /** Event name (cho TRIGGER_EVENT behavior) */
    event_name?: string;

    /** Overlap behavior */
    overlap_behavior: EOverlapBehavior;

    /** Maximum concurrent executions */
    max_concurrent_executions?: number;

    /** Schedule có active không */
    is_active: boolean;

    /** Thời điểm bắt đầu schedule */
    start_date?: string;

    /** Thời điểm kết thúc schedule */
    end_date?: string;

    /** Maximum number of executions */
    max_executions?: number;

    /** Timeout cho mỗi execution (milliseconds) */
    execution_timeout?: number;

    // ===== JOB TRACKING FIELDS =====
    /** Current Bull job ID for tracking delayed job */
    jobId?: string;

    /** Next execution timestamp (milliseconds) */
    nextExecutionTime?: number;

    /** Job creation timestamp */
    jobCreatedAt?: number;

    /** Total execution count */
    executionCount?: number;

    /** Last execution timestamp */
    lastExecutionTime?: number;

    /** Last execution status */
    lastExecutionStatus?: 'success' | 'failed' | 'cancelled';

    /** Original CRON expression (for CRON schedules) */
    originalCronExpression?: string;
}

// =================================================================
// SECTION 4: INPUT/OUTPUT INTERFACES
// =================================================================

/**
 * Interface cho input data của Schedule node
 */
export interface IScheduleInput extends IBaseNodeInput {
    /** Schedule configuration override */
    schedule_override?: {
        is_active?: boolean;
        next_run_time?: string;
        input_data?: Record<string, any>;
    };

    /** Context variables */
    variables?: Record<string, any>;
}

/**
 * Interface cho output data của Schedule node
 */
export interface IScheduleOutput extends IBaseNodeOutput {
    /** Schedule metadata */
    schedule_metadata: {
        /** Schedule ID */
        schedule_id: string;

        /** Schedule name */
        name: string;

        /** Schedule type */
        type: EScheduleType;

        /** Current status */
        status: EScheduleStatus;

        /** Next scheduled run time */
        next_run_time?: number;

        /** Last execution time */
        last_execution_time?: number;

        /** Total executions count */
        total_executions: number;

        /** Successful executions count */
        successful_executions: number;

        /** Failed executions count */
        failed_executions: number;

        /** Schedule created time */
        created_at: number;

        /** Schedule updated time */
        updated_at: number;

        /** Recent executions history */
        recent_executions: IScheduleExecution[];
    };

    /** Input data được pass vào workflow */
    input_data: Record<string, any>;

    /** Variables */
    variables: Record<string, any>;
}

// =================================================================
// SECTION 5: TYPE ALIASES - USING EXISTING ENTITIES
// Sử dụng entities có sẵn thay vì duplicate interfaces
// =================================================================

/**
 * Type alias cho Schedule node definition
 * ✅ Sử dụng entity có sẵn thay vì duplicate interface
 * Xem: src/modules/workflow/entities/node-definition.entity.ts
 */
export type IScheduleNodeDefinition = {
    id: string;
    type_name: string;
    version: number;
    display_name: string;
    description?: string;
    group_name: string;
    icon?: string;
    properties: any[]; // Dynamic properties từ database
    inputs?: string[];
    outputs?: string[];
    credentials?: any[];
    created_at: number;
    updated_at: number;
};

/**
 * Type alias cho Schedule node instance
 * ✅ Sử dụng entity có sẵn với type-safe parameters
 * Xem: src/modules/workflow/interfaces/workflow.interface.ts
 */
export type IScheduleNodeInstance = {
    id: string;
    workflow_id: string;
    name: string;
    type: string;
    type_version: string;
    position: { x: number; y: number };
    parameters: IScheduleParameters; // Type-safe parameters
    disabled?: boolean;
    notes?: string;
    retry_on_fail?: boolean;
    max_tries?: number;
    wait_between_tries?: number;
    on_error?: string;
    integration_id?: string;
    node_definition_id: string;
};

/**
 * Type-safe node execution cho Schedule
 */
export type IScheduleNodeExecution = ITypedNodeExecution<
    IScheduleInput,
    IScheduleOutput,
    IScheduleParameters
>;

// =================================================================
// SECTION 6: HELPER FUNCTIONS
// =================================================================

/**
 * Helper function để validate cron expression
 */
export function validateCronExpression(expression: string): {
    isValid: boolean;
    error?: string;
} {
    // Basic cron validation (5 or 6 fields)
    const parts = expression.trim().split(/\s+/);

    if (parts.length < 5 || parts.length > 6) {
        return {
            isValid: false,
            error: 'Cron expression must have 5 or 6 fields'
        };
    }

    // More detailed validation would be implemented here
    // For now, basic validation
    return { isValid: true };
}

/**
 * Helper function để calculate next run time
 */
export function calculateNextRunTime(
    scheduleType: EScheduleType,
    config: any,
    currentTime: number = Date.now()
): number | null {
    switch (scheduleType) {
        case EScheduleType.ONCE:
            const onceTime = new Date(config.datetime).getTime();
            return onceTime > currentTime ? onceTime : null;

        case EScheduleType.INTERVAL:
            const intervalMs = convertIntervalToMs(config.value, config.type);
            return currentTime + intervalMs;

        case EScheduleType.DAILY:
            return calculateNextDailyRun(config, currentTime);

        case EScheduleType.WEEKLY:
            return calculateNextWeeklyRun(config, currentTime);

        case EScheduleType.MONTHLY:
            return calculateNextMonthlyRun(config, currentTime);

        case EScheduleType.CRON:
            // Would use a cron library to calculate next run
            return null; // Placeholder

        default:
            return null;
    }
}

/**
 * Helper function để convert interval to milliseconds
 */
export function convertIntervalToMs(value: number, type: EIntervalType): number {
    switch (type) {
        case EIntervalType.SECONDS:
            return value * 1000;
        case EIntervalType.MINUTES:
            return value * 60 * 1000;
        case EIntervalType.HOURS:
            return value * 60 * 60 * 1000;
        case EIntervalType.DAYS:
            return value * 24 * 60 * 60 * 1000;
        default:
            return value * 1000;
    }
}

/**
 * Helper function để calculate next daily run
 */
function calculateNextDailyRun(config: IDailyConfig, currentTime: number): number {
    const now = new Date(currentTime);
    const nextRun = new Date(now);

    nextRun.setHours(config.hour, config.minute, config.second || 0, 0);

    // Nếu thời gian đã qua trong ngày hôm nay, chuyển sang ngày mai
    if (nextRun.getTime() <= currentTime) {
        nextRun.setDate(nextRun.getDate() + 1);
    }

    return nextRun.getTime();
}

/**
 * Helper function để calculate next weekly run
 */
function calculateNextWeeklyRun(config: IWeeklyConfig, currentTime: number): number {
    const now = new Date(currentTime);
    const currentDay = now.getDay();

    // Tìm ngày tiếp theo trong tuần
    let nextDay = config.days_of_week.find(day => day > currentDay);

    if (nextDay === undefined) {
        // Không có ngày nào trong tuần này, lấy ngày đầu tiên của tuần sau
        nextDay = Math.min(...config.days_of_week);
        const daysUntilNext = (7 - currentDay) + nextDay;

        const nextRun = new Date(now);
        nextRun.setDate(nextRun.getDate() + daysUntilNext);
        nextRun.setHours(config.hour, config.minute, config.second || 0, 0);

        return nextRun.getTime();
    } else {
        const daysUntilNext = nextDay - currentDay;
        const nextRun = new Date(now);
        nextRun.setDate(nextRun.getDate() + daysUntilNext);
        nextRun.setHours(config.hour, config.minute, config.second || 0, 0);

        // Nếu là cùng ngày nhưng giờ đã qua
        if (daysUntilNext === 0 && nextRun.getTime() <= currentTime) {
            // Tìm ngày tiếp theo
            const remainingDays = config.days_of_week.filter(day => day > currentDay);
            if (remainingDays.length > 0) {
                const nextValidDay = Math.min(...remainingDays);
                nextRun.setDate(nextRun.getDate() + (nextValidDay - currentDay));
            } else {
                // Tuần sau
                const firstDayNextWeek = Math.min(...config.days_of_week);
                nextRun.setDate(nextRun.getDate() + (7 - currentDay + firstDayNextWeek));
            }
        }

        return nextRun.getTime();
    }
}

/**
 * Helper function để calculate next monthly run
 */
function calculateNextMonthlyRun(config: IMonthlyConfig, currentTime: number): number {
    const now = new Date(currentTime);
    let nextRun = new Date(now);

    // Set target day of month
    if (config.day_of_month === 'last') {
        // Last day of month
        nextRun.setMonth(nextRun.getMonth() + 1, 0); // 0 = last day of previous month
    } else {
        nextRun.setDate(config.day_of_month);
    }

    nextRun.setHours(config.hour, config.minute, config.second || 0, 0);

    // Nếu thời gian đã qua trong tháng này, chuyển sang tháng sau
    if (nextRun.getTime() <= currentTime) {
        if (config.day_of_month === 'last') {
            nextRun.setMonth(nextRun.getMonth() + 2, 0);
        } else {
            nextRun.setMonth(nextRun.getMonth() + 1, config.day_of_month);
        }
        nextRun.setHours(config.hour, config.minute, config.second || 0, 0);
    }

    // Check months filter
    if (config.months && config.months.length > 0) {
        while (!config.months.includes(nextRun.getMonth() + 1)) {
            if (config.day_of_month === 'last') {
                nextRun.setMonth(nextRun.getMonth() + 2, 0);
            } else {
                nextRun.setMonth(nextRun.getMonth() + 1, config.day_of_month);
            }
            nextRun.setHours(config.hour, config.minute, config.second || 0, 0);
        }
    }

    return nextRun.getTime();
}

/**
 * Helper function để validate schedule parameters
 */
export function validateScheduleParameters(params: Partial<IScheduleParameters>): {
    isValid: boolean;
    errors: string[];
} {
    const errors: string[] = [];

    if (!params.schedule_type) {
        errors.push('Schedule type is required');
    }

    if (!params.name || params.name.trim().length === 0) {
        errors.push('Schedule name is required');
    }

    if (!params.execution_behavior) {
        errors.push('Execution behavior is required');
    }

    // Validate specific schedule configurations
    switch (params.schedule_type) {
        case EScheduleType.ONCE:
            if (!params.once_config?.datetime) {
                errors.push('DateTime is required for ONCE schedule');
            }
            break;

        case EScheduleType.CRON:
            if (!params.cron_config?.expression) {
                errors.push('Cron expression is required for CRON schedule');
            } else {
                const cronValidation = validateCronExpression(params.cron_config.expression);
                if (!cronValidation.isValid) {
                    errors.push(`Invalid cron expression: ${cronValidation.error}`);
                }
            }
            break;

        case EScheduleType.INTERVAL:
            if (!params.interval_config) {
                errors.push('Interval configuration is required for INTERVAL schedule');
            } else {
                if (!params.interval_config.value || params.interval_config.value <= 0) {
                    errors.push('Interval value must be > 0');
                }
            }
            break;

        case EScheduleType.DAILY:
            if (!params.daily_config) {
                errors.push('Daily configuration is required for DAILY schedule');
            } else {
                const config = params.daily_config;
                if (config.hour < 0 || config.hour > 23) {
                    errors.push('Hour must be between 0-23');
                }
                if (config.minute < 0 || config.minute > 59) {
                    errors.push('Minute must be between 0-59');
                }
                if (config.second !== undefined && (config.second < 0 || config.second > 59)) {
                    errors.push('Second must be between 0-59');
                }
            }
            break;

        case EScheduleType.WEEKLY:
            if (!params.weekly_config) {
                errors.push('Weekly configuration is required for WEEKLY schedule');
            } else {
                const config = params.weekly_config;
                if (!config.days_of_week || config.days_of_week.length === 0) {
                    errors.push('At least one day of week is required');
                }
                if (config.hour < 0 || config.hour > 23) {
                    errors.push('Hour must be between 0-23');
                }
                if (config.minute < 0 || config.minute > 59) {
                    errors.push('Minute must be between 0-59');
                }
            }
            break;

        case EScheduleType.MONTHLY:
            if (!params.monthly_config) {
                errors.push('Monthly configuration is required for MONTHLY schedule');
            } else {
                const config = params.monthly_config;
                if (config.day_of_month !== 'last' &&
                    (config.day_of_month < 1 || config.day_of_month > 31)) {
                    errors.push('Day of month must be between 1-31 or "last"');
                }
                if (config.hour < 0 || config.hour > 23) {
                    errors.push('Hour must be between 0-23');
                }
                if (config.minute < 0 || config.minute > 59) {
                    errors.push('Minute must be between 0-59');
                }
            }
            break;
    }

    // Validate execution behavior specific requirements
    if (params.execution_behavior === EExecutionBehavior.START_FROM_NODE && !params.target_node_id) {
        errors.push('Target node ID is required for START_FROM_NODE behavior');
    }

    if (params.execution_behavior === EExecutionBehavior.TRIGGER_EVENT && !params.event_name) {
        errors.push('Event name is required for TRIGGER_EVENT behavior');
    }

    // Validate dates
    if (params.start_date && params.end_date) {
        const startTime = new Date(params.start_date).getTime();
        const endTime = new Date(params.end_date).getTime();

        if (startTime >= endTime) {
            errors.push('End date must be after start date');
        }
    }

    // Validate numeric values
    if (params.max_executions !== undefined && params.max_executions <= 0) {
        errors.push('Max executions must be > 0');
    }

    if (params.execution_timeout !== undefined && params.execution_timeout <= 0) {
        errors.push('Execution timeout must be > 0');
    }

    if (params.max_concurrent_executions !== undefined && params.max_concurrent_executions <= 0) {
        errors.push('Max concurrent executions must be > 0');
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}
