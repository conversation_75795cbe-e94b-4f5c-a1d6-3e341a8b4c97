/**
 * @file Facebook Campaign Management Module Exports
 *
 * Export tất cả interfaces, types, và functions cho Facebook Campaign Management integration
 *
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

// Export Types & Enums
export {
    EFacebookCampaignManagementOperation,
    EFacebookCampaignObjective,
    EFacebookStatus,
    EFacebookOptimizationGoal,
    EFacebookBillingEvent,
    EFacebookSpecialAdCategory,
    EFacebookEffectiveStatus,
    EFacebookLocationType
} from './facebook-campaign-management.types';

// Export Interfaces
export {
    IFacebookCampaignManagementInput,
    IFacebookCampaignManagementOutput,
    IListCampaignsParameters,
    IUpdateCampaignParameters,
    IListAdSetsParameters,
    IUpdateAdSetParameters,
    IListAdsParameters,
    IUpdateAdParameters,
    IGetReachEstimateParameters,
    ISearchAdInterestsParameters,
    ISearchLocationsParameters,
    IFacebookCampaignManagementParameters
} from './facebook-campaign-management.interface';

// Export Node Properties
export {
    FACEBOOK_CAMPAIGN_MANAGEMENT_PROPERTIES
} from './facebook-campaign-management.properties';

// Export Validation & Credentials
export {
    FACEBOOK_CAMPAIGN_MANAGEMENT_CREDENTIAL,
    validateFacebookCampaignManagementParameters,
    isFacebookCampaignManagementParameters,
    IFacebookCampaignManagementNodeExecution
} from './facebook-campaign-management.validation';
