import { IScheduleParameters } from './utility/schedule.interface';
/**
 * @file Core interfaces index - Export tất cả node interfaces
 * 
 * File này tập hợp và export tất cả các interface của các node types
 * để dễ dàng import và sử dụng trong các module khác
 * 
 */

// =================================================================
// SHARED LOGIC
// =================================================================
export * from './shared/condition-evaluation.interface';

// =================================================================
// AI NODES
// =================================================================

// =================================================================
// HTTP NODES
// =================================================================
export * from './http/http-request.interface';

// =================================================================
// LOGIC NODES
// =================================================================
export * from './logic/if-condition.interface';
export * from './logic/loop.interface';
export * from './logic/switch.interface';

// =================================================================
// TRANSFORM NODES
// =================================================================
export * from './transform/edit-fields.interface';
export * from './transform/filter.interface';
export * from './transform/merge.interface';

// =================================================================
// UTILITY NODES
// =================================================================
export * from './utility/schedule.interface';
export * from './utility/wait.interface';

// =================================================================
// NODE REGISTRY
// Registry tất cả các node types để dễ dàng quản lý
// =================================================================


import {
    IHttpRequestParameters,
    validateHttpRequestParameters
} from './http/http-request.interface';

import {
    IIfConditionParameters,
    validateIfConditionParameters
} from './logic/if-condition.interface';

import {
    ISwitchParameters,
    validateSwitchParameters
} from './logic/switch.interface';

import {
    ILoopParameters,
    validateLoopParameters
} from './logic/loop.interface';

import {
    IMergeParameters,
    validateMergeParameters
} from './transform/merge.interface';

import {
    IWaitParameters,
    validateWaitParameters
} from './utility/wait.interface';

import {
    validateScheduleParameters
} from './utility/schedule.interface';

import {
    IFilterParameters,
    validateFilterParameters
} from './transform/filter.interface';

import { ENodeType } from '../node-manager.interface';
import {
    IEditFieldsParameters,
    validateEditFieldsParameters
} from './transform/edit-fields.interface';

/**
 * Union type của tất cả node parameters
 * Đây là type chính được sử dụng trong entity để đảm bảo type safety
 */
export type AllCoreNodeParameters =
    | IHttpRequestParameters
    | IIfConditionParameters
    | ISwitchParameters
    | ILoopParameters
    | IMergeParameters
    | IWaitParameters
    | IScheduleParameters
    | IFilterParameters
    | IEditFieldsParameters;

/**
 * Node validation functions registry
 */
export const NODE_VALIDATORS: {
    [K in ENodeType]?: (params: Partial<AllCoreNodeParameters>) => { isValid: boolean; errors: string[] };
} = {
    [ENodeType.HTTP_REQUEST]: validateHttpRequestParameters,
    [ENodeType.IF_CONDITION]: validateIfConditionParameters,
    [ENodeType.SWITCH]: validateSwitchParameters,
    [ENodeType.LOOP]: validateLoopParameters,
    [ENodeType.MERGE]: validateMergeParameters,
    [ENodeType.WAIT]: validateWaitParameters,
    [ENodeType.SCHEDULE]: validateScheduleParameters,
    [ENodeType.FILTER]: validateFilterParameters,
    [ENodeType.EDIT_FIELDS]: validateEditFieldsParameters
} as const;

/**
 * Node types registry - Sử dụng enum values trực tiếp
 */
export const NODE_TYPES = {
    HTTP_REQUEST: ENodeType.HTTP_REQUEST,
    IF_CONDITION: ENodeType.IF_CONDITION,
    SWITCH: ENodeType.SWITCH,
    LOOP: ENodeType.LOOP,
    MERGE: ENodeType.MERGE,
    WAIT: ENodeType.WAIT,
    SCHEDULE: ENodeType.SCHEDULE,
    FILTER: ENodeType.FILTER,
    EDIT_FIELDS: ENodeType.EDIT_FIELDS
} as const;

/**
 * Helper function để validate node parameters theo type
 */
export function validateNodeParameters(
    nodeType: ENodeType,
    parameters: Partial<AllCoreNodeParameters>
): { isValid: boolean; errors: string[] } {
    const validator = NODE_VALIDATORS[nodeType];
    if (!validator) {
        throw new Error(`Unknown node type: ${nodeType}`);
    }
    return validator(parameters as any);
}

/**
 * Type guard để kiểm tra node parameters type
 */

export function isHttpRequestParameters(params: any): params is IHttpRequestParameters {
    return params && typeof params.url === 'string' && typeof params.method === 'string';
}

export function isIfConditionParameters(params: any): params is IIfConditionParameters {
    return params && (Array.isArray(params.condition_groups) || params.use_custom_expression);
}

export function isSwitchParameters(params: any): params is ISwitchParameters {
    return params && typeof params.mode === 'string' && Array.isArray(params.cases);
}

export function isLoopParameters(params: any): params is ILoopParameters {
    return params && typeof params.type === 'string' && typeof params.max_iterations === 'number';
}

export function isMergeParameters(params: any): params is IMergeParameters {
    return params && typeof params.merge_type === 'string' && Array.isArray(params.input_sources);
}

export function isWaitParameters(params: any): params is IWaitParameters {
    return params && typeof params.wait_type === 'string';
}

export function isScheduleParameters(params: any): params is IScheduleParameters {
    return params && typeof params.schedule_type === 'string' && typeof params.name === 'string';
}

export function isFilterParameters(params: any): params is IFilterParameters {
    return params && typeof params.filter_type === 'string' && typeof params.source_field === 'string';
}

export function isEditFieldsParameters(params: any): params is IEditFieldsParameters {
    return params && Array.isArray(params.field_operations);
}

/**
 * Helper function để get node type từ parameters
 */
export function getNodeTypeFromParameters(params: AllCoreNodeParameters): ENodeType {
    if (isHttpRequestParameters(params)) return NODE_TYPES.HTTP_REQUEST;
    if (isIfConditionParameters(params)) return NODE_TYPES.IF_CONDITION;
    if (isSwitchParameters(params)) return NODE_TYPES.SWITCH;
    if (isLoopParameters(params)) return NODE_TYPES.LOOP;
    if (isMergeParameters(params)) return NODE_TYPES.MERGE;
    if (isWaitParameters(params)) return NODE_TYPES.WAIT;
    if (isScheduleParameters(params)) return NODE_TYPES.SCHEDULE;
    if (isFilterParameters(params)) return NODE_TYPES.FILTER;
    if (isEditFieldsParameters(params)) return NODE_TYPES.EDIT_FIELDS;
    throw new Error('Unknown parameters type');
}
