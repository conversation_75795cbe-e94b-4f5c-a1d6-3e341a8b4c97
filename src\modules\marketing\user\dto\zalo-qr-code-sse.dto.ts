import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsNumber } from 'class-validator';

/**
 * DTO cho tạo QR code session
 */
export class CreateQRCodeSessionDto {
  @ApiProperty({
    description: 'Integration ID của Zalo OA',
    example: 'uuid-integration-id',
  })
  @IsString()
  @IsNotEmpty()
  integrationId: string;
}

/**
 * DTO cho response tạo QR code session
 */
export class QRCodeSessionResponseDto {
  @ApiProperty({
    description: 'Session ID để tracking',
    example: 'session-uuid-123',
  })
  sessionId: string;

  @ApiProperty({
    description: 'QR code dưới dạng base64',
    example: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...',
  })
  qrCodeBase64: string;

  @ApiProperty({
    description: '<PERSON>h<PERSON><PERSON> gian hế<PERSON> hạn (timestamp)',
    example: 1640995230,
  })
  expiresAt: number;

  @ApiProperty({
    description: 'Integration ID từ request',
    example: 'uuid-integration-id',
  })
  integrationId: string;

  @ApiPropertyOptional({
    description: 'Thông báo',
    example: 'Session đã được tạo thành công',
  })
  message?: string;
}

/**
 * DTO cho SSE event data
 */
export class QRCodeSseEventDto {
  @ApiProperty({
    description: 'Loại event',
    enum: ['qr_generated', 'login_success', 'qr_expired', 'heartbeat', 'error'],
    example: 'login_success',
  })
  event: string;

  @ApiProperty({
    description: 'Dữ liệu của event',
    example: {
      session_id: 'session-uuid-123',
      zalo_uid: 'zalo-user-123',
      user_info: {
        name: 'Nguyễn Văn A',
        avatar: 'https://...',
      },
    },
  })
  data: Record<string, any>;

  @ApiProperty({
    description: 'Timestamp của event',
    example: '2024-01-01T10:00:00.000Z',
  })
  timestamp: string;
}

/**
 * DTO cho session status
 */
export class QRCodeSessionStatusDto {
  @ApiProperty({
    description: 'Session ID',
    example: 'session-uuid-123',
  })
  sessionId: string;

  @ApiProperty({
    description: 'Trạng thái session',
    enum: ['pending', 'success', 'expired', 'error'],
    example: 'pending',
  })
  status: string;

  @ApiProperty({
    description: 'Integration ID',
    example: 'uuid-integration-id',
  })
  integrationId: string;

  @ApiProperty({
    description: 'Thời gian tạo',
    example: '2024-01-01T10:00:00.000Z',
  })
  createdAt: string;

  @ApiProperty({
    description: 'Thời gian hết hạn',
    example: '2024-01-01T10:00:30.000Z',
  })
  expiresAt: string;
}

/**
 * Interface cho SSE message
 */
export interface QRCodeSseMessage {
  id: string;
  event: string;
  data: string;
  retry?: number;
}
