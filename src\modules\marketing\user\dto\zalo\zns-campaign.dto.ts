import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  Min,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ZaloZnsCampaignStatus } from '../../entities/zalo-zns-campaign.entity';
import { QueryDto } from '@/common/dto/query.dto';
import { BatchZnsMessageDto } from './send-zns-job.dto';

/**
 * Enum cho loại đối tượng đầu ra
 */
export enum TargetAudienceType {
  PHONE_LIST = 'PHONE_LIST',
  SEGMENT = 'SEGMENT',
  AUDIENCE_LIST = 'AUDIENCE_LIST',
}

/**
 * DTO cho tạo chiến dịch ZNS
 */
export class CreateZnsCampaignDto {
  @ApiProperty({
    description: 'ID Integration của Zalo OA',
    example: 'uuid-integration-id',
  })
  @IsString()
  @IsNotEmpty()
  integrationId: string;

  @ApiProperty({
    description: 'Tên chiến dịch',
    example: 'Chiến dịch thông báo đơn hàng',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiPropertyOptional({
    description: 'Mô tả chiến dịch',
    example: 'Gửi thông báo đơn hàng cho khách hàng',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'ID template ZNS',
    example: 'template123456789',
  })
  @IsString()
  @IsNotEmpty()
  templateId: string;

  @ApiProperty({
    description: 'Dữ liệu cho template',
    example: {
      shopName: 'RedAI Shop',
      orderStatus: 'Đã xác nhận',
    },
  })
  @IsObject()
  @IsNotEmpty()
  templateData: Record<string, any>;

  @ApiProperty({
    description: 'Loại đối tượng đầu ra',
    enum: TargetAudienceType,
    example: TargetAudienceType.PHONE_LIST,
  })
  @IsEnum(TargetAudienceType)
  targetAudienceType: TargetAudienceType;

  @ApiPropertyOptional({
    description:
      'Danh sách số điện thoại (bắt buộc khi targetAudienceType = PHONE_LIST)',
    example: ['0912345678', '0987654321'],
  })
  @ValidateIf((o) => o.targetAudienceType === TargetAudienceType.PHONE_LIST)
  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty({ each: true })
  phoneList?: string[];

  @ApiPropertyOptional({
    description: 'ID segment (bắt buộc khi targetAudienceType = SEGMENT)',
    example: 123,
  })
  @ValidateIf((o) => o.targetAudienceType === TargetAudienceType.SEGMENT)
  @IsNumber()
  segmentId?: number;

  @ApiPropertyOptional({
    description:
      'Danh sách ID audience (bắt buộc khi targetAudienceType = AUDIENCE_LIST)',
    example: [1, 2, 3],
  })
  @ValidateIf((o) => o.targetAudienceType === TargetAudienceType.AUDIENCE_LIST)
  @IsArray()
  @IsNumber({}, { each: true })
  audienceIds?: number[];

  @ApiProperty({
    description: 'Trạng thái chiến dịch',
    enum: ZaloZnsCampaignStatus,
    example: ZaloZnsCampaignStatus.DRAFT,
  })
  @IsEnum(ZaloZnsCampaignStatus)
  status: ZaloZnsCampaignStatus;

  @ApiPropertyOptional({
    description: 'Thời gian lên lịch gửi (Unix timestamp)',
    example: 1640995200000,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  scheduledAt?: number;
}

/**
 * DTO cho cập nhật chiến dịch ZNS
 */
export class UpdateZnsCampaignDto {
  @ApiPropertyOptional({
    description: 'Tên chiến dịch',
    example: 'Chiến dịch thông báo đơn hàng (cập nhật)',
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  name?: string;

  @ApiPropertyOptional({
    description: 'Mô tả chiến dịch',
    example: 'Gửi thông báo đơn hàng cho khách hàng VIP',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'ID template ZNS',
    example: 'template987654321',
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  templateId?: string;

  @ApiPropertyOptional({
    description: 'Dữ liệu cho template',
    example: {
      shopName: 'RedAI Shop Pro',
      orderStatus: 'Đang xử lý',
    },
  })
  @IsOptional()
  @IsObject()
  templateData?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Danh sách số điện thoại',
    example: ['0912345678', '0987654321', '0123456789'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty({ each: true })
  phoneList?: string[];

  @ApiPropertyOptional({
    description: 'Thời gian lên lịch gửi (Unix timestamp)',
    example: 1640995200000,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  scheduledAt?: number;
}

/**
 * DTO cho query danh sách chiến dịch ZNS
 */
export class ZnsCampaignQueryDto extends QueryDto {
  @ApiPropertyOptional({
    description: 'Trạng thái chiến dịch',
    enum: ZaloZnsCampaignStatus,
    example: ZaloZnsCampaignStatus.SENT,
  })
  @IsOptional()
  @IsEnum(ZaloZnsCampaignStatus)
  status?: ZaloZnsCampaignStatus;

  @ApiPropertyOptional({
    description: 'ID Integration (optional filter)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsString()
  integrationId?: string;
}

/**
 * DTO cho response chiến dịch ZNS
 */
export class ZnsCampaignResponseDto {
  @ApiProperty({
    description: 'ID chiến dịch',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'ID người dùng',
    example: 123,
  })
  userId: number;

  @ApiProperty({
    description: 'ID Official Account',
    example: 'oa123456789',
  })
  oaId: string;

  @ApiPropertyOptional({
    description: 'ID Integration (UUID)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  integrationId?: string;

  @ApiProperty({
    description: 'Tên chiến dịch',
    example: 'Chiến dịch thông báo đơn hàng',
  })
  name: string;

  @ApiPropertyOptional({
    description: 'Mô tả chiến dịch',
    example: 'Gửi thông báo đơn hàng cho khách hàng',
  })
  description?: string;

  @ApiProperty({
    description: 'ID template ZNS',
    example: 'template123456789',
  })
  templateId: string;

  @ApiProperty({
    description:
      'Dữ liệu cho template (có thể là object chung hoặc function để cá nhân hóa)',
    example: {
      shopName: 'RedAI Shop',
      orderStatus: 'Đã xác nhận',
    },
  })
  templateData: Record<string, any>;

  @ApiPropertyOptional({
    description:
      'Danh sách số điện thoại (khi targetAudienceType = PHONE_LIST)',
    example: ['0912345678', '0987654321'],
  })
  phoneList?: string[];

  @ApiPropertyOptional({
    description: 'ID segment (khi targetAudienceType = SEGMENT)',
    example: 123,
  })
  segmentId?: number;

  @ApiPropertyOptional({
    description:
      'Danh sách ID audience (khi targetAudienceType = AUDIENCE_LIST)',
    example: [1, 2, 3],
  })
  audienceIds?: number[];

  @ApiProperty({
    description: 'Trạng thái chiến dịch',
    enum: ZaloZnsCampaignStatus,
    example: ZaloZnsCampaignStatus.SENT,
  })
  status: ZaloZnsCampaignStatus;

  @ApiPropertyOptional({
    description: 'Thời gian lên lịch gửi (Unix timestamp)',
    example: 1640995200000,
  })
  scheduledAt?: number;

  @ApiPropertyOptional({
    description: 'Thời gian bắt đầu (Unix timestamp)',
    example: 1640995200000,
  })
  startedAt?: number;

  @ApiPropertyOptional({
    description: 'Thời gian hoàn thành (Unix timestamp)',
    example: 1640995800000,
  })
  completedAt?: number;

  @ApiProperty({
    description: 'Tổng số tin nhắn',
    example: 100,
  })
  totalMessages: number;

  @ApiProperty({
    description: 'Số tin nhắn đã gửi',
    example: 95,
  })
  sentMessages: number;

  @ApiProperty({
    description: 'Số tin nhắn thất bại',
    example: 5,
  })
  failedMessages: number;

  @ApiPropertyOptional({
    description: 'Thông báo lỗi (nếu có)',
    example: 'Template không hợp lệ',
  })
  errorMessage?: string;

  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1640995000000,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật (Unix timestamp)',
    example: 1640995800000,
  })
  updatedAt: number;
}

/**
 * DTO cho cá nhân hóa template data
 */
export class PersonalizedTemplateDataDto {
  @ApiProperty({
    description: 'Có sử dụng cá nhân hóa hay không',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  usePersonalization?: boolean;

  @ApiProperty({
    description: 'Template data chung (khi không cá nhân hóa)',
    example: {
      shopName: 'RedAI Shop',
      orderStatus: 'Đã xác nhận',
    },
  })
  @IsObject()
  @IsOptional()
  commonData?: Record<string, any>;

  @ApiProperty({
    description: 'Mapping từ trường audience sang template variable',
    example: {
      customerName: 'name',
      customerEmail: 'email',
      customerPhone: 'phoneNumber',
      customerAddress: 'address',
    },
  })
  @IsObject()
  @IsOptional()
  fieldMapping?: Record<string, string>;

  @ApiProperty({
    description:
      'Custom fields mapping (từ custom field name sang template variable)',
    example: {
      orderCode: 'order_code',
      totalAmount: 'total_amount',
    },
  })
  @IsObject()
  @IsOptional()
  customFieldMapping?: Record<string, string>;
}

/**
 * DTO cho tạo chiến dịch ZNS với cá nhân hóa
 */
export class CreatePersonalizedZnsCampaignDto {
  @ApiProperty({
    description: 'ID Integration của Zalo OA',
    example: 'uuid-integration-id',
  })
  @IsString()
  @IsNotEmpty()
  integrationId: string;

  @ApiProperty({
    description: 'Tên chiến dịch',
    example: 'Chiến dịch thông báo đơn hàng cá nhân hóa',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiPropertyOptional({
    description: 'Mô tả chiến dịch',
    example:
      'Gửi thông báo đơn hàng với thông tin cá nhân hóa cho từng khách hàng',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'ID template ZNS',
    example: 'template123456789',
  })
  @IsString()
  @IsNotEmpty()
  templateId: string;

  @ApiProperty({
    description: 'Cấu hình cá nhân hóa template data',
    type: PersonalizedTemplateDataDto,
  })
  @ValidateNested()
  @Type(() => PersonalizedTemplateDataDto)
  @IsObject()
  personalizedTemplateData: PersonalizedTemplateDataDto;

  @ApiProperty({
    description: 'Loại đối tượng đầu ra',
    enum: TargetAudienceType,
    example: TargetAudienceType.SEGMENT,
  })
  @IsEnum(TargetAudienceType)
  targetAudienceType: TargetAudienceType;

  @ApiPropertyOptional({
    description:
      'Danh sách số điện thoại (khi targetAudienceType = PHONE_LIST)',
    example: ['0912345678', '0987654321'],
  })
  @ValidateIf((o) => o.targetAudienceType === TargetAudienceType.PHONE_LIST)
  @IsArray()
  @IsString({ each: true })
  phoneList?: string[];

  @ApiPropertyOptional({
    description: 'ID segment (khi targetAudienceType = SEGMENT)',
    example: 123,
  })
  @ValidateIf((o) => o.targetAudienceType === TargetAudienceType.SEGMENT)
  @IsNumber()
  segmentId?: number;

  @ApiPropertyOptional({
    description:
      'Danh sách ID audience (khi targetAudienceType = AUDIENCE_LIST)',
    example: [1, 2, 3],
  })
  @ValidateIf((o) => o.targetAudienceType === TargetAudienceType.AUDIENCE_LIST)
  @IsArray()
  @IsNumber({}, { each: true })
  audienceIds?: number[];

  @ApiPropertyOptional({
    description: 'Trạng thái chiến dịch',
    enum: ZaloZnsCampaignStatus,
    example: ZaloZnsCampaignStatus.DRAFT,
  })
  @IsOptional()
  @IsEnum(ZaloZnsCampaignStatus)
  status?: ZaloZnsCampaignStatus;

  @ApiPropertyOptional({
    description: 'Thời gian lên lịch gửi (Unix timestamp)',
    example: 1640995200000,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  scheduledAt?: number;
}

/**
 * Enum cho loại chiến dịch ZNS thống nhất
 */
export enum ZnsCampaignType {
  CAMPAIGN = 'CAMPAIGN', // Tạo campaign thông thường
  PERSONALIZED_CAMPAIGN = 'PERSONALIZED_CAMPAIGN', // Tạo campaign cá nhân hóa
  SINGLE_MESSAGE = 'SINGLE_MESSAGE', // Gửi tin nhắn đơn lẻ
  BATCH_MESSAGE = 'BATCH_MESSAGE', // Gửi batch tin nhắn
}

/**
 * DTO thống nhất cho tạo chiến dịch ZNS
 */
export class UnifiedCreateZnsCampaignDto {
  @ApiProperty({
    description: 'ID Integration của Zalo OA',
    example: 'uuid-integration-id',
  })
  @IsString()
  @IsNotEmpty()
  integrationId: string;

  @ApiProperty({
    description: 'Loại chiến dịch ZNS',
    enum: ZnsCampaignType,
    example: ZnsCampaignType.CAMPAIGN,
  })
  @IsEnum(ZnsCampaignType)
  campaignType: ZnsCampaignType;

  // === Trường cho CAMPAIGN và PERSONALIZED_CAMPAIGN ===
  @ApiPropertyOptional({
    description:
      'Tên chiến dịch (bắt buộc cho CAMPAIGN và PERSONALIZED_CAMPAIGN)',
    example: 'Chiến dịch thông báo đơn hàng',
  })
  @ValidateIf(
    (o) =>
      o.campaignType === ZnsCampaignType.CAMPAIGN ||
      o.campaignType === ZnsCampaignType.PERSONALIZED_CAMPAIGN,
  )
  @IsString()
  @IsNotEmpty()
  name?: string;

  @ApiPropertyOptional({
    description: 'Mô tả chiến dịch',
    example: 'Gửi thông báo đơn hàng cho khách hàng',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description:
      'Trạng thái chiến dịch (cho CAMPAIGN và PERSONALIZED_CAMPAIGN)',
    enum: ZaloZnsCampaignStatus,
    example: ZaloZnsCampaignStatus.DRAFT,
  })
  @ValidateIf(
    (o) =>
      o.campaignType === ZnsCampaignType.CAMPAIGN ||
      o.campaignType === ZnsCampaignType.PERSONALIZED_CAMPAIGN,
  )
  @IsOptional()
  @IsEnum(ZaloZnsCampaignStatus)
  status?: ZaloZnsCampaignStatus;

  @ApiPropertyOptional({
    description: 'Thời gian lên lịch gửi (Unix timestamp)',
    example: 1640995200000,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  scheduledAt?: number;

  @ApiPropertyOptional({
    description:
      'Loại đối tượng đầu ra (cho CAMPAIGN và PERSONALIZED_CAMPAIGN)',
    enum: TargetAudienceType,
    example: TargetAudienceType.SEGMENT,
  })
  @ValidateIf(
    (o) =>
      o.campaignType === ZnsCampaignType.CAMPAIGN ||
      o.campaignType === ZnsCampaignType.PERSONALIZED_CAMPAIGN,
  )
  @IsOptional()
  @IsEnum(TargetAudienceType)
  targetAudienceType?: TargetAudienceType;

  @ApiPropertyOptional({
    description:
      'Danh sách số điện thoại (khi targetAudienceType = PHONE_LIST)',
    example: ['0912345678', '0987654321'],
  })
  @ValidateIf((o) => o.targetAudienceType === TargetAudienceType.PHONE_LIST)
  @IsArray()
  @IsString({ each: true })
  phoneList?: string[];

  @ApiPropertyOptional({
    description: 'ID segment (khi targetAudienceType = SEGMENT)',
    example: 123,
  })
  @ValidateIf((o) => o.targetAudienceType === TargetAudienceType.SEGMENT)
  @IsNumber()
  segmentId?: number;

  @ApiPropertyOptional({
    description:
      'Danh sách ID audience (khi targetAudienceType = AUDIENCE_LIST)',
    example: [1, 2, 3],
  })
  @ValidateIf((o) => o.targetAudienceType === TargetAudienceType.AUDIENCE_LIST)
  @IsArray()
  @IsNumber({}, { each: true })
  audienceIds?: number[];

  // === Trường cho tất cả loại ===
  @ApiProperty({
    description: 'ID template ZNS',
    example: 'template123456789',
  })
  @IsString()
  @IsNotEmpty()
  templateId: string;

  @ApiPropertyOptional({
    description: 'Dữ liệu cho template (cho CAMPAIGN, SINGLE_MESSAGE)',
    example: {
      shopName: 'RedAI Shop',
      orderStatus: 'Đã xác nhận',
    },
  })
  @ValidateIf(
    (o) =>
      o.campaignType === ZnsCampaignType.CAMPAIGN ||
      o.campaignType === ZnsCampaignType.SINGLE_MESSAGE,
  )
  @IsObject()
  templateData?: Record<string, any>;

  // === Trường cho PERSONALIZED_CAMPAIGN ===
  @ApiPropertyOptional({
    description:
      'Cấu hình cá nhân hóa template data (cho PERSONALIZED_CAMPAIGN)',
    type: PersonalizedTemplateDataDto,
  })
  @ValidateIf((o) => o.campaignType === ZnsCampaignType.PERSONALIZED_CAMPAIGN)
  @ValidateNested()
  @Type(() => PersonalizedTemplateDataDto)
  @IsObject()
  personalizedTemplateData?: PersonalizedTemplateDataDto;

  // === Trường cho SINGLE_MESSAGE ===
  @ApiPropertyOptional({
    description: 'Số điện thoại nhận (bắt buộc cho SINGLE_MESSAGE)',
    example: '0912345678',
  })
  @ValidateIf((o) => o.campaignType === ZnsCampaignType.SINGLE_MESSAGE)
  @IsString()
  @IsNotEmpty()
  phone?: string;

  @ApiPropertyOptional({
    description: 'Tracking ID (cho SINGLE_MESSAGE)',
    example: 'tracking_12345',
  })
  @IsOptional()
  @IsString()
  trackingId?: string;

  // === Trường cho BATCH_MESSAGE ===
  @ApiPropertyOptional({
    description: 'Danh sách tin nhắn ZNS (bắt buộc cho BATCH_MESSAGE)',
    type: [BatchZnsMessageDto],
  })
  @ValidateIf((o) => o.campaignType === ZnsCampaignType.BATCH_MESSAGE)
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BatchZnsMessageDto)
  messages?: BatchZnsMessageDto[];

  @ApiPropertyOptional({
    description: 'Batch index (thứ tự batch, cho BATCH_MESSAGE)',
    example: 0,
  })
  @IsOptional()
  @IsNumber()
  batchIndex?: number;

  @ApiPropertyOptional({
    description: 'Tổng số batch (cho BATCH_MESSAGE)',
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  totalBatches?: number;
}

/**
 * DTO cho response thống nhất của chiến dịch ZNS
 */
export class UnifiedZnsCampaignResponseDto {
  @ApiProperty({
    description: 'Loại chiến dịch ZNS',
    enum: ZnsCampaignType,
    example: ZnsCampaignType.CAMPAIGN,
  })
  campaignType: ZnsCampaignType;

  @ApiPropertyOptional({
    description: 'Thông tin chiến dịch (cho CAMPAIGN và PERSONALIZED_CAMPAIGN)',
    type: ZnsCampaignResponseDto,
  })
  campaign?: ZnsCampaignResponseDto;

  @ApiPropertyOptional({
    description: 'Job ID (cho SINGLE_MESSAGE và BATCH_MESSAGE)',
    example: 'job_12345',
  })
  jobId?: string;

  @ApiPropertyOptional({
    description: 'Thông báo thành công',
    example: 'Tạo chiến dịch ZNS thành công',
  })
  message?: string;
}
