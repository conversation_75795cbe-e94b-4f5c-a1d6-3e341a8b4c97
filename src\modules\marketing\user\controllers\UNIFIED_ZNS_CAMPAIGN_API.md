# API Thống Nhất Cho ZNS Campaigns

## Tổng quan

Đã gộp 4 API tạo ZNS campaigns thành 1 API duy nhất để đơn giản hóa việc sử dụng:

### API Cũ (Deprecated)
- `POST /v1/marketing/zalo/zns/zns-campaigns` - Tạo campaign thông thường
- `POST /v1/marketing/zalo/zns/zns-campaigns/personalized` - Tạo campaign cá nhân hóa  
- `POST /v1/marketing/zalo/zns/zns-campaigns/send-single-zns` - Gửi tin nhắn đơn lẻ
- `POST /v1/marketing/zalo/zns/zns-campaigns/send-batch-zns` - Gửi batch tin nhắn

### API Mới (Thống nhất)
- `POST /v1/marketing/zalo/zns/zns-campaigns/unified` - API duy nhất cho tất cả loại

## Cách sử dụng API Thống nhất

### 1. Tạo Campaign Thông thường

```json
{
  "integrationId": "uuid-integration-id",
  "campaignType": "CAMPAIGN",
  "name": "Thông báo đơn hàng",
  "description": "Gửi thông báo đơn hàng cho khách hàng",
  "templateId": "template_order_notification_123",
  "templateData": {
    "shopName": "RedAI Shop",
    "orderStatus": "Đã xác nhận"
  },
  "targetAudienceType": "SEGMENT",
  "segmentId": 123,
  "status": "DRAFT"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Tạo chiến dịch ZNS thành công",
  "data": {
    "campaignType": "CAMPAIGN",
    "campaign": {
      "id": 1,
      "name": "Thông báo đơn hàng",
      "status": "DRAFT",
      // ... other campaign fields
    }
  }
}
```

### 2. Tạo Campaign Cá nhân hóa

```json
{
  "integrationId": "uuid-integration-id",
  "campaignType": "PERSONALIZED_CAMPAIGN",
  "name": "Thông báo đơn hàng cá nhân hóa",
  "description": "Gửi thông báo đơn hàng với thông tin cá nhân hóa",
  "templateId": "template_order_notification_123",
  "personalizedTemplateData": {
    "usePersonalization": true,
    "commonData": {
      "shopName": "RedAI Shop"
    },
    "fieldMapping": {
      "customerName": "name",
      "customerEmail": "email"
    }
  },
  "targetAudienceType": "SEGMENT",
  "segmentId": 123,
  "status": "DRAFT"
}
```

### 3. Gửi Tin nhắn Đơn lẻ

```json
{
  "integrationId": "uuid-integration-id",
  "campaignType": "SINGLE_MESSAGE",
  "templateId": "template_order_notification_123",
  "templateData": {
    "shopName": "RedAI Shop",
    "customerName": "Nguyễn Văn A",
    "orderId": "ORD123456"
  },
  "phone": "0912345678",
  "trackingId": "tracking_12345"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Tạo job gửi ZNS đơn lẻ thành công",
  "data": {
    "campaignType": "SINGLE_MESSAGE",
    "jobId": "job_12345"
  }
}
```

### 4. Gửi Batch Tin nhắn

```json
{
  "integrationId": "uuid-integration-id",
  "campaignType": "BATCH_MESSAGE",
  "messages": [
    {
      "phone": "0912345678",
      "templateId": "template_order_notification_123",
      "templateData": {
        "shopName": "RedAI Shop",
        "customerName": "Nguyễn Văn A",
        "orderId": "ORD123456"
      },
      "trackingId": "tracking_001"
    },
    {
      "phone": "0987654321",
      "templateId": "template_order_notification_123",
      "templateData": {
        "shopName": "RedAI Shop",
        "customerName": "Trần Thị B",
        "orderId": "ORD123457"
      },
      "trackingId": "tracking_002"
    }
  ],
  "batchIndex": 0,
  "totalBatches": 1
}
```

## Enum ZnsCampaignType

```typescript
enum ZnsCampaignType {
  CAMPAIGN = 'CAMPAIGN',                     // Tạo campaign thông thường
  PERSONALIZED_CAMPAIGN = 'PERSONALIZED_CAMPAIGN', // Tạo campaign cá nhân hóa
  SINGLE_MESSAGE = 'SINGLE_MESSAGE',         // Gửi tin nhắn đơn lẻ
  BATCH_MESSAGE = 'BATCH_MESSAGE',           // Gửi batch tin nhắn
}
```

## Validation Rules

### CAMPAIGN & PERSONALIZED_CAMPAIGN
- `name`: Bắt buộc
- `targetAudienceType`: Bắt buộc
- `templateData`: Bắt buộc cho CAMPAIGN
- `personalizedTemplateData`: Bắt buộc cho PERSONALIZED_CAMPAIGN

### SINGLE_MESSAGE
- `phone`: Bắt buộc
- `templateData`: Bắt buộc

### BATCH_MESSAGE
- `messages`: Bắt buộc, array không rỗng

## Migration Guide

### Từ API cũ sang API mới:

1. **Tạo campaign thông thường:**
   ```diff
   - POST /v1/marketing/zalo/zns/zns-campaigns
   + POST /v1/marketing/zalo/zns/zns-campaigns/unified
   + "campaignType": "CAMPAIGN"
   ```

2. **Tạo campaign cá nhân hóa:**
   ```diff
   - POST /v1/marketing/zalo/zns/zns-campaigns/personalized
   + POST /v1/marketing/zalo/zns/zns-campaigns/unified
   + "campaignType": "PERSONALIZED_CAMPAIGN"
   ```

3. **Gửi tin nhắn đơn lẻ:**
   ```diff
   - POST /v1/marketing/zalo/zns/zns-campaigns/send-single-zns
   + POST /v1/marketing/zalo/zns/zns-campaigns/unified
   + "campaignType": "SINGLE_MESSAGE"
   ```

4. **Gửi batch tin nhắn:**
   ```diff
   - POST /v1/marketing/zalo/zns/zns-campaigns/send-batch-zns
   + POST /v1/marketing/zalo/zns/zns-campaigns/unified
   + "campaignType": "BATCH_MESSAGE"
   ```

## Lưu ý

- Các API cũ vẫn hoạt động nhưng đã được đánh dấu **deprecated**
- Nên chuyển sang sử dụng API thống nhất mới
- Response format khác nhau tùy theo `campaignType`:
  - CAMPAIGN/PERSONALIZED_CAMPAIGN: trả về `campaign` object
  - SINGLE_MESSAGE/BATCH_MESSAGE: trả về `jobId` string
