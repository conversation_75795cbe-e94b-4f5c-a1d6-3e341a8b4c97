# Schedule Node Architecture & Implementation Guide

## 🏗️ Kiến trúc tổng quan

### 1. Core Components

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Schedule Node  │───▶│ Scheduler Service│───▶│  Queue System   │
│   (Interface)   │    │   (Time Engine)  │    │   (Job Queue)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Database      │    │   Cron Parser   │    │ Workflow Engine │
│  (Schedules)    │    │  (Time Logic)   │    │  (Execution)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2. Technology Stack

#### A. Scheduler Engine
- **Node-cron**: Cron expression parsing và scheduling
- **Bull Queue**: Job queue management với Redis
- **Redis**: Message broker và job storage
- **TypeORM**: Database operations

#### B. Time Management
- **Luxon**: Advanced date/time manipulation
- **Timezone support**: IANA timezone database
- **Cron-parser**: Validate và parse cron expressions

#### C. Queue System
- **Bull/BullMQ**: Robust job queue với retry logic
- **Redis**: Persistent job storage
- **Worker processes**: Scalable job execution

## 🔧 Implementation Details

### 1. Scheduler Service

```typescript
@Injectable()
export class SchedulerService {
    private scheduledJobs = new Map<string, ScheduledTask>();
    
    constructor(
        private readonly queueService: QueueService,
        private readonly scheduleRepository: ScheduleRepository,
        private readonly cronParser: CronParser
    ) {}
    
    async registerSchedule(scheduleConfig: IScheduleParameters): Promise<void> {
        // Parse schedule configuration
        const nextRunTime = this.calculateNextRun(scheduleConfig);
        
        // Create scheduled task
        const task = this.createScheduledTask(scheduleConfig, nextRunTime);
        
        // Register with node-cron or custom scheduler
        this.scheduledJobs.set(scheduleConfig.id, task);
    }
    
    private calculateNextRun(config: IScheduleParameters): Date {
        switch (config.schedule_type) {
            case EScheduleType.CRON:
                return this.cronParser.parseExpression(config.cron_config.expression).next().toDate();
            case EScheduleType.DAILY:
                return this.calculateDailyNextRun(config.daily_config);
            // ... other types
        }
    }
}
```

### 2. Queue Integration

```typescript
@Injectable()
export class WorkflowQueueService {
    constructor(
        @InjectQueue('workflow-execution') 
        private workflowQueue: Queue
    ) {}
    
    async addScheduledExecution(scheduleId: string, config: IScheduleParameters): Promise<void> {
        const job = await this.workflowQueue.add(
            'execute-scheduled-workflow',
            {
                scheduleId,
                workflowId: config.workflowId,
                executionBehavior: config.execution_behavior,
                targetNodeId: config.target_node_id,
                inputData: config.input_data || {}
            },
            {
                attempts: config.retry_config?.max_retries || 1,
                backoff: {
                    type: 'exponential',
                    delay: config.retry_config?.retry_delay || 60000
                },
                timeout: config.execution_timeout || 300000
            }
        );
        
        this.logger.log(`Scheduled job added: ${job.id} for schedule: ${scheduleId}`);
    }
}
```

### 3. Database Schema

```sql
-- Schedules table
CREATE TABLE schedules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    workflow_id UUID NOT NULL REFERENCES workflows(id),
    node_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    schedule_type VARCHAR(50) NOT NULL,
    schedule_config JSONB NOT NULL,
    execution_behavior VARCHAR(50) NOT NULL,
    overlap_behavior VARCHAR(50) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    start_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE,
    max_executions INTEGER,
    execution_timeout INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Schedule executions table
CREATE TABLE schedule_executions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    schedule_id UUID NOT NULL REFERENCES schedules(id),
    workflow_execution_id UUID REFERENCES workflow_executions(id),
    scheduled_time TIMESTAMP WITH TIME ZONE NOT NULL,
    executed_time TIMESTAMP WITH TIME ZONE,
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    duration_ms INTEGER,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_schedules_active ON schedules(is_active) WHERE is_active = true;
CREATE INDEX idx_schedules_workflow ON schedules(workflow_id);
CREATE INDEX idx_schedule_executions_schedule ON schedule_executions(schedule_id);
CREATE INDEX idx_schedule_executions_status ON schedule_executions(status);
```

## 🚀 Implementation Steps

### Phase 1: Core Infrastructure
1. **Install dependencies**:
   ```bash
   npm install node-cron bull luxon cron-parser
   npm install @types/node-cron @types/cron-parser
   ```

2. **Setup Redis** (for Bull Queue):
   ```typescript
   // queue.module.ts
   @Module({
     imports: [
       BullModule.forRoot({
         redis: {
           host: process.env.REDIS_HOST || 'localhost',
           port: parseInt(process.env.REDIS_PORT) || 6379,
         },
       }),
       BullModule.registerQueue({
         name: 'workflow-execution',
       }),
     ],
   })
   export class QueueModule {}
   ```

### Phase 2: Scheduler Service
1. **Create SchedulerService**
2. **Implement time calculation logic**
3. **Add cron expression validation**
4. **Handle timezone conversions**

### Phase 3: Queue Integration
1. **Setup Bull queues**
2. **Create job processors**
3. **Implement retry logic**
4. **Add monitoring và logging**

### Phase 4: Database Integration
1. **Create entities và repositories**
2. **Implement CRUD operations**
3. **Add execution tracking**
4. **Setup cleanup jobs**

## 🔄 Execution Flow

### 1. Schedule Registration
```typescript
// When workflow is activated
async activateWorkflow(workflowId: string) {
    const scheduleNodes = await this.getScheduleNodes(workflowId);
    
    for (const node of scheduleNodes) {
        await this.schedulerService.registerSchedule({
            id: node.id,
            workflowId: workflowId,
            ...node.parameters
        });
    }
}
```

### 2. Time Monitoring
```typescript
// Scheduler service continuously monitors
setInterval(async () => {
    const dueSchedules = await this.findDueSchedules();
    
    for (const schedule of dueSchedules) {
        if (this.shouldExecute(schedule)) {
            await this.queueService.addScheduledExecution(schedule.id, schedule);
            await this.updateNextRunTime(schedule);
        }
    }
}, 1000); // Check every second
```

### 3. Job Execution
```typescript
@Processor('workflow-execution')
export class WorkflowExecutionProcessor {
    @Process('execute-scheduled-workflow')
    async executeScheduledWorkflow(job: Job<ScheduledExecutionData>) {
        const { scheduleId, workflowId, executionBehavior } = job.data;
        
        try {
            let executionResult;
            
            switch (executionBehavior) {
                case EExecutionBehavior.START_WORKFLOW:
                    executionResult = await this.workflowEngine.executeWorkflow(workflowId);
                    break;
                case EExecutionBehavior.START_FROM_NODE:
                    executionResult = await this.workflowEngine.executeFromNode(
                        workflowId, 
                        job.data.targetNodeId
                    );
                    break;
                case EExecutionBehavior.TRIGGER_EVENT:
                    executionResult = await this.workflowEngine.triggerEvent(
                        workflowId, 
                        job.data.eventName
                    );
                    break;
            }
            
            await this.updateExecutionStatus(scheduleId, 'completed', executionResult);
            
        } catch (error) {
            await this.updateExecutionStatus(scheduleId, 'failed', null, error.message);
            throw error; // For Bull retry mechanism
        }
    }
}
```

## 📊 Monitoring & Management

### 1. Schedule Health Check
```typescript
@Injectable()
export class ScheduleHealthService {
    async checkScheduleHealth(): Promise<ScheduleHealthReport> {
        const activeSchedules = await this.scheduleRepository.findActive();
        const failedExecutions = await this.getRecentFailures();
        const queueHealth = await this.queueService.getHealth();
        
        return {
            totalActiveSchedules: activeSchedules.length,
            failedExecutionsLast24h: failedExecutions.length,
            queueHealth,
            recommendations: this.generateRecommendations()
        };
    }
}
```

### 2. Performance Optimization
- **Batch processing**: Group multiple schedules
- **Lazy loading**: Load schedules on demand
- **Caching**: Cache next run times
- **Partitioning**: Distribute schedules across workers

## 🛡️ Error Handling & Recovery

### 1. Overlap Behavior Implementation
```typescript
async handleOverlap(schedule: Schedule, behavior: EOverlapBehavior): Promise<boolean> {
    const runningExecutions = await this.getRunningExecutions(schedule.id);
    
    switch (behavior) {
        case EOverlapBehavior.SKIP:
            return runningExecutions.length === 0;
        case EOverlapBehavior.ALLOW:
            return true;
        case EOverlapBehavior.REPLACE:
            await this.cancelRunningExecutions(runningExecutions);
            return true;
        case EOverlapBehavior.QUEUE:
            return true; // Bull queue handles queuing
    }
}
```

### 2. Failure Recovery
- **Dead letter queue**: Failed jobs
- **Exponential backoff**: Retry delays
- **Circuit breaker**: Stop failing schedules
- **Alert system**: Notify administrators

## 🔧 Configuration Examples

### Environment Variables
```env
# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Scheduler Configuration
SCHEDULER_CHECK_INTERVAL=1000
MAX_CONCURRENT_EXECUTIONS=10
DEFAULT_EXECUTION_TIMEOUT=300000

# Queue Configuration
QUEUE_CONCURRENCY=5
QUEUE_MAX_RETRIES=3
QUEUE_RETRY_DELAY=60000
```

### Module Configuration
```typescript
@Module({
  imports: [
    ScheduleModule.forRoot(),
    BullModule.forRoot({
      redis: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT),
      },
    }),
  ],
  providers: [
    SchedulerService,
    WorkflowQueueService,
    ScheduleHealthService,
  ],
})
export class WorkflowScheduleModule {}
```

Kiến trúc này đảm bảo tính scalable, reliable và maintainable cho Schedule node system!
