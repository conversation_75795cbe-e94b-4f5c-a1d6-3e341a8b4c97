import { NodeDefinition } from '../entities/node-definition.entity';
import { NodeGroupEnum } from '../enums/node-group.enum';
import {
  ECredentialName,
  ENodeAuthType,
  EPropertyType,
  ELoadOptionsResource,
  ELoadOptionsMethod,
  ICredentialDefinition,
  INodeProperty,
  ENodeType
} from '../interfaces/node-manager.interface';

/**
 * <PERSON><PERSON> liệu mẫu cho Node Definitions
 * <PERSON><PERSON> gồm các node phổ biến trong workflow automation
 */
export const NODE_DEFINITIONS_SEED_DATA: Partial<NodeDefinition>[] = [
  // ========== AI NODES ==========
  {
    typeName: ENodeType.OPENAI_CHAT,
    version: 1,
    displayName: 'OpenAI Chat',
    description: 'G<PERSON><PERSON> tin nhắn đến OpenAI GPT models và nhận phản hồi',
    groupName: NodeGroupEnum.AI,
    icon: 'openai',
    credentials: [
      {
        name: ECredentialName.OPENAI_API,
        displayName: 'OpenAI API Key',
        description: 'API key để truy cập OpenAI services',
        required: true,
        authType: ENodeAuthType.API_KEY,
        providerType: 'openai',
        testable: true,
        testUrl: '/api/integrations/test-connection'
      } as ICredentialDefinition
    ],
    properties: [
      {
        name: 'model',
        displayName: 'Model',
        description: 'Chọn model OpenAI để sử dụng',
        type: EPropertyType.Options,
        required: true,
        loadOptions: {
          resource: ELoadOptionsResource.AI_MODELS,
          method: ELoadOptionsMethod.LIST_BY_PROVIDER,
          dependsOn: ['credential']
        }
      },
      {
        name: 'messages',
        displayName: 'Messages',
        description: 'Danh sách tin nhắn trong cuộc hội thoại',
        type: EPropertyType.Array,
        required: true,
        properties: [
          {
            name: 'role',
            displayName: 'Role',
            type: EPropertyType.Options,
            required: true,
            options: [
              { name: 'System', value: 'system' },
              { name: 'User', value: 'user' },
              { name: 'Assistant', value: 'assistant' }
            ]
          },
          {
            name: 'content',
            displayName: 'Content',
            type: EPropertyType.String,
            required: true
          }
        ]
      },
      {
        name: 'temperature',
        displayName: 'Temperature',
        description: 'Độ sáng tạo của model (0.0 - 2.0)',
        type: EPropertyType.Slider,
        minValue: 0,
        maxValue: 2,
        step: 0.1
      },
      {
        name: 'maxTokens',
        displayName: 'Max Tokens',
        description: 'Số token tối đa trong phản hồi',
        type: EPropertyType.Number,
        minValue: 1,
        maxValue: 4096
      }
    ] as INodeProperty[],
    inputs: ['main'],
    outputs: ['main']
  },

  // ========== HTTP NODES ==========
  {
    typeName: ENodeType.HTTP_REQUEST,
    version: 1,
    displayName: 'HTTP Request',
    description: 'Thực hiện HTTP request đến API endpoint',
    groupName: NodeGroupEnum.INTEGRATION,
    icon: 'http',
    properties: [
      {
        name: 'method',
        displayName: 'Method',
        description: 'HTTP method để sử dụng',
        type: EPropertyType.Options,
        required: true,
        options: [
          { name: 'GET', value: 'GET' },
          { name: 'POST', value: 'POST' },
          { name: 'PUT', value: 'PUT' },
          { name: 'DELETE', value: 'DELETE' },
          { name: 'PATCH', value: 'PATCH' }
        ]
      },
      {
        name: 'url',
        displayName: 'URL',
        description: 'URL endpoint để gửi request',
        type: EPropertyType.String,
        required: true
      },
      {
        name: 'headers',
        displayName: 'Headers',
        description: 'HTTP headers',
        type: EPropertyType.Collection,
        properties: [
          {
            name: 'contentType',
            displayName: 'Content-Type',
            type: EPropertyType.Options,
            options: [
              { name: 'application/json', value: 'application/json' },
              { name: 'application/x-www-form-urlencoded', value: 'application/x-www-form-urlencoded' },
              { name: 'text/plain', value: 'text/plain' }
            ]
          },
          {
            name: 'authorization',
            displayName: 'Authorization',
            type: EPropertyType.String
          }
        ]
      },
      {
        name: 'body',
        displayName: 'Body',
        description: 'Request body (cho POST/PUT/PATCH)',
        type: EPropertyType.Json
      }
    ] as INodeProperty[],
    inputs: ['main'],
    outputs: ['main']
  },

  // ========== GOOGLE SHEETS NODE ==========
  {
    typeName: ENodeType.GOOGLE_SHEETS,
    version: 1,
    displayName: 'Google Sheets',
    description: 'Đọc và ghi dữ liệu từ Google Sheets',
    groupName: NodeGroupEnum.INTEGRATION,
    icon: 'google-sheets',
    credentials: [
      {
        name: ECredentialName.GOOGLE_SHEETS,
        displayName: 'Google Sheets Account',
        description: 'Tài khoản Google để truy cập Sheets',
        required: true,
        authType: ENodeAuthType.OAUTH2,
        providerType: 'google',
        authUrl: '/api/auth/google/connect',
        scopes: [
          'https://www.googleapis.com/auth/spreadsheets',
          'https://www.googleapis.com/auth/drive.readonly'
        ],
        testable: true,
        testUrl: '/api/integrations/test-connection'
      } as ICredentialDefinition
    ],
    properties: [
      {
        name: 'operation',
        displayName: 'Operation',
        description: 'Thao tác cần thực hiện',
        type: EPropertyType.Options,
        required: true,
        options: [
          { name: 'Read', value: 'read' },
          { name: 'Write', value: 'write' },
          { name: 'Update', value: 'update' },
          { name: 'Delete', value: 'delete' }
        ]
      },
      {
        name: 'spreadsheetId',
        displayName: 'Spreadsheet',
        description: 'Chọn Google Sheets để làm việc',
        type: EPropertyType.Options,
        required: true,
        loadOptions: {
          resource: ELoadOptionsResource.GOOGLE_SHEETS,
          method: ELoadOptionsMethod.GET_SHEETS,
          dependsOn: ['credential']
        }
      },
      {
        name: 'worksheetName',
        displayName: 'Worksheet',
        description: 'Chọn worksheet trong spreadsheet',
        type: EPropertyType.Options,
        required: true,
        loadOptions: {
          resource: ELoadOptionsResource.GOOGLE_WORKSHEETS,
          method: ELoadOptionsMethod.GET_WORKSHEETS,
          dependsOn: ['credential', 'spreadsheetId']
        }
      },
      {
        name: 'range',
        displayName: 'Range',
        description: 'Phạm vi cells (ví dụ: A1:C10)',
        type: EPropertyType.String,
        required: true
      }
    ] as INodeProperty[],
    inputs: ['main'],
    outputs: ['main']
  }
];
