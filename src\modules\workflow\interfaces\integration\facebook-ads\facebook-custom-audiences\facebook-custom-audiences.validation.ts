/**
 * @file Facebook Custom Audiences Validation & Credentials
 * 
 * <PERSON><PERSON><PERSON> nghĩa validation functions và credentials cho Facebook Custom Audiences integration
 * Theo patterns từ Make.com chuẩn
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

import {
    IBaseIntegrationCredential
} from '../../base/base-integration.interface';

import {
    ECredentialName,
    ENodeAuthType
} from '../../../node-manager.interface';

import {
    ITypedNodeExecution
} from '../../../execute.interface';

import {
    EFacebookCustomAudiencesOperation
} from './facebook-custom-audiences.types';

import {
    IFacebookCustomAudiencesInput,
    IFacebookCustomAudiencesOutput,
    IFacebookCustomAudiencesParameters,
    ICreateCustomAudienceParameters,
    IAddEmailsToCustomAudienceParameters,
    IAddUsersToCustomAudienceParameters,
    IRemoveAudienceMembersParameters,
    ICreateLookalikeAudienceParameters,
    ICreatePageFanLookalikeAudienceParameters,
    ICreateCampaignConversionLookalikesParameters,
    ICreateValueBasedCustomAudienceParameters,
    IPopulateSeedAudienceParameters,
    ICreateValueBasedLookalikeParameters,
    ILookalikeSpecification,
    IPageFanLookalikeSpecification,
    ICampaignConversionLookalikeSpecification,
    ISourceSpecification,
    ILocationSpecification,
    IGeoLocationsSpecification,
    IUserData
} from './facebook-custom-audiences.interface';

// =================================================================
// CREDENTIAL DEFINITION
// =================================================================

/**
 * Facebook Custom Audiences credential definition
 */
export const FACEBOOK_CUSTOM_AUDIENCES_CREDENTIAL: IBaseIntegrationCredential = {
    provider: 'facebook',
    name: ECredentialName.FACEBOOK_OAUTH,
    displayName: 'Facebook Custom Audiences OAuth2',
    description: 'OAuth2 authentication for Facebook Custom Audiences',
    required: true,
    authType: ENodeAuthType.OAUTH2,
    testable: true,
    testUrl: '/api/integrations/test-connection'
};

// =================================================================
// VALIDATION FUNCTIONS
// =================================================================

/**
 * Validate Facebook Custom Audiences parameters
 */
export function validateFacebookCustomAudiencesParameters(
    params: Partial<IFacebookCustomAudiencesParameters>
): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check required fields - Kiểm tra các trường bắt buộc
    if (!params.operation) {
        errors.push('Operation is required');
    }

    // Operation specific validation - Xác thực theo từng thao tác
    switch (params.operation) {
        case EFacebookCustomAudiencesOperation.CREATE_CUSTOM_AUDIENCE:
            const createCustomAudienceParams = params as ICreateCustomAudienceParameters;
            if (!createCustomAudienceParams.business_manager) {
                errors.push('Business Manager is required for Create Custom Audience operation');
            }
            if (!createCustomAudienceParams.name) {
                errors.push('Name is required for Create Custom Audience operation');
            }
            break;

        case EFacebookCustomAudiencesOperation.ADD_EMAILS_TO_CUSTOM_AUDIENCE:
            const addEmailsParams = params as IAddEmailsToCustomAudienceParameters;
            if (!addEmailsParams.business_manager) {
                errors.push('Business Manager is required for Add Emails to Custom Audience operation');
            }
            if (!addEmailsParams.emails || addEmailsParams.emails.length === 0) {
                errors.push('Emails list is required and cannot be empty for Add Emails to Custom Audience operation');
            }
            // Validate email format
            if (addEmailsParams.emails) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                addEmailsParams.emails.forEach((email, index) => {
                    if (!emailRegex.test(email)) {
                        errors.push(`Invalid email format at index ${index}: ${email}`);
                    }
                });
            }
            break;

        case EFacebookCustomAudiencesOperation.ADD_USERS_TO_CUSTOM_AUDIENCE:
            const addUsersParams = params as IAddUsersToCustomAudienceParameters;
            if (!addUsersParams.business_manager) {
                errors.push('Business Manager is required for Add Users to Custom Audience operation');
            }
            if (!addUsersParams.data || addUsersParams.data.length === 0) {
                errors.push('Data list is required and cannot be empty for Add Users to Custom Audience operation');
            }
            // Validate user data
            if (addUsersParams.data) {
                addUsersParams.data.forEach((userData, index) => {
                    // Validate birth year format
                    if (userData.birth_year && !/^(19|20)[0-9]{2}$/.test(userData.birth_year)) {
                        errors.push(`Invalid birth year format at index ${index}: ${userData.birth_year}. Must be YYYY format from 1900 to current year.`);
                    }

                    // Validate birth month format
                    if (userData.birth_month && !/^[0-9]{2}$/.test(userData.birth_month)) {
                        errors.push(`Invalid birth month format at index ${index}: ${userData.birth_month}. Must be MM format: 01-12.`);
                    }

                    // Validate birth day format
                    if (userData.birth_day && !/^[0-9]{2}$/.test(userData.birth_day)) {
                        errors.push(`Invalid birth day format at index ${index}: ${userData.birth_day}. Must be DD format: 01-31.`);
                    }

                    // Validate country format
                    if (userData.country && !/^[a-zA-Z]{2}$/.test(userData.country)) {
                        errors.push(`Invalid country format at index ${index}: ${userData.country}. Must be 2-letter ISO 3166-1 alpha-2 code.`);
                    }

                    // Validate email format
                    if (userData.email) {
                        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                        if (!emailRegex.test(userData.email)) {
                            errors.push(`Invalid email format at index ${index}: ${userData.email}`);
                        }
                    }

                    // Validate Page ID requirement
                    if (userData.page_scoped_user_id && !userData.page_id) {
                        errors.push(`Page ID is required when Page Scoped User ID is provided at index ${index}`);
                    }
                });
            }
            break;

        case EFacebookCustomAudiencesOperation.REMOVE_AUDIENCE_MEMBERS:
            const removeAudienceMembersParams = params as IRemoveAudienceMembersParameters;
            if (!removeAudienceMembersParams.business_manager) {
                errors.push('Business Manager is required for Remove Audience Members operation');
            }
            if (!removeAudienceMembersParams.remove_members_by) {
                errors.push('Remove members by is required for Remove Audience Members operation');
            }
            break;

        case EFacebookCustomAudiencesOperation.CREATE_LOOKALIKE_AUDIENCE:
            const createLookalikeParams = params as ICreateLookalikeAudienceParameters;
            if (!createLookalikeParams.business_manager) {
                errors.push('Business Manager is required for Create Lookalike Audience operation');
            }
            if (!createLookalikeParams.name) {
                errors.push('Name is required for Create Lookalike Audience operation');
            }
            if (!createLookalikeParams.lookalike_specification) {
                errors.push('Lookalike specification is required for Create Lookalike Audience operation');
            } else {
                const spec = createLookalikeParams.lookalike_specification;

                // Validate source
                if (!spec.source) {
                    errors.push('Source is required in lookalike specification');
                } else {
                    if (!spec.source.ratio || spec.source.ratio < 0.01 || spec.source.ratio > 0.20) {
                        errors.push('Ratio is required and must be between 0.01 and 0.20');
                    }
                    if (spec.source.starting_ratio && (spec.source.starting_ratio < 0.01 || spec.source.starting_ratio > 0.20)) {
                        errors.push('Starting ratio must be between 0.01 and 0.20');
                    }
                    if (spec.source.starting_ratio && spec.source.ratio && spec.source.starting_ratio >= spec.source.ratio) {
                        errors.push('Starting ratio must be less than ratio');
                    }
                }

                // Validate location
                if (!spec.location) {
                    errors.push('Location is required in lookalike specification');
                } else {
                    if (spec.location.type === 'simple_country') {
                        if (!spec.location.country) {
                            errors.push('Country is required for simple country location type');
                        }
                    } else if (spec.location.type === 'location_specification') {
                        if (!spec.location.location_specification) {
                            errors.push('Location specification is required for location specification type');
                        } else {
                            const locSpec = spec.location.location_specification;
                            if (!locSpec.geo_locations) {
                                errors.push('Geo locations is required in location specification');
                            } else {
                                const geoLoc = locSpec.geo_locations;
                                if ((!geoLoc.countries || geoLoc.countries.length === 0) &&
                                    (!geoLoc.country_groups || geoLoc.country_groups.length === 0)) {
                                    errors.push('Either countries or country groups are required in geo locations');
                                }
                            }
                        }
                    } else {
                        errors.push('Location type must be either simple_country or location_specification');
                    }
                }

                // Validate allow_international_seeds
                if (spec.allow_international_seeds && !['Yes', 'No', 'Empty'].includes(spec.allow_international_seeds)) {
                    errors.push('Allow international seeds must be Yes, No, or Empty');
                }
            }
            break;

        case EFacebookCustomAudiencesOperation.CREATE_PAGE_FAN_LOOKALIKE_AUDIENCE:
            const createPageFanLookalikeParams = params as ICreatePageFanLookalikeAudienceParameters;
            if (!createPageFanLookalikeParams.business_manager) {
                errors.push('Business Manager is required for Create Page Fan Lookalike Audience operation');
            }
            if (!createPageFanLookalikeParams.lookalike_specification) {
                errors.push('Lookalike specification is required for Create Page Fan Lookalike Audience operation');
            } else {
                const spec = createPageFanLookalikeParams.lookalike_specification;

                // Validate page
                if (!spec.page) {
                    errors.push('Page is required in lookalike specification');
                }

                // Validate country
                if (!spec.country) {
                    errors.push('Country is required in lookalike specification');
                }

                // Validate ratio
                if (!spec.ratio || spec.ratio < 0.01 || spec.ratio > 0.20) {
                    errors.push('Ratio is required and must be between 0.01 and 0.20');
                }

                // Validate starting_ratio if provided
                if (spec.starting_ratio && (spec.starting_ratio < 0.01 || spec.starting_ratio > 0.20)) {
                    errors.push('Starting ratio must be between 0.01 and 0.20');
                }
                if (spec.starting_ratio && spec.ratio && spec.starting_ratio >= spec.ratio) {
                    errors.push('Starting ratio must be less than ratio');
                }

                // Validate allow_international_seeds
                if (spec.allow_international_seeds && !['Yes', 'No', 'Empty'].includes(spec.allow_international_seeds)) {
                    errors.push('Allow international seeds must be Yes, No, or Empty');
                }
            }
            break;

        case EFacebookCustomAudiencesOperation.CREATE_CAMPAIGN_CONVERSION_LOOKALIKES:
            const createCampaignConversionParams = params as ICreateCampaignConversionLookalikesParameters;
            if (!createCampaignConversionParams.business_manager) {
                errors.push('Business Manager is required for Create Campaign or Ad Set Conversion Lookalikes operation');
            }
            if (!createCampaignConversionParams.lookalike_specification) {
                errors.push('Lookalike specification is required for Create Campaign or Ad Set Conversion Lookalikes operation');
            } else {
                const spec = createCampaignConversionParams.lookalike_specification;

                // Validate country
                if (!spec.country) {
                    errors.push('Country is required in lookalike specification');
                }

                // Validate ratio
                if (!spec.ratio || spec.ratio < 0.01 || spec.ratio > 0.20) {
                    errors.push('Ratio is required and must be between 0.01 and 0.20');
                }

                // Validate starting_ratio if provided
                if (spec.starting_ratio && (spec.starting_ratio < 0.01 || spec.starting_ratio > 0.20)) {
                    errors.push('Starting ratio must be between 0.01 and 0.20');
                }
                if (spec.starting_ratio && spec.ratio && spec.starting_ratio >= spec.ratio) {
                    errors.push('Starting ratio must be less than ratio');
                }

                // Validate allow_international_seeds
                if (spec.allow_international_seeds && !['Yes', 'No', 'Empty'].includes(spec.allow_international_seeds)) {
                    errors.push('Allow international seeds must be Yes, No, or Empty');
                }
            }
            break;

        case EFacebookCustomAudiencesOperation.CREATE_VALUE_BASED_CUSTOM_AUDIENCE:
            const createValueBasedParams = params as ICreateValueBasedCustomAudienceParameters;
            if (!createValueBasedParams.business_manager) {
                errors.push('Business Manager is required for Create Value-Based Custom Audience operation');
            }
            if (!createValueBasedParams.name) {
                errors.push('Name is required for Create Value-Based Custom Audience operation');
            }
            break;

        case EFacebookCustomAudiencesOperation.POPULATE_SEED_AUDIENCE:
            const populateSeedParams = params as IPopulateSeedAudienceParameters;
            if (!populateSeedParams.business_manager) {
                errors.push('Business Manager is required for Populate Seed Audience operation');
            }
            if (!populateSeedParams.data || populateSeedParams.data.length === 0) {
                errors.push('Data list is required and cannot be empty for Populate Seed Audience operation');
            }
            // Validate seed audience data
            if (populateSeedParams.data) {
                populateSeedParams.data.forEach((seedData, index) => {
                    // Validate lookalike_value (required)
                    if (!seedData.lookalike_value) {
                        errors.push(`Lookalike value is required at index ${index}`);
                    }

                    // Validate birth year format
                    if (seedData.birth_year && !/^(19|20)[0-9]{2}$/.test(seedData.birth_year)) {
                        errors.push(`Invalid birth year format at index ${index}: ${seedData.birth_year}. Must be YYYY format from 1900 to current year.`);
                    }

                    // Validate birth month format
                    if (seedData.birth_month && !/^[0-9]{2}$/.test(seedData.birth_month)) {
                        errors.push(`Invalid birth month format at index ${index}: ${seedData.birth_month}. Must be MM format: 01-12.`);
                    }

                    // Validate birth day format
                    if (seedData.birth_day && !/^[0-9]{2}$/.test(seedData.birth_day)) {
                        errors.push(`Invalid birth day format at index ${index}: ${seedData.birth_day}. Must be DD format: 01-31.`);
                    }

                    // Validate country format
                    if (seedData.country && !/^[a-zA-Z]{2}$/.test(seedData.country)) {
                        errors.push(`Invalid country format at index ${index}: ${seedData.country}. Must be 2-letter ISO 3166-1 alpha-2 code.`);
                    }

                    // Validate email format
                    if (seedData.email) {
                        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                        if (!emailRegex.test(seedData.email)) {
                            errors.push(`Invalid email format at index ${index}: ${seedData.email}`);
                        }
                    }
                });
            }
            break;

        case EFacebookCustomAudiencesOperation.CREATE_VALUE_BASED_LOOKALIKE:
            const createValueBasedLookalikeParams = params as ICreateValueBasedLookalikeParameters;
            if (!createValueBasedLookalikeParams.business_manager) {
                errors.push('Business Manager is required for Create Value-Based Lookalike operation');
            }
            if (!createValueBasedLookalikeParams.name) {
                errors.push('Name is required for Create Value-Based Lookalike operation');
            }
            if (!createValueBasedLookalikeParams.lookalike_specification) {
                errors.push('Lookalike specification is required for Create Value-Based Lookalike operation');
            } else {
                const spec = createValueBasedLookalikeParams.lookalike_specification;

                // Validate ratio
                if (!spec.ratio || spec.ratio < 0.01 || spec.ratio > 0.20) {
                    errors.push('Ratio is required and must be between 0.01 and 0.20');
                }

                // Validate country
                if (!spec.country) {
                    errors.push('Country is required in lookalike specification');
                }
            }
            break;

        // TODO: Add validation for other operations when implemented
        default:
            if (params.operation && !Object.values(EFacebookCustomAudiencesOperation).includes(params.operation)) {
                errors.push(`Unsupported operation: ${params.operation}`);
            }
            break;
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}

/**
 * Type guard cho Facebook Custom Audiences parameters
 */
export function isFacebookCustomAudiencesParameters(params: any): params is IFacebookCustomAudiencesParameters {
    return params && Object.values(EFacebookCustomAudiencesOperation).includes(params.operation);
}

/**
 * Type-safe node execution cho Facebook Custom Audiences
 */
export type IFacebookCustomAudiencesNodeExecution = ITypedNodeExecution<
    IFacebookCustomAudiencesInput,
    IFacebookCustomAudiencesOutput,
    IFacebookCustomAudiencesParameters
>;
