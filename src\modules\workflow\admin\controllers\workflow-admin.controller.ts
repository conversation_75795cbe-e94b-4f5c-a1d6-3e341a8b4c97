import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
  ApiBody,
} from '@nestjs/swagger';
import { WorkflowAdminService } from '../services';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { ApiResponseDto, PaginatedResult } from '@common/response/api-response-dto';
import {
  CreateWorkflowDto,
  UpdateWorkflowDto,
  WorkflowResponseDto,
  QueryWorkflowDto,
  BulkDeleteWorkflowDto,
  BulkDeleteWorkflowResponseDto,
} from '../../dto';
import { ApiErrorResponse, ApiMultipleErrorResponses } from '@common/decorators/api-error-response.decorator';
import { ApiErrorResponseDto } from '@common/dto/api-error-response.dto';
import { WORKFLOW_ADMIN_ERROR_CODES } from '../../exceptions/workflow.exception';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { PermissionsGuard } from '@/modules/auth/guards/permissions.guard';
import { WorkflowExecutionAdminService } from '../services/workflow-execution-admin.service';

/**
 * Controller xử lý các API liên quan đến workflow cho admin
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_WORKFLOW)
@ApiExtraModels(
  ApiResponseDto,
  WorkflowResponseDto,
  CreateWorkflowDto,
  UpdateWorkflowDto,
  QueryWorkflowDto,
  BulkDeleteWorkflowDto,
  BulkDeleteWorkflowResponseDto,
  PaginatedResult,
  ApiErrorResponseDto,
)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard, PermissionsGuard)
@Controller('admin/workflow')
export class WorkflowAdminController {
  constructor(
    private readonly workflowAdminService: WorkflowAdminService,
    private readonly workflowExecutionAdminService: WorkflowExecutionAdminService,
  ) {}

  /**
   * Lấy danh sách tất cả workflows (admin có thể xem tất cả)
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách tất cả workflows' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách workflows',
    schema: ApiResponseDto.getPaginatedSchema(WorkflowResponseDto),
  })
  @ApiMultipleErrorResponses(
    HttpStatus.INTERNAL_SERVER_ERROR,
    [
      WORKFLOW_ADMIN_ERROR_CODES.WORKFLOW_FETCH_ERROR,
      WORKFLOW_ADMIN_ERROR_CODES.WORKFLOW_GENERAL_ERROR
    ]
  )
  async getWorkflows(
    @CurrentEmployee('id') employeeId: number,
    @Query() queryDto: QueryWorkflowDto,
  ): Promise<ApiResponseDto<PaginatedResult<WorkflowResponseDto>>> {
    const result = await this.workflowAdminService.getWorkflows(employeeId, queryDto);
    return ApiResponseDto.paginated(result, 'Lấy danh sách workflows thành công');
  }

  /**
   * Lấy workflow theo ID (admin có thể xem tất cả)
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy workflow theo ID' })
  @ApiParam({
    name: 'id',
    description: 'ID của workflow',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Thông tin workflow',
    schema: ApiResponseDto.getSchema(WorkflowResponseDto),
  })
  @ApiMultipleErrorResponses(
    HttpStatus.NOT_FOUND,
    [WORKFLOW_ADMIN_ERROR_CODES.WORKFLOW_NOT_FOUND]
  )
  @ApiMultipleErrorResponses(
    HttpStatus.INTERNAL_SERVER_ERROR,
    [WORKFLOW_ADMIN_ERROR_CODES.WORKFLOW_FETCH_ERROR]
  )
  async getWorkflowById(
    @CurrentEmployee('id') employeeId: number,
    @Param('id', ParseUUIDPipe) workflowId: string,
  ): Promise<ApiResponseDto<WorkflowResponseDto>> {
    const result = await this.workflowAdminService.getWorkflowById(employeeId, workflowId);
    return ApiResponseDto.success(result, 'Lấy thông tin workflow thành công');
  }

  /**
   * Tạo workflow mới cho admin
   */
  @Post()
  @ApiOperation({ summary: 'Tạo workflow mới cho admin' })
  @ApiBody({ type: CreateWorkflowDto })
  @ApiResponse({
    status: 201,
    description: 'Workflow đã được tạo',
    schema: ApiResponseDto.getSchema(WorkflowResponseDto),
  })
  @ApiMultipleErrorResponses(
    HttpStatus.BAD_REQUEST,
    [
      WORKFLOW_ADMIN_ERROR_CODES.WORKFLOW_INVALID_NAME,
      WORKFLOW_ADMIN_ERROR_CODES.WORKFLOW_INVALID_SETTINGS,
      WORKFLOW_ADMIN_ERROR_CODES.ADMIN_WORKFLOW_INVALID_USER_ASSIGNMENT,
      WORKFLOW_ADMIN_ERROR_CODES.ADMIN_WORKFLOW_INVALID_EMPLOYEE_ID
    ]
  )
  @ApiMultipleErrorResponses(
    HttpStatus.INTERNAL_SERVER_ERROR,
    [WORKFLOW_ADMIN_ERROR_CODES.WORKFLOW_CREATION_ERROR]
  )
  async createWorkflow(
    @CurrentEmployee('id') employeeId: number,
    @Body() createDto: CreateWorkflowDto,
  ): Promise<ApiResponseDto<WorkflowResponseDto>> {
    const result = await this.workflowAdminService.createWorkflow(employeeId, createDto);
    return ApiResponseDto.created(result, 'Tạo workflow thành công');
  }

  /**
   * Cập nhật workflow (chỉ được edit workflow của chính admin)
   */
  @Patch(':id')
  @ApiOperation({ summary: 'Cập nhật workflow (chỉ workflow của admin hiện tại)' })
  @ApiParam({
    name: 'id',
    description: 'ID của workflow',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiBody({ type: UpdateWorkflowDto })
  @ApiResponse({
    status: 200,
    description: 'Workflow đã được cập nhật',
    schema: ApiResponseDto.getSchema(WorkflowResponseDto),
  })
  @ApiMultipleErrorResponses(
    HttpStatus.NOT_FOUND,
    [WORKFLOW_ADMIN_ERROR_CODES.WORKFLOW_NOT_FOUND]
  )
  @ApiMultipleErrorResponses(
    HttpStatus.FORBIDDEN,
    [WORKFLOW_ADMIN_ERROR_CODES.ADMIN_WORKFLOW_EDIT_PERMISSION_DENIED]
  )
  @ApiMultipleErrorResponses(
    HttpStatus.BAD_REQUEST,
    [
      WORKFLOW_ADMIN_ERROR_CODES.WORKFLOW_INVALID_NAME,
      WORKFLOW_ADMIN_ERROR_CODES.WORKFLOW_INVALID_SETTINGS,
      WORKFLOW_ADMIN_ERROR_CODES.WORKFLOW_INVALID_INPUT
    ]
  )
  @ApiMultipleErrorResponses(
    HttpStatus.INTERNAL_SERVER_ERROR,
    [WORKFLOW_ADMIN_ERROR_CODES.WORKFLOW_UPDATE_ERROR]
  )
  async updateWorkflow(
    @CurrentEmployee('id') employeeId: number,
    @Param('id', ParseUUIDPipe) workflowId: string,
    @Body() updateDto: UpdateWorkflowDto,
  ): Promise<ApiResponseDto<WorkflowResponseDto>> {
    const result = await this.workflowAdminService.updateWorkflow(employeeId, workflowId, updateDto);
    return ApiResponseDto.success(result, 'Cập nhật workflow thành công');
  }

  /**
   * Bulk delete workflows (chỉ được xóa workflow của chính admin)
   */
  @Delete()
  @ApiOperation({ summary: 'Xóa nhiều workflows (chỉ workflow của admin hiện tại)' })
  @ApiBody({ type: BulkDeleteWorkflowDto })
  @ApiResponse({
    status: 200,
    description: 'Kết quả xóa workflows',
    schema: ApiResponseDto.getSchema(BulkDeleteWorkflowResponseDto),
  })
  @ApiMultipleErrorResponses(
    HttpStatus.BAD_REQUEST,
    [
      WORKFLOW_ADMIN_ERROR_CODES.WORKFLOW_EMPTY_IDS_LIST,
      WORKFLOW_ADMIN_ERROR_CODES.WORKFLOW_INVALID_ID,
      WORKFLOW_ADMIN_ERROR_CODES.WORKFLOW_INVALID_INPUT
    ]
  )
  @ApiMultipleErrorResponses(
    HttpStatus.FORBIDDEN,
    [
      WORKFLOW_ADMIN_ERROR_CODES.ADMIN_WORKFLOW_DELETE_PERMISSION_DENIED,
      WORKFLOW_ADMIN_ERROR_CODES.ADMIN_WORKFLOW_BULK_DELETE_OWNERSHIP_ERROR
    ]
  )
  @ApiMultipleErrorResponses(
    HttpStatus.PARTIAL_CONTENT,
    [WORKFLOW_ADMIN_ERROR_CODES.ADMIN_WORKFLOW_BULK_OPERATION_PARTIAL_FAILURE]
  )
  @ApiMultipleErrorResponses(
    HttpStatus.INTERNAL_SERVER_ERROR,
    [WORKFLOW_ADMIN_ERROR_CODES.WORKFLOW_BULK_DELETE_ERROR]
  )
  async bulkDeleteWorkflows(
    @CurrentEmployee('id') employeeId: number,
    @Body() bulkDeleteDto: BulkDeleteWorkflowDto,
  ): Promise<ApiResponseDto<BulkDeleteWorkflowResponseDto>> {
    const result = await this.workflowAdminService.bulkDeleteWorkflows(employeeId, bulkDeleteDto);
    return ApiResponseDto.success(result, 'Xóa workflows thành công');
  }

  /**
   * Thực thi workflow (Admin)
   */
  @Post(':workflowId/execute')
  @ApiOperation({
    summary: 'Thực thi workflow (Admin)',
    description: 'Admin khởi chạy thực thi workflow từ đầu'
  })
  @ApiParam({
    name: 'workflowId',
    description: 'ID của workflow cần thực thi',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Workflow đã được khởi chạy thành công',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Workflow đã được khởi chạy thành công' },
        data: {
          type: 'object',
          properties: {
            executionId: { type: 'string', example: '123e4567-e89b-12d3-a456-************' },
            status: { type: 'string', example: 'running' },
            startedAt: { type: 'string', format: 'date-time' }
          }
        }
      }
    }
  })
  @ApiMultipleErrorResponses(
    HttpStatus.NOT_FOUND,
    [WORKFLOW_ADMIN_ERROR_CODES.WORKFLOW_NOT_FOUND]
  )
  @ApiMultipleErrorResponses(
    HttpStatus.BAD_REQUEST,
    [WORKFLOW_ADMIN_ERROR_CODES.WORKFLOW_EXECUTION_ERROR]
  )
  async executeWorkflow(
    @CurrentEmployee('id') employeeId: number,
    @Param('workflowId', ParseUUIDPipe) workflowId: string,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.workflowExecutionAdminService.executeWorkflow(employeeId, workflowId);
    return ApiResponseDto.success(result, 'Workflow đã được khởi chạy thành công');
  }

  /**
   * Thực thi node cụ thể trong workflow (Admin)
   */
  @Post(':workflowId/node/:id/execute')
  @ApiOperation({
    summary: 'Thực thi node cụ thể trong workflow (Admin)',
    description: 'Admin khởi chạy thực thi từ một node cụ thể trong workflow'
  })
  @ApiParam({
    name: 'workflowId',
    description: 'ID của workflow',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của node cần thực thi',
    example: '456e7890-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Node đã được khởi chạy thành công',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Node đã được khởi chạy thành công' },
        data: {
          type: 'object',
          properties: {
            executionId: { type: 'string', example: '123e4567-e89b-12d3-a456-************' },
            nodeId: { type: 'string', example: '456e7890-e89b-12d3-a456-************' },
            status: { type: 'string', example: 'running' },
            startedAt: { type: 'string', format: 'date-time' }
          }
        }
      }
    }
  })
  @ApiMultipleErrorResponses(
    HttpStatus.NOT_FOUND,
    [WORKFLOW_ADMIN_ERROR_CODES.WORKFLOW_NOT_FOUND]
  )
  @ApiMultipleErrorResponses(
    HttpStatus.BAD_REQUEST,
    [WORKFLOW_ADMIN_ERROR_CODES.WORKFLOW_EXECUTION_ERROR]
  )
  async executeWorkflowNode(
    @CurrentEmployee('id') employeeId: number,
    @Param('workflowId', ParseUUIDPipe) workflowId: string,
    @Param('id', ParseUUIDPipe) nodeId: string,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.workflowExecutionAdminService.executeWorkflowNode(employeeId, workflowId, nodeId);
    return ApiResponseDto.success(result, 'Node đã được khởi chạy thành công');
  }
}
