/**
 * @file Node Update Interceptor Service
 * 
 * Service intercept node updates và handle schedule-specific logic
 * Detect schedule nodes và tạo/update delayed jobs
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Node } from '../../entities/node.entity';
import { ScheduleDetectionService } from './schedule-detection.service';
import { EnhancedDelayedJobManagerService } from './enhanced-delayed-job-manager.service';
import { IScheduleParameters } from '../../interfaces/core/utility/schedule.interface';

/**
 * Interface cho node update result
 */
interface INodeUpdateResult {
    success: boolean;
    isScheduleNode: boolean;
    jobCreated?: boolean;
    jobId?: string;
    nextExecutionTime?: number;
    error?: string;
}

/**
 * Interface cho node update data
 */
interface INodeUpdateData {
    parameters?: any;
    name?: string;
    disabled?: boolean;
    [key: string]: any;
}

@Injectable()
export class NodeUpdateInterceptorService {
    private readonly logger = new Logger(NodeUpdateInterceptorService.name);
    
    constructor(
        @InjectRepository(Node)
        private readonly nodeRepository: Repository<Node>,
        
        private readonly scheduleDetectionService: ScheduleDetectionService,
        private readonly delayedJobManager: EnhancedDelayedJobManagerService
    ) {}
    
    /**
     * Handle node update với schedule logic
     */
    async handleNodeUpdate(
        nodeId: string,
        updateData: INodeUpdateData
    ): Promise<INodeUpdateResult> {
        try {
            // 1. Check if node is schedule type
            const isScheduleNode = await this.scheduleDetectionService.isScheduleNode(nodeId);
            
            if (!isScheduleNode) {
                // Regular node update - không cần schedule logic
                return {
                    success: true,
                    isScheduleNode: false
                };
            }
            
            // 2. Handle schedule node update
            return await this.handleScheduleNodeUpdate(nodeId, updateData);
            
        } catch (error) {
            this.logger.error(`Failed to handle node update: ${nodeId}`, error);
            return {
                success: false,
                isScheduleNode: false,
                error: error.message
            };
        }
    }
    
    /**
     * Handle schedule node specific update
     */
    private async handleScheduleNodeUpdate(
        nodeId: string,
        updateData: INodeUpdateData
    ): Promise<INodeUpdateResult> {
        try {
            // Get current node info
            const nodeInfo = await this.scheduleDetectionService.getNodeWithDefinition(nodeId);
            
            if (!nodeInfo) {
                throw new Error(`Schedule node not found: ${nodeId}`);
            }
            
            // Check if parameters are being updated
            if (!updateData.parameters) {
                // No parameters update - regular schedule node update
                return {
                    success: true,
                    isScheduleNode: true
                };
            }
            
            const newScheduleParams = updateData.parameters as IScheduleParameters;
            
            // Validate schedule parameters
            const validation = this.validateScheduleParameters(newScheduleParams);
            if (!validation.isValid) {
                throw new Error(`Invalid schedule parameters: ${validation.errors.join(', ')}`);
            }
            
            // Handle job creation/update if schedule is active
            let jobResult: any = null;
            let jobCreated = false;
            if (newScheduleParams.is_active) {
                jobResult = await this.delayedJobManager.createOrUpdateDelayedJob(
                    nodeId,
                    nodeInfo.workflowId,
                    newScheduleParams
                );

                if (!jobResult.isValid) {
                    throw new Error(`Failed to create delayed job: ${jobResult.error}`);
                }

                jobCreated = true;

                // Update parameters với job info
                updateData.parameters = {
                    ...newScheduleParams,
                    jobId: jobResult.jobId,
                    nextExecutionTime: jobResult.nextExecutionTime,
                    jobCreatedAt: Date.now()
                };
            } else {
                // Schedule is inactive - cancel existing job if any
                if (newScheduleParams.jobId) {
                    await this.delayedJobManager.cancelJob(newScheduleParams.jobId);
                }

                // Clear job info
                updateData.parameters = {
                    ...newScheduleParams,
                    jobId: undefined,
                    nextExecutionTime: undefined
                };
            }

            return {
                success: true,
                isScheduleNode: true,
                jobCreated,
                jobId: jobResult?.jobId,
                nextExecutionTime: jobResult?.nextExecutionTime
            };
            
        } catch (error) {
            this.logger.error(`Failed to handle schedule node update: ${nodeId}`, error);
            return {
                success: false,
                isScheduleNode: true,
                error: error.message
            };
        }
    }
    
    /**
     * Validate schedule parameters
     */
    private validateScheduleParameters(params: IScheduleParameters): {
        isValid: boolean;
        errors: string[];
    } {
        const errors: string[] = [];
        
        // Check required fields
        if (!params.schedule_type) {
            errors.push('schedule_type is required');
        }
        
        if (!params.execution_behavior) {
            errors.push('execution_behavior is required');
        }
        
        if (params.is_active === undefined || params.is_active === null) {
            errors.push('is_active is required');
        }
        
        // Validate schedule type specific configs
        switch (params.schedule_type) {
            case 'once':
                if (!params.once_config?.datetime) {
                    errors.push('once_config.datetime is required for ONCE schedule');
                }
                break;
                
            case 'daily':
                if (!params.daily_config) {
                    errors.push('daily_config is required for DAILY schedule');
                } else {
                    if (params.daily_config.hour < 0 || params.daily_config.hour > 23) {
                        errors.push('daily_config.hour must be between 0-23');
                    }
                    if (params.daily_config.minute < 0 || params.daily_config.minute > 59) {
                        errors.push('daily_config.minute must be between 0-59');
                    }
                }
                break;
                
            case 'weekly':
                if (!params.weekly_config?.days_of_week?.length) {
                    errors.push('weekly_config.days_of_week is required for WEEKLY schedule');
                }
                break;
                
            case 'monthly':
                if (!params.monthly_config) {
                    errors.push('monthly_config is required for MONTHLY schedule');
                }
                break;
                
            case 'interval':
                if (!params.interval_config?.value || !params.interval_config?.type) {
                    errors.push('interval_config.value and type are required for INTERVAL schedule');
                }
                break;
                
            case 'cron':
                if (!params.cron_config?.expression) {
                    errors.push('cron_config.expression is required for CRON schedule');
                }
                break;
        }
        
        // Validate execution behavior specific fields
        if (params.execution_behavior === 'start_from_node' && !params.target_node_id) {
            errors.push('target_node_id is required for START_FROM_NODE behavior');
        }
        
        if (params.execution_behavior === 'trigger_event' && !params.event_name) {
            errors.push('event_name is required for TRIGGER_EVENT behavior');
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }
    
    /**
     * Handle node creation (for schedule nodes)
     */
    async handleNodeCreation(
        nodeId: string,
        nodeData: any
    ): Promise<INodeUpdateResult> {
        try {
            // Check if newly created node is schedule type
            const isScheduleNode = await this.scheduleDetectionService.isScheduleNode(nodeId);
            
            if (!isScheduleNode) {
                return {
                    success: true,
                    isScheduleNode: false
                };
            }
            
            // For new schedule nodes, handle initial job creation if active
            if (nodeData.parameters?.is_active) {
                return await this.handleScheduleNodeUpdate(nodeId, nodeData);
            }
            
            return {
                success: true,
                isScheduleNode: true
            };
            
        } catch (error) {
            this.logger.error(`Failed to handle node creation: ${nodeId}`, error);
            return {
                success: false,
                isScheduleNode: false,
                error: error.message
            };
        }
    }
    
    /**
     * Handle node deletion (cleanup jobs)
     */
    async handleNodeDeletion(nodeId: string): Promise<void> {
        try {
            // Get node info before deletion
            const node = await this.nodeRepository.findOne({ where: { id: nodeId } });
            
            if (node && node.parameters) {
                const scheduleParams = node.parameters as IScheduleParameters;
                
                // Cancel any existing job
                if (scheduleParams.jobId) {
                    await this.delayedJobManager.cancelJob(scheduleParams.jobId);
                    this.logger.log(`Cancelled job ${scheduleParams.jobId} for deleted node: ${nodeId}`);
                }
            }
            
        } catch (error) {
            this.logger.error(`Failed to handle node deletion cleanup: ${nodeId}`, error);
        }
    }
    
    /**
     * Bulk handle multiple schedule nodes (for workflow activation)
     */
    async handleBulkScheduleNodeUpdate(
        nodeIds: string[],
        isActive: boolean
    ): Promise<{
        totalNodes: number;
        successCount: number;
        failureCount: number;
        errors: string[];
    }> {
        const errors: string[] = [];
        let successCount = 0;
        let failureCount = 0;
        
        for (const nodeId of nodeIds) {
            try {
                const node = await this.nodeRepository.findOne({ where: { id: nodeId } });
                
                if (node && node.parameters) {
                    const updatedParams = {
                        ...node.parameters,
                        is_active: isActive
                    };
                    
                    const result = await this.handleNodeUpdate(nodeId, {
                        parameters: updatedParams
                    });
                    
                    if (result.success) {
                        successCount++;
                    } else {
                        failureCount++;
                        errors.push(`Node ${nodeId}: ${result.error}`);
                    }
                }
                
            } catch (error) {
                failureCount++;
                errors.push(`Node ${nodeId}: ${error.message}`);
            }
        }
        
        return {
            totalNodes: nodeIds.length,
            successCount,
            failureCount,
            errors
        };
    }
    
    /**
     * Get schedule node status
     */
    async getScheduleNodeStatus(nodeId: string): Promise<{
        isScheduleNode: boolean;
        isActive?: boolean;
        hasJob?: boolean;
        jobId?: string;
        nextExecutionTime?: number;
        lastExecutionTime?: number;
        executionCount?: number;
    }> {
        try {
            const isScheduleNode = await this.scheduleDetectionService.isScheduleNode(nodeId);
            
            if (!isScheduleNode) {
                return { isScheduleNode: false };
            }
            
            const node = await this.nodeRepository.findOne({ where: { id: nodeId } });
            
            if (node && node.parameters) {
                const params = node.parameters as IScheduleParameters;
                
                return {
                    isScheduleNode: true,
                    isActive: params.is_active,
                    hasJob: !!params.jobId,
                    jobId: params.jobId,
                    nextExecutionTime: params.nextExecutionTime,
                    lastExecutionTime: params.lastExecutionTime,
                    executionCount: params.executionCount || 0
                };
            }
            
            return { isScheduleNode: true };
            
        } catch (error) {
            this.logger.error(`Failed to get schedule node status: ${nodeId}`, error);
            return { isScheduleNode: false };
        }
    }
}
