import { Module, Global } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { TypeOrmModule } from '@nestjs/typeorm';
import { S3Service } from './s3.service';
import { SystemConfigSharedService } from './system-config-shared.service';
import { SystemConfiguration } from '@modules/system-configuration/entities/system-configuration.entity';
import { OpenAiService } from './ai/openai.service';
import { AnthropicService } from './ai/anthropic.service';
import { GoogleAIService } from './ai/google_ai.service';
import { DeepSeekService } from './ai/deepseek.service';
import { XAIService } from './ai/xai.service';
import { RagFileProcessingService } from './ai/rag-file-processing.service';
import { RagMediaProcessingService } from './ai/rag-media-processing.service';
import { RagProductProcessingService } from './ai/rag-product-processing.service';
import { AiProviderHelper } from './ai/helpers/ai-provider.helper';
import { CdnService } from './cdn.service';
import { RedisService } from '@shared/services/redis.service';
import { RunStatusService } from '@shared/services/run-status.service';
import { RecaptchaService } from '@shared/services/recaptcha.service';
import { PdfEditService } from '@/shared/services/pdf/pdf-edit.service';
import { EncryptionService, EncryptionWebsiteService, KeyPairEncryptionService } from './encryption';
import { EncryptionService as SimpleEncryptionService } from './encryption.service';
import { SmsModule } from './sms/sms.module';
import { SepayHubModule } from '@shared/services/sepay-hub';
import { TelegramModule } from './telegram/telegram.module';
import { ZaloModule } from './zalo/zalo.module';
import { GoogleApiModule } from '@shared/services/google';
import { AuthenticatorModule } from './authenticator/authenticator.module';
import { MarketingAiModule } from '@shared/services/marketing-ai';
import { FacebookModule } from './facebook/facebook.module';
import { PayPalModule } from './paypal/paypal.module';
import { StripeModule } from './stripe/stripe.module';
import { AhamoveService } from './shipment/ahamove/ahamove.service';
import { GHNService } from './shipment/ghn/ghn.service';
import { GHTKService } from './shipment/ghtk/ghtk.service';
import { ConfigModule } from '@nestjs/config';
import { ApiKeyEncryptionHelper } from '@/modules/models/helpers/api-key-encryption.helper';
import { RagApiInterceptor } from '@shared/interceptors/rag-api.interceptor';
import { EmailTrackingService } from './email-tracking.service';
import { UserCampaignHistory } from '@/modules/marketing/user/entities/user-campaign-history.entity';
import { TrackingLinkUtil } from '@/shared/utils/tracking-link.util';
import { AutomationWebModule } from './automation-web/automation-web.module';
import { AutomationWebService } from './automation-web/automation-web.service';

@Global()
@Module({
  imports: [
    TypeOrmModule.forFeature([SystemConfiguration, UserCampaignHistory]),
    HttpModule,
    SmsModule,
    SepayHubModule,
    TelegramModule,
    ZaloModule,
    GoogleApiModule,
    AuthenticatorModule,
    MarketingAiModule,
    FacebookModule,
    PayPalModule,
    StripeModule,
    AutomationWebModule,
    ConfigModule,
  ],
  providers: [
    SystemConfigSharedService,
    S3Service,
    OpenAiService,
    AnthropicService,
    GoogleAIService,
    DeepSeekService,
    XAIService,
    RagFileProcessingService,
    RagMediaProcessingService,
    RagProductProcessingService,
    AiProviderHelper,
    CdnService,
    RedisService,
    RunStatusService,
    RecaptchaService,
    PdfEditService,
    {
      provide: 'SimpleEncryptionService',
      useClass: SimpleEncryptionService,
    },
    EncryptionService,
    KeyPairEncryptionService,
    ApiKeyEncryptionHelper,
    RagApiInterceptor,
    EmailTrackingService,
    TrackingLinkUtil,
    GHNService,
    GHTKService,
    AhamoveService,
    EncryptionWebsiteService,
    AutomationWebService,
  ],
  exports: [
    SystemConfigSharedService,
    S3Service,
    OpenAiService,
    AnthropicService,
    GoogleAIService,
    DeepSeekService,
    XAIService,
    RagFileProcessingService,
    RagMediaProcessingService,
    RagProductProcessingService,
    AiProviderHelper,
    CdnService,
    RedisService,
    RunStatusService,
    RecaptchaService,
    PdfEditService,
    'SimpleEncryptionService',
    EncryptionService,
    KeyPairEncryptionService,
    ApiKeyEncryptionHelper,
    SepayHubModule,
    TelegramModule,
    ZaloModule,
    GoogleApiModule,
    AuthenticatorModule,
    MarketingAiModule,
    FacebookModule,
    PayPalModule,
    StripeModule,
    RagApiInterceptor,
    EmailTrackingService,
    TrackingLinkUtil,
    GHNService,
    GHTKService,
    AhamoveService,
    EncryptionWebsiteService,
    AutomationWebModule,
    AutomationWebService,
  ],
})
export class ServicesModule {}
