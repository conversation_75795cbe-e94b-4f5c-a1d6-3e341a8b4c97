import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import {
  Observable,
  Subject,
  fromEvent,
  map,
  catchError,
  of,
  EMPTY,
} from 'rxjs';
import { AppException, ErrorCode } from '@/common/exceptions';
import { ConfigService } from '@/config/config.service';
import {
  CreateQRCodeSessionDto,
  QRCodeSessionResponseDto,
  QRCodeSessionStatusDto,
  QRCodeSseMessage,
} from '../dto/zalo-qr-code-sse.dto';

/**
 * Interface cho session metadata tracking
 */
interface SessionMetadata {
  user_id: number;
  integration_id: string;
  created_at: Date;
  expires_at: Date;
  source: string;
  ip_address?: string;
}

/**
 * Service xử lý QR Code SSE proxy đến automation-web
 */
@Injectable()
export class ZaloQRCodeSseService {
  private readonly logger = new Logger(ZaloQRCodeSseService.name);
  private readonly automationWebBaseUrl: string;

  // Session tracking
  private readonly sessionMetadata = new Map<string, SessionMetadata>();
  private readonly lastRequestTime = new Map<number, number>();

  // Constants
  private readonly RATE_LIMIT_COOLDOWN = 30000; // 30 seconds
  private readonly SESSION_TIMEOUT = 5 * 60 * 1000; // 5 minutes

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.automationWebBaseUrl =
      this.configService.getAutomationWebConfig().apiUrl;
    this.logger.log(`Automation Web Base URL: ${this.automationWebBaseUrl}`);
  }

  /**
   * Kiểm tra rate limit cho user
   */
  private checkRateLimit(userId: number): void {
    const now = Date.now();
    const lastRequest = this.lastRequestTime.get(userId) || 0;

    if (now - lastRequest < this.RATE_LIMIT_COOLDOWN) {
      const remainingTime = Math.ceil(
        (this.RATE_LIMIT_COOLDOWN - (now - lastRequest)) / 1000,
      );
      throw new AppException(
        ErrorCode.RATE_LIMIT_EXCEEDED,
        `Vui lòng đợi ${remainingTime} giây trước khi tạo QR session mới`,
      );
    }

    this.lastRequestTime.set(userId, now);
  }

  /**
   * Track session metadata
   */
  private trackSession(
    sessionId: string,
    userId: number,
    integrationId: string,
  ): void {
    this.sessionMetadata.set(sessionId, {
      user_id: userId,
      integration_id: integrationId,
      created_at: new Date(),
      expires_at: new Date(Date.now() + this.SESSION_TIMEOUT),
      source: 'redai_app',
    });

    this.logger.log(`Session tracked: ${sessionId} for user ${userId}`);
  }

  /**
   * Enhanced SSE event processing
   */
  private processSSEEvent(eventData: any, sessionId: string): QRCodeSseMessage {
    const metadata = this.sessionMetadata.get(sessionId);

    // Add context to events
    const enhancedData = {
      ...eventData,
      session_metadata: {
        user_id: metadata?.user_id,
        integration_id: metadata?.integration_id,
        session_age: metadata ? Date.now() - metadata.created_at.getTime() : 0,
      },
      timestamp: new Date().toISOString(),
    };

    // Log important events
    if (['qr_scanned', 'login_success', 'error'].includes(eventData.event)) {
      this.logger.log(`Important SSE event: ${eventData.event}`, {
        session_id: sessionId,
        user_id: metadata?.user_id,
        event_data: eventData,
      });
    }

    return {
      id: `sse-${Date.now()}`,
      event: eventData.event || 'message',
      data: JSON.stringify(enhancedData),
    };
  }

  /**
   * Tạo QR code session thông qua automation-web
   */
  async createQRCodeSession(
    userId: number,
    createDto: CreateQRCodeSessionDto,
  ): Promise<QRCodeSessionResponseDto> {
    try {
      // Check rate limit
      this.checkRateLimit(userId);

      this.logger.log(
        `Creating QR code session for user ${userId}, integration: ${createDto.integrationId}`,
      );

      const response = await this.httpService.axiosRef.post(
        `${this.automationWebBaseUrl}/zalo/qr-code`,
        {
          integration_id: createDto.integrationId,
          user_id: userId, // Gửi user_id để automation-web có thể gọi webhook
        },
        {
          timeout: 30000, // 30 seconds timeout
        },
      );

      if (response.data.code !== 200) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          response.data.message || 'Lỗi tạo QR code session',
        );
      }

      const result = response.data.result;

      this.logger.log(
        `QR code session created successfully: ${result.session_id}`,
      );

      // Track session metadata
      this.trackSession(result.session_id, userId, createDto.integrationId);

      return {
        sessionId: result.session_id,
        qrCodeBase64: result.qr_code_base64,
        expiresAt: result.expires_at,
        integrationId: result.integration_id,
        message: response.data.message,
      };
    } catch (error) {
      this.logger.error(
        `Error creating QR code session: ${error.message}`,
        error.stack,
      );

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Không thể tạo QR code session',
      );
    }
  }

  /**
   * Lấy trạng thái session từ automation-web
   */
  async getSessionStatus(
    sessionId: string,
  ): Promise<QRCodeSessionStatusDto | null> {
    try {
      this.logger.log(`Getting session status for: ${sessionId}`);

      const response = await this.httpService.axiosRef.get(
        `${this.automationWebBaseUrl}/zalo/qr-code/status/${sessionId}`,
        {
          timeout: 10000, // 10 seconds timeout
        },
      );

      if (response.data.code === 404) {
        this.logger.warn(`Session not found: ${sessionId}`);
        return null;
      }

      if (response.data.code !== 200) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          response.data.message || 'Lỗi lấy trạng thái session',
        );
      }

      const result = response.data.result;

      return {
        sessionId: result.session_id,
        status: result.status,
        integrationId: result.integration_id,
        createdAt: result.created_at,
        expiresAt: result.expires_at,
      };
    } catch (error) {
      this.logger.error(
        `Error getting session status: ${error.message}`,
        error.stack,
      );

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Không thể lấy trạng thái session',
      );
    }
  }

  /**
   * Kiểm tra automation-web service có khả dụng không
   */
  private async checkAutomationWebHealth(): Promise<boolean> {
    try {
      await this.httpService.axiosRef.get(
        `${this.automationWebBaseUrl}/health`,
        {
          timeout: 5000,
        },
      );
      return true;
    } catch (error) {
      this.logger.warn(`Automation-web health check failed: ${error.message}`);
      return false;
    }
  }

  /**
   * Tạo SSE stream proxy đến automation-web
   */
  createSseStream(sessionId: string): Observable<QRCodeSseMessage> {
    this.logger.log(`Creating SSE stream for session: ${sessionId}`);

    return new Observable<QRCodeSseMessage>((observer) => {
      // Kiểm tra automation-web trước khi tạo stream
      this.checkAutomationWebHealth()
        .then((isHealthy) => {
          if (!isHealthy) {
            observer.next({
              id: `error-${Date.now()}`,
              event: 'error',
              data: JSON.stringify({
                message: 'Automation-web service không khả dụng',
                sessionId,
                error: 'Service unavailable',
                timestamp: new Date().toISOString(),
              }),
            });
            observer.complete();
            return;
          }

          // Nếu service khả dụng, tiếp tục tạo SSE connection
          startSSEConnection();
        })
        .catch((error) => {
          observer.next({
            id: `error-${Date.now()}`,
            event: 'error',
            data: JSON.stringify({
              message: 'Lỗi kiểm tra automation-web service',
              sessionId,
              error: error.message,
              timestamp: new Date().toISOString(),
            }),
          });
          observer.complete();
        });

      let isActive = true;
      let sseRequest: any = null;
      let retryCount = 0;
      const maxRetries = 3;
      const baseDelay = 2000; // 2 seconds

      const startSSEConnection = () => {
        // Send connection confirmation
        observer.next({
          id: `connection-${Date.now()}`,
          event: 'connected',
          data: JSON.stringify({
            message: 'Kết nối SSE QR code thành công',
            sessionId,
            timestamp: new Date().toISOString(),
          }),
        });

        // Kết nối SSE stream từ automation-web
        const connectSSE = async () => {
          try {
            const sseUrl = `${this.automationWebBaseUrl}/zalo/qr-code/stream/${sessionId}`;
            this.logger.log(`Connecting to automation-web SSE: ${sseUrl}`);

            // Kiểm tra automation-web có khả dụng không trước
            try {
              await this.httpService.axiosRef.get(
                `${this.automationWebBaseUrl}/health`,
                {
                  timeout: 5000,
                },
              );
            } catch (healthError) {
              retryCount++;
              this.logger.error(
                `Automation-web service not available (attempt ${retryCount}/${maxRetries}): ${healthError.message}`,
              );

              if (retryCount >= maxRetries) {
                observer.next({
                  id: `error-${Date.now()}`,
                  event: 'error',
                  data: JSON.stringify({
                    message:
                      'Automation-web service không khả dụng sau nhiều lần thử',
                    sessionId,
                    error: 'Service unavailable',
                    retryCount,
                    timestamp: new Date().toISOString(),
                  }),
                });
                observer.complete();
                return;
              }

              // Retry với exponential backoff
              const delay = baseDelay * Math.pow(2, retryCount - 1);
              this.logger.log(`Retrying connection in ${delay}ms...`);
              setTimeout(() => {
                if (isActive) {
                  connectSSE();
                }
              }, delay);
              return;
            }

            // Sử dụng axios để stream SSE
            sseRequest = this.httpService.axiosRef.get(sseUrl, {
              responseType: 'stream',
              timeout: 0, // No timeout for SSE
              headers: {
                Accept: 'text/event-stream',
                'Cache-Control': 'no-cache',
              },
            });

            const response = await sseRequest;

            if (!isActive) {
              response.data.destroy();
              return;
            }

            // Parse SSE stream
            let buffer = '';

            response.data.on('data', (chunk: Buffer) => {
              if (!isActive) return;

              buffer += chunk.toString();
              const lines = buffer.split('\n');
              buffer = lines.pop() || ''; // Keep incomplete line in buffer

              for (const line of lines) {
                if (line.startsWith('data: ')) {
                  try {
                    const eventData = JSON.parse(line.substring(6));

                    this.logger.debug(`SSE event received:`, eventData);

                    // Process and forward enhanced event to client
                    const enhancedEvent = this.processSSEEvent(
                      eventData,
                      sessionId,
                    );
                    observer.next(enhancedEvent);

                    // Auto complete on terminal events
                    if (
                      ['login_success', 'qr_expired', 'error'].includes(
                        eventData.event,
                      )
                    ) {
                      this.logger.log(
                        `Terminal SSE event received: ${eventData.event}`,
                      );
                      observer.complete();
                      return;
                    }
                  } catch (parseError) {
                    this.logger.error(
                      `Error parsing SSE data: ${parseError.message}`,
                    );
                  }
                }
              }
            });

            response.data.on('end', () => {
              this.logger.log(`SSE stream ended for session: ${sessionId}`);
              if (isActive) {
                observer.complete();
              }
            });

            response.data.on('error', (error: any) => {
              this.logger.error(`SSE stream error: ${error.message}`);
              if (isActive) {
                observer.next({
                  id: `error-${Date.now()}`,
                  event: 'error',
                  data: JSON.stringify({
                    message: 'Lỗi SSE stream từ automation-web',
                    sessionId,
                    error: error.message,
                    timestamp: new Date().toISOString(),
                  }),
                });
                observer.error(error);
              }
            });
          } catch (error) {
            retryCount++;
            this.logger.error(
              `Error connecting to SSE (attempt ${retryCount}/${maxRetries}): ${error.message}`,
            );

            if (retryCount >= maxRetries) {
              if (isActive) {
                observer.next({
                  id: `error-${Date.now()}`,
                  event: 'error',
                  data: JSON.stringify({
                    message:
                      'Không thể kết nối SSE đến automation-web sau nhiều lần thử',
                    sessionId,
                    error: error.message,
                    retryCount,
                    timestamp: new Date().toISOString(),
                  }),
                });
                observer.complete(); // Complete thay vì error để tránh crash
              }
            } else {
              // Retry với exponential backoff
              const delay = baseDelay * Math.pow(2, retryCount - 1);
              this.logger.log(`Retrying SSE connection in ${delay}ms...`);
              setTimeout(() => {
                if (isActive) {
                  connectSSE();
                }
              }, delay);
            }
          }
        };

        // Bắt đầu kết nối SSE
        connectSSE();

        // Cleanup function
        return () => {
          this.logger.log(
            `Cleaning up SSE connection for session: ${sessionId}`,
          );
          isActive = false;

          if (sseRequest) {
            try {
              sseRequest
                .then((response: any) => {
                  if (response?.data?.destroy) {
                    response.data.destroy();
                  }
                })
                .catch(() => {
                  // Ignore cleanup errors
                });
            } catch (error) {
              // Ignore cleanup errors
            }
          }
        };
      }; // Đóng startSSEConnection function

      // Cleanup function
      return () => {
        this.logger.log(`Cleaning up SSE connection for session: ${sessionId}`);
        isActive = false;

        if (sseRequest) {
          try {
            sseRequest
              .then((response: any) => {
                if (response?.data?.destroy) {
                  response.data.destroy();
                }
              })
              .catch(() => {
                // Ignore cleanup errors
              });
          } catch (error) {
            // Ignore cleanup errors
          }
        }
      };
    });
  }
}
