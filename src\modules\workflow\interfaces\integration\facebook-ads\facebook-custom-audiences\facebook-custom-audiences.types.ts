/**
 * @file Facebook Custom Audiences Types & Enums
 * 
 * Định nghĩa các enums và types cho Facebook Custom Audiences integration
 * Theo patterns từ Make.com chuẩn
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

// =================================================================
// SECTION 1: FACEBOOK CUSTOM AUDIENCES ENUMS
// =================================================================

/**
 * Facebook Custom Audiences specific operations (theo Make.com chuẩn)
 */
export enum EFacebookCustomAudiencesOperation {
    // === CUSTOM AUDIENCES OPERATIONS - Các thao tác đối tượng tùy chỉnh ===
    /** Create a Custom Audience - Tạo đối tượng tùy chỉnh */
    CREATE_CUSTOM_AUDIENCE = 'createCustomAudience',
    /** Add Emails to a Custom Audience - Thêm email vào đối tượng tùy chỉnh */
    ADD_EMAILS_TO_CUSTOM_AUDIENCE = 'addEmailsToCustomAudience',
    /** Add Users to a Custom Audience - Thêm người dùng với multi-key matches */
    ADD_USERS_TO_CUSTOM_AUDIENCE = 'addUsersToCustomAudience',
    /** Remove Audience Members - Xóa thành viên theo email hoặc external ID */
    REMOVE_AUDIENCE_MEMBERS = 'removeAudienceMembers',

    // === LOOKALIKE AUDIENCES OPERATIONS - Các thao tác đối tượng tương tự ===
    /** Create a Lookalike Audience - Tạo đối tượng tương tự */
    CREATE_LOOKALIKE_AUDIENCE = 'createLookalikeAudience',

    // === PAGE FAN LOOKALIKE AUDIENCES - Đối tượng tương tự fan trang ===
    /** Create a Page Fan Lookalike Audience - Tạo đối tượng tương tự dựa trên fan trang */
    CREATE_PAGE_FAN_LOOKALIKE_AUDIENCE = 'createPageFanLookalikeAudience',

    // === CAMPAIGN AND AD SET LOOKALIKE AUDIENCE - Đối tượng tương tự chiến dịch ===
    /** Create a Campaign or Ad Set Conversion Lookalikes - Tạo đối tượng tương tự từ chiến dịch/ad set */
    CREATE_CAMPAIGN_CONVERSION_LOOKALIKES = 'createCampaignConversionLookalikes',

    // === VALUE-BASED CUSTOM AUDIENCE - Đối tượng tùy chỉnh dựa trên giá trị ===
    /** Create a Value-Based Custom Audience - Tạo đối tượng tùy chỉnh dựa trên giá trị */
    CREATE_VALUE_BASED_CUSTOM_AUDIENCE = 'createValueBasedCustomAudience',
    /** Populate a Seed Audience - Điền dữ liệu cho seed audience */
    POPULATE_SEED_AUDIENCE = 'populateSeedAudience',
    /** Create a Value-Based Lookalike - Tạo đối tượng tương tự dựa trên giá trị */
    CREATE_VALUE_BASED_LOOKALIKE = 'createValueBasedLookalike'
}

/**
 * Facebook Custom Audience types - Loại đối tượng tùy chỉnh Facebook
 */
export enum EFacebookCustomAudienceType {
    /** Custom Audience - Đối tượng tùy chỉnh */
    CUSTOM = 'CUSTOM',
    /** Website Custom Audience - Đối tượng tùy chỉnh từ website */
    WEBSITE = 'WEBSITE',
    /** App Activity Custom Audience - Đối tượng tùy chỉnh từ hoạt động ứng dụng */
    APP_ACTIVITY = 'APP_ACTIVITY',
    /** Offline Activity Custom Audience - Đối tượng tùy chỉnh từ hoạt động offline */
    OFFLINE_ACTIVITY = 'OFFLINE_ACTIVITY',
    /** Video Engagement Custom Audience - Đối tượng tùy chỉnh từ tương tác video */
    VIDEO_ENGAGEMENT = 'VIDEO_ENGAGEMENT',
    /** Lead Generation Custom Audience - Đối tượng tùy chỉnh từ tạo khách hàng tiềm năng */
    LEAD_GENERATION = 'LEAD_GENERATION'
}

/**
 * Facebook Custom Audience subtype - Loại phụ đối tượng tùy chỉnh
 */
export enum EFacebookCustomAudienceSubtype {
    /** Custom - Tùy chỉnh */
    CUSTOM = 'CUSTOM',
    /** Website - Website */
    WEBSITE = 'WEBSITE',
    /** App - Ứng dụng */
    APP = 'APP',
    /** Offline Conversion - Chuyển đổi offline */
    OFFLINE_CONVERSION = 'OFFLINE_CONVERSION',
    /** Claim - Yêu cầu */
    CLAIM = 'CLAIM',
    /** Partner - Đối tác */
    PARTNER = 'PARTNER',
    /** Managed - Được quản lý */
    MANAGED = 'MANAGED',
    /** Video - Video */
    VIDEO = 'VIDEO',
    /** Lookalike - Tương tự */
    LOOKALIKE = 'LOOKALIKE'
}

/**
 * Facebook Lookalike audience spec - Thông số đối tượng tương tự
 */
export enum EFacebookLookalikeSpec {
    /** Ratio - Tỷ lệ (1-10%) */
    RATIO = 'ratio',
    /** Country - Quốc gia */
    COUNTRY = 'country',
    /** Type - Loại */
    TYPE = 'type'
}

/**
 * Facebook audience match keys - Khóa khớp đối tượng
 */
export enum EFacebookAudienceMatchKey {
    /** Email - Email */
    EMAIL = 'EMAIL',
    /** Phone - Số điện thoại */
    PHONE = 'PHONE',
    /** Mobile Advertiser ID - ID quảng cáo di động */
    MADID = 'MADID',
    /** Facebook User ID - ID người dùng Facebook */
    FN = 'FN',
    /** Last Name - Họ */
    LN = 'LN',
    /** First Name - Tên */
    FI = 'FI',
    /** Date of Birth - Ngày sinh */
    DB = 'DB',
    /** Gender - Giới tính */
    GEN = 'GEN',
    /** City - Thành phố */
    CT = 'CT',
    /** State - Bang/Tỉnh */
    ST = 'ST',
    /** Zip Code - Mã bưu điện */
    ZIP = 'ZIP',
    /** Country - Quốc gia */
    COUNTRY = 'COUNTRY',
    /** External ID - ID bên ngoài */
    EXTERN_ID = 'EXTERN_ID'
}

/**
 * Facebook audience operation type - Loại thao tác đối tượng
 */
export enum EFacebookAudienceOperationType {
    /** Add - Thêm */
    ADD = 'ADD',
    /** Remove - Xóa */
    REMOVE = 'REMOVE',
    /** Replace - Thay thế */
    REPLACE = 'REPLACE'
}

/**
 * Facebook lookalike audience type - Loại đối tượng tương tự
 */
export enum EFacebookLookalikeAudienceType {
    /** Similarity - Tương tự */
    SIMILARITY = 'similarity',
    /** Reach - Tiếp cận */
    REACH = 'reach'
}

/**
 * Facebook audience status - Trạng thái đối tượng
 */
export enum EFacebookAudienceStatus {
    /** Active - Hoạt động */
    ACTIVE = 'ACTIVE',
    /** Inactive - Không hoạt động */
    INACTIVE = 'INACTIVE',
    /** Processing - Đang xử lý */
    PROCESSING = 'PROCESSING',
    /** Ready - Sẵn sàng */
    READY = 'READY',
    /** Too Small - Quá nhỏ */
    TOO_SMALL = 'TOO_SMALL'
}
