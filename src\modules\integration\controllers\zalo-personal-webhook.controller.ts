import { Controller, Post, Body, HttpStatus, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { ApiResponseDto as AppApiResponse } from '@/common/response';
import {
  ZaloPersonalWebhookService,
  ZaloPersonalLoginWebhookData,
} from '../services/zalo-personal-webhook.service';
import { ZaloPersonalIntegrationResponseDto } from '../dto/zalo-personal/zalo-personal-integration-response.dto';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller cho Zalo Personal Webhook (không cần authentication)
 * Endpoint này được gọi bởi automation-web service
 */
@ApiTags(SWAGGER_API_TAGS.INTEGRATION)
@Controller('integration/zalo-personal/webhook')
export class ZaloPersonalWebhookController {
  private readonly logger = new Logger(ZaloPersonalWebhookController.name);

  constructor(
    private readonly zaloPersonalWebhookService: ZaloPersonalWebhookService,
  ) {}

  /**
   * Webhook callback từ automation-web khi đăng nhập thành công
   * Endpoint này KHÔNG cần authentication để automation-web có thể gọi
   */
  @Post('login-success')
  @ApiOperation({
    summary: 'Webhook callback từ automation-web khi đăng nhập thành công',
    description:
      'Endpoint này được gọi bởi automation-web sau khi QR code đăng nhập thành công. KHÔNG cần JWT token.',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Tạo integration thành công',
    type: ZaloPersonalIntegrationResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu webhook không hợp lệ',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Lỗi server khi xử lý webhook',
  })
  async handleLoginSuccessWebhook(
    @Body() webhookData: ZaloPersonalLoginWebhookData,
  ): Promise<AppApiResponse<ZaloPersonalIntegrationResponseDto>> {
    try {
      this.logger.log(
        `Received webhook from automation-web for user ${webhookData.userId}`,
      );

      const result =
        await this.zaloPersonalWebhookService.handleLoginSuccessWebhook(
          webhookData,
        );

      this.logger.log(
        `Successfully processed webhook for user ${webhookData.userId}, integration ID: ${result.id}`,
      );

      return AppApiResponse.success(
        result,
        'Tạo Zalo Personal Integration từ đăng nhập thành công',
      );
    } catch (error) {
      this.logger.error(
        `Failed to process webhook: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Webhook callback khi có lỗi đăng nhập
   */
  @Post('login-error')
  @ApiOperation({
    summary: 'Webhook callback khi có lỗi đăng nhập',
    description:
      'Endpoint này được gọi bởi automation-web khi có lỗi trong quá trình đăng nhập',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xử lý lỗi thành công',
  })
  async handleLoginErrorWebhook(
    @Body()
    webhookData: {
      userId: number;
      integrationId: string;
      error: string;
      errorMessage: string;
    },
  ): Promise<AppApiResponse<{ success: boolean; message: string }>> {
    try {
      this.logger.warn(
        `Received login error webhook for user ${webhookData.userId}: ${webhookData.error}`,
      );

      const result =
        await this.zaloPersonalWebhookService.handleLoginErrorWebhook(
          webhookData,
        );

      return AppApiResponse.success(result, 'Xử lý lỗi đăng nhập thành công');
    } catch (error) {
      this.logger.error(
        `Failed to process login error webhook: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Webhook callback khi session hết hạn
   */
  @Post('session-expired')
  @ApiOperation({
    summary: 'Webhook callback khi session hết hạn',
    description:
      'Endpoint này được gọi bởi automation-web khi session đăng nhập hết hạn',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xử lý session hết hạn thành công',
  })
  async handleSessionExpiredWebhook(
    @Body()
    webhookData: {
      userId: number;
      integrationId: string;
      sessionId: string;
    },
  ): Promise<AppApiResponse<{ success: boolean; message: string }>> {
    try {
      this.logger.warn(
        `Received session expired webhook for user ${webhookData.userId}`,
      );

      const result =
        await this.zaloPersonalWebhookService.handleSessionExpiredWebhook(
          webhookData,
        );

      return AppApiResponse.success(result, 'Xử lý session hết hạn thành công');
    } catch (error) {
      this.logger.error(
        `Failed to process session expired webhook: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Health check endpoint cho automation-web
   */
  @Post('health')
  @ApiOperation({
    summary: 'Health check endpoint cho automation-web',
    description: 'Endpoint để automation-web kiểm tra kết nối với main app',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Service healthy',
  })
  async healthCheck(): Promise<
    AppApiResponse<{ status: string; timestamp: number }>
  > {
    return AppApiResponse.success(
      {
        status: 'healthy',
        timestamp: Date.now(),
      },
      'Zalo Personal Webhook service is healthy',
    );
  }
}
