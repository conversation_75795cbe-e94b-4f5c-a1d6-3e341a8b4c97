/**
 * Enum định nghĩa các tên queue trong hệ thống
 * Đ<PERSON><PERSON> bảo tính nhất quán khi sử dụng các queue trong ứng dụng
 */
export enum QueueName {
  /**
   * Queue xử lý email
   */
  EMAIL = 'email',

  /**
   * Queue xử lý tin nhắn SMS
   */
  SMS = 'sms',

  /**
   * Queue xử lý thông báo
   */
  NOTIFICATION = 'notification',

  /**
   * Queue xử lý dữ liệu
   */
  DATA_PROCESS = 'data-process',

  SEND_SYSTEM_EMAIL = 'send-system-email',

  /**
   * Queue xử lý crawl URL cho user
   */
  CRAWL_URL = 'crawl-url',

  /**
   * Queue xử lý crawl URL cho admin
   */
  CRAWL_URL_ADMIN = 'crawl-url-admin',

  /**
   * Queue xử lý email hệ thống với agent
   */
  AGENT = 'agent',

  /**
   * Queue xử lý email hệ thống
   */
  EMAIL_SYSTEM = 'email-system',

  /**
   * Queue xử lý email marketing
   */
  EMAIL_MARKETING = 'email-marketing',

  /**
   * Queue xử lý SMS marketing
   */
  SMS_MARKETING = 'sms-marketing',

  /**
   * Queue xử lý Zalo ZNS
   */
  ZALO_ZNS = 'zalo-zns',

  /**
   * Queue xử lý Zalo Consultation Sequence
   */
  ZALO_CONSULTATION_SEQUENCE = 'zalo-consultation-sequence',

  /**
   * Queue xử lý Zalo Group Message Sequence
   */
  ZALO_GROUP_MESSAGE_SEQUENCE = 'zalo-group-message-sequence',

  /**
   * Queue xử lý Zalo Video Tracking
   */
  ZALO_VIDEO_TRACKING = 'zalo-video-tracking',

  /**
   * Queue xử lý lên lịch xuất bản bài viết Zalo
   */
  ZALO_ARTICLE_SCHEDULER = 'zalo-article-scheduler',

  /**
   * Queue xử lý tracking bài viết Zalo
   */
  ZALO_ARTICLE_TRACKING = 'zalo-article-tracking',

  /**
   * Queue xử lý Zalo Webhook
   */
  ZALO_WEBHOOK = 'zalo-webhook',

  /**
   * Queue xử lý Zalo AI Response
   */
  ZALO_AI_RESPONSE = 'zalo-ai-response',

  /**
   * Queue xử lý Calendar
   */
  CALENDAR = 'calendar',

  /**
   * Queue xử lý Fine-tuning
   */
  FINE_TUNE = 'fine-tune',

  /**
   * Queue xử lý tích hợp (Integration)
   */
  INTEGRATION = 'integration',

  /**
   * Queue xử lý đồng bộ Zalo audience
   */
  ZALO_AUDIENCE_SYNC = 'zalo-audience-sync',

  /**
   * Queue xử lý Zalo Upload (Image, File, GIF)
   */
  ZALO_UPLOAD = 'zalo-upload',

  /**
   * Queue xử lý In-App AI processing
   */
  IN_APP_AI = 'in-app-ai',

  /**
   * Queue xử lý Webhook Events
   */
  WEBHOOK = 'webhook',

  /**
   * Queue xử lý External Webhooks
   */
  EXTERNAL_WEBHOOK = 'external-webhook',

  // Workflow queues - only real execution
  WORKFLOW_EXECUTION = 'workflow-execution',

  /**
   * Queue xử lý page navigation từ app khác
   */
  PAGE_NAVIGATION = 'page-navigation',


  WEBSITE_AI = 'website-ai',
}

/**
 * Enum định nghĩa các tên job trong mỗi queue
 */
export enum EmailJobName {
  /**
   * Job gửi email thông thường
   */
  SEND_EMAIL = 'send-email',

  /**
   * Job gửi email theo mẫu
   */
  SEND_TEMPLATE_EMAIL = 'send-template-email',
}

/**
 * Enum định nghĩa các tên job trong queue SMS
 */
export enum SmsJobName {
  /**8
   * Job gửi SMS thông thường
   */
  SEND_SMS = 'send-sms',

  /**
   * Job gửi SMS theo mẫu
   */
  SEND_TEMPLATE_SMS = 'send-template-sms',

  /**
   * Job gửi SMS hệ thống
   */
  SMS_SYSTEM = 'sms-system',

  /**
   * Job gửi SMS marketing
   */
  SMS_MARKETING = 'sms-marketing',

  /**
   * Job gửi SMS marketing admin
   */
  SMS_MARKETING_ADMIN = 'sms-marketing-admin',
}

/**
 * Enum định nghĩa các tên job trong queue thông báo
 */
export enum NotificationJobName {
  /**
   * Job gửi thông báo
   */
  SEND_NOTIFICATION = 'send-notification',

  /**
   * Job gửi thông báo theo mẫu
   */
  SEND_TEMPLATE_NOTIFICATION = 'send-template-notification',
}

/**
 * Enum định nghĩa các tên job trong queue email system
 */
export enum EmailSystemJobName {
  /**
   * Job gửi email hệ thống theo template
   */
  SEND_TEMPLATE_EMAIL = 'send-template-email',
}

/**
 * Enum định nghĩa các tên job trong queue email marketing
 */
export enum EmailMarketingJobName {
  /**
   * Job gửi email marketing (single email)
   */
  SEND_EMAIL = 'send-email',

  /**
   * Job gửi batch email marketing (multiple emails)
   */
  SEND_BATCH_EMAIL = 'send-batch-email',

  /**
   * Job gửi admin email campaign (single email)
   */
  SEND_ADMIN_EMAIL = 'send-admin-email',

  /**
   * Job gửi batch admin email campaign (multiple emails)
   */
  SEND_BATCH_ADMIN_EMAIL = 'send-batch-admin-email',
}

/**
 * Enum định nghĩa các tên job trong queue Zalo ZNS
 */
export enum ZaloZnsJobName {
  /**
   * Job gửi ZNS đơn lẻ
   */
  SEND_ZNS = 'send-zns',

  /**
   * Job gửi ZNS theo chiến dịch
   */
  SEND_ZNS_CAMPAIGN = 'send-zns-campaign',

  /**
   * Job gửi batch ZNS (multiple messages)
   */
  SEND_BATCH_ZNS = 'send-batch-zns',
}

/**
 * Enum định nghĩa các tên job trong queue Zalo OA Message
 */
export enum ZaloOaMessageJobName {
  /**
   * Job gửi tin nhắn OA đơn lẻ
   */
  SEND_OA_MESSAGE = 'send-oa-message',

  /**
   * Job gửi batch tin nhắn OA
   */
  SEND_BATCH_OA_MESSAGE = 'send-batch-oa-message',

  /**
   * Job gửi chiến dịch tin nhắn OA
   */
  SEND_OA_MESSAGE_CAMPAIGN = 'send-oa-message-campaign',
}

/**
 * Enum định nghĩa các tên job trong queue Zalo Webhook
 */
export enum ZaloWebhookJobName {
  // Message Events
  /**
   * Job xử lý sự kiện người dùng gửi tin nhắn text
   */
  PROCESS_USER_SEND_TEXT = 'process-user-send-text',

  /**
   * Job xử lý sự kiện người dùng gửi hình ảnh
   */
  PROCESS_USER_SEND_IMAGE = 'process-user-send-image',

  /**
   * Job xử lý sự kiện người dùng gửi file
   */
  PROCESS_USER_SEND_FILE = 'process-user-send-file',

  /**
   * Job xử lý sự kiện người dùng gửi sticker
   */
  PROCESS_USER_SEND_STICKER = 'process-user-send-sticker',

  /**
   * Job xử lý sự kiện người dùng gửi vị trí
   */
  PROCESS_USER_SEND_LOCATION = 'process-user-send-location',

  /**
   * Job xử lý sự kiện người dùng gửi link
   */
  PROCESS_USER_SEND_LINK = 'process-user-send-link',

  // Follow Events
  /**
   * Job xử lý sự kiện người dùng follow OA
   */
  PROCESS_USER_FOLLOW = 'process-user-follow',

  /**
   * Job xử lý sự kiện người dùng unfollow OA
   */
  PROCESS_USER_UNFOLLOW = 'process-user-unfollow',

  // User Info Events
  /**
   * Job xử lý sự kiện người dùng submit thông tin
   */
  PROCESS_USER_INFO = 'process-user-info',

  /**
   * Job xử lý sự kiện người dùng gửi feedback
   */
  PROCESS_FEEDBACK = 'process-feedback',

  // Message Status Events
  /**
   * Job xử lý sự kiện người dùng nhận tin nhắn
   */
  PROCESS_USER_RECEIVED_MESSAGE = 'process-user-received-message',

  /**
   * Job xử lý sự kiện người dùng react tin nhắn
   */
  PROCESS_USER_REACTED_MESSAGE = 'process-user-reacted-message',

  // Group Events (GMF)
  /**
   * Job xử lý sự kiện tạo nhóm
   */
  PROCESS_CREATE_GROUP = 'process-create-group',

  /**
   * Job xử lý sự kiện người dùng tham gia nhóm
   */
  PROCESS_USER_JOIN_GROUP = 'process-user-join-group',

  /**
   * Job xử lý sự kiện người dùng yêu cầu tham gia nhóm
   */
  PROCESS_USER_REQUEST_JOIN_GROUP = 'process-user-request-join-group',

  /**
   * Job xử lý sự kiện duyệt thành viên
   */
  PROCESS_REACT_REQUEST_JOIN_GROUP_ACCEPT = 'process-react-request-join-group-accept',

  /**
   * Job xử lý sự kiện từ chối duyệt thành viên
   */
  PROCESS_REACT_REQUEST_JOIN_GROUP_REJECT = 'process-react-request-join-group-reject',

  /**
   * Job xử lý sự kiện thêm phó nhóm
   */
  PROCESS_ADD_GROUP_ADMIN = 'process-add-group-admin',

  /**
   * Job xử lý sự kiện xóa phó nhóm
   */
  PROCESS_REMOVE_GROUP_ADMIN = 'process-remove-group-admin',

  /**
   * Job xử lý sự kiện cập nhật thông tin nhóm
   */
  PROCESS_UPDATE_GROUP_INFO = 'process-update-group-info',

  /**
   * Job xử lý sự kiện thành viên rời nhóm
   */
  PROCESS_USER_LEAVE_GROUP = 'process-user-leave-group',

  /**
   * Job xử lý sự kiện xóa nhóm
   */
  PROCESS_DELETE_GROUP = 'process-delete-group',

  // ZNS Events
  /**
   * Job xử lý sự kiện phản hồi người dùng ZNS
   */
  PROCESS_ZNS_USER_FEEDBACK = 'process-zns-user-feedback',

  /**
   * Job xử lý sự kiện thay đổi quota ZNS
   */
  PROCESS_ZNS_QUOTA_CHANGE = 'process-zns-quota-change',

  /**
   * Job xử lý sự kiện thay đổi loại nội dung ZNS
   */
  PROCESS_ZNS_CONTENT_TYPE_CHANGE = 'process-zns-content-type-change',

  /**
   * Job xử lý sự kiện thay đổi chất lượng template ZNS
   */
  PROCESS_ZNS_TEMPLATE_QUALITY_CHANGE = 'process-zns-template-quality-change',

  /**
   * Job xử lý sự kiện journey hết hạn
   */
  PROCESS_ZNS_JOURNEY_EXPIRED = 'process-zns-journey-expired',

  /**
   * Job xử lý sự kiện journey được tính phí
   */
  PROCESS_ZNS_JOURNEY_CHARGED = 'process-zns-journey-charged',

  /**
   * Job xử lý sự kiện người dùng nhận ZNS
   */
  PROCESS_ZNS_USER_RECEIVED = 'process-zns-user-received',

  /**
   * Job xử lý sự kiện thay đổi trạng thái template
   */
  PROCESS_ZNS_TEMPLATE_STATUS_CHANGE = 'process-zns-template-status-change',

  // AI Response Jobs
  /**
   * Job tạo phản hồi AI
   */
  GENERATE_AI_RESPONSE = 'generate-ai-response',

  /**
   * Job gửi phản hồi AI
   */
  SEND_AI_RESPONSE = 'send-ai-response',
}

/**
 * Enum định nghĩa các tên job trong queue Zalo AI Response
 */
export enum ZaloAiResponseJobName {
  /**
   * Job xử lý raw webhook cho AI response
   */
  PROCESS_RAW_WEBHOOK = 'process-raw-webhook',
}

/**
 * Enum định nghĩa các tên job trong queue crawl URL
 */
export enum CrawlUrlJobName {
  /**
   * Job crawl URL cho user
   */
  CRAWL_URL = 'crawl-url',

  /**
   * Job crawl URL cho admin
   */
  CRAWL_URL_ADMIN = 'crawl-url-admin',
}

/**
 * Enum định nghĩa các tên job trong queue Calendar
 */
export enum CalendarJobName {
  /**
   * Job gửi reminder
   */
  SEND_REMINDER = 'send-reminder',

  /**
   * Job thực thi task
   */
  EXECUTE_TASK = 'execute-task',

  /**
   * Job tạo báo cáo
   */
  GENERATE_REPORT = 'generate-report',

  /**
   * Job tạo recurrence instances
   */
  CREATE_RECURRENCE_INSTANCES = 'create-recurrence-instances',

  /**
   * Job đồng bộ với Google Calendar
   */
  SYNC_GOOGLE_CALENDAR = 'sync-google-calendar',

  /**
   * Job tạo Zoom meeting
   */
  CREATE_ZOOM_MEETING = 'create-zoom-meeting',
}

/**
 * Enum định nghĩa các tên job trong queue Fine-tuning
 */
export enum FineTuneJobName {
  /**
   * Job fine-tune với upload data trong Worker
   */
  FINE_TUNE_UPLOAD_DATA = 'fine-tune-upload-data',

  /**
   * Job fine-tune với file đã upload sẵn
   */
  FINE_TUNE_PROCESS = 'fine-tune-process',

  /**
   * Job monitor fine-tuning status
   */
  FINE_TUNE_MONITOR = 'fine-tune-monitor',

  /**
   * Job polling fine-tuning status với payload đơn giản
   */
  FINE_TUNE_POLLING = 'fine-tune-polling',
}

/**
 * Enum định nghĩa các tên job trong queue Page Navigation
 */
export enum PageNavigationJobName {
  /**
   * Job chuyển trang cho người dùng
   */
  NAVIGATE_TO_PAGE = 'navigate-to-page',

  /**
   * Job thông báo trạng thái trang
   */
  PAGE_STATUS_UPDATE = 'page-status-update',
}

/**
 * Enum định nghĩa các tên job trong queue Integration
 */
export enum IntegrationJobName {
  /**
   * Job test kết nối FPT SMS Brandname
   */
  FPT_BRAND_NAME_TEST_CONNECTION = 'fpt-brand-name-test-connection',
}

/**
 * Enum định nghĩa các tên job trong queue Data Process
 */
export enum DataProcessJobName {
  /**
   * Job tạo nhiều sản phẩm khách hàng cùng lúc
   */
  BULK_CREATE_CUSTOMER_PRODUCTS = 'bulk-create-customer-products',
}

/**
 * Enum định nghĩa các tên job trong queue Zalo Audience Sync
 */
export enum ZaloAudienceSyncJobName {
  /**
   * Job đồng bộ người dùng Zalo vào audience
   */
  SYNC_ZALO_USERS_TO_AUDIENCE = 'sync-zalo-users-to-audience',

  /**
   * Job đồng bộ tin nhắn Zalo vào database
   */
  SYNC_ZALO_MESSAGES = 'sync-zalo-messages',
}

/**
 * Enum định nghĩa các tên job trong queue Zalo Video Tracking
 */
export enum ZaloVideoTrackingJobName {
  /**
   * Job kiểm tra trạng thái video upload
   */
  CHECK_VIDEO_STATUS = 'check-video-status',
}

/**
 * Enum định nghĩa các tên job trong queue Zalo Article Scheduler
 */
export enum ZaloArticleSchedulerJobName {
  /**
   * Job xuất bản bài viết đã lên lịch
   */
  PUBLISH_SCHEDULED_ARTICLE = 'publish-scheduled-article',
}

/**
 * Enum định nghĩa các tên job trong queue Zalo Article Tracking
 */
export enum ZaloArticleTrackingJobName {
  /**
   * Job kiểm tra tiến trình tạo bài viết và lấy article ID
   */
  CHECK_ARTICLE_STATUS = 'check-article-status',
}

/**
 * Enum định nghĩa các tên job trong queue Zalo Upload
 */
export enum ZaloUploadJobName {
  /**
   * Job upload image
   */
  UPLOAD_IMAGE = 'upload-image',

  /**
   * Job upload file
   */
  UPLOAD_FILE = 'upload-file',

  /**
   * Job upload GIF
   */
  UPLOAD_GIF = 'upload-gif',
}

/**
 * Enum định nghĩa các tên job trong queue In-App AI
 */
export enum InAppAiJobName {
  /**
   * Job xử lý tin nhắn AI trong ứng dụng
   */
  TRIGGER = 'trigger',
}

/**
 * Các tùy chọn mặc định cho các job
 * Enum định nghĩa các tên job trong queue Webhook
 */
export enum WebhookJobName {
  /**
   * Job xử lý webhook event
   */
  PROCESS_WEBHOOK_EVENT = 'process-webhook-event',

  /**
   * Job trigger workflows từ webhook
   */
  TRIGGER_WORKFLOWS = 'trigger-workflows',

  /**
   * Job gửi external webhooks
   */
  SEND_EXTERNAL_WEBHOOK = 'send-external-webhook',

  /**
   * Job xử lý analytics
   */
  PROCESS_ANALYTICS = 'process-analytics',

  /**
   * Job retry failed webhooks
   */
  RETRY_FAILED_WEBHOOK = 'retry-failed-webhook',

  /**
   * Job trừ dung lượng user addon usage
   */
  DEDUCT_USER_ADDON_USAGE = 'deduct-user-addon-usage',

  /**
   * Job xử lý Zalo webhook event
   */
  PROCESS_ZALO_WEBHOOK = 'process-zalo-webhook',
}

/**
 * Enum định nghĩa các tên job trong queue External Webhook
 */
export enum ExternalWebhookJobName {
  /**
   * Job gửi webhook đến external URL
   */
  SEND_WEBHOOK = 'send-webhook',

  /**
   * Job retry failed external webhook
   */
  RETRY_WEBHOOK = 'retry-webhook',
}

/**
 * Enum định nghĩa các tên job trong queue Workflow Execution
 */
export enum WorkflowExecutionJobName {
  /**
   * Job thực thi workflow từ trigger
   */
  EXECUTE_WORKFLOW = 'execute-workflow',

  /**
   * Job thực thi node đơn lẻ trong workflow
   */
  EXECUTE_NODE = 'execute-node',

  /**
   * Job retry failed workflow execution
   */
  RETRY_WORKFLOW = 'retry-workflow',

  /**
   * Job cleanup workflow execution data
   */
  CLEANUP_EXECUTION = 'cleanup-execution',
}

// Node test job names enum removed - focusing on real execution only

/**
 * Các tùy chọn mặc định cho các job (BullMQ)
 */
export const DEFAULT_JOB_OPTIONS = {
  attempts: 3, // Số lần thử lại
  backoff: {
    type: 'exponential' as const, // Kiểu backoff khi retry (fixed, exponential)
    delay: 1000, // Thời gian delay giữa các lần retry (ms)
  },
  removeOnComplete: true, // Tự động xóa job sau khi hoàn thành
  removeOnFail: false, // Giữ lại job thất bại để kiểm tra
};

/**
 * Các tùy chọn cho job ưu tiên cao (BullMQ)
 */
export const HIGH_PRIORITY_JOB_OPTIONS = {
  ...DEFAULT_JOB_OPTIONS,
  priority: 10, // Ưu tiên cao hơn (BullMQ: số càng cao càng ưu tiên)
  attempts: 5, // Nhiều lần thử lại hơn
  backoff: {
    type: 'exponential' as const,
    delay: 500, // Delay ngắn hơn cho high priority
  },
};

/**
 * Enum định nghĩa các tên job trong queue Zalo Consultation Sequence
 */
export enum ZaloConsultationSequenceJobName {
  /**
   * Job gửi chuỗi tin nhắn tư vấn cho 1 user
   */
  SEND_CONSULTATION_SEQUENCE_USER = 'send-consultation-sequence-user',
}

/**
 * Enum định nghĩa các tên job trong queue Zalo Group Message Sequence
 */
export enum ZaloGroupMessageSequenceJobName {
  /**
   * Job gửi chuỗi tin nhắn group cho nhiều nhóm
   */
  SEND_GROUP_MESSAGE_SEQUENCE = 'send-group-message-sequence',
}

export enum GraphJobName {
  SUPERVISOR_WORKERS = 'supervisor-workers',
  PLANNER_EXECUTOR = 'planner-executor',
}
