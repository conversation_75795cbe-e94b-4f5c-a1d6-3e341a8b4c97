import { AppException } from '@/common';
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { QueueService } from '@shared/queue/queue.service';
import { WebsiteJobData } from '@shared/queue/queue.types';
import { RunStatusService } from '@shared/services/run-status.service';
import { RedisService } from '@shared/services/redis.service';
import { CancelReason } from '@shared/run-status';
import { Repository } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { z } from 'zod';
import { Response } from 'express';
import { Platform } from '../../../shared/enums';
import { User } from '../../user/entities/user.entity';
import { AgentConnection } from '../../agent/entities/agent-connection.entity';
import { Agent } from '../../agent/entities/agent.entity';
import { ExternalCustomerPlatformData } from '../../business/entities/external-customer-platform-data.entity';
import {
  AttachmentContentDto,
  MessageContentType,
  ExternalMessageRequestDto,
} from '../dto';
import { CHAT_ERROR_CODES } from '../exceptions';
import { ContentValidationService } from './content-validation.service';
import { PayloadLiveChatKey } from '@modules/integration/interfaces/website.interface';

// Import new services
import { ExternalMessageService } from './external-message.service';
import { WebsiteVisitorService } from './website-visitor.service';

/**
 * Website Chat Service (Refactored)
 *
 * Handles high-level chat orchestration for website visitors.
 * Delegates message operations to ExternalMessageService and visitor management to WebsiteVisitorService.
 * Focuses on agent resolution, job queue coordination, and SSE streaming.
 */
@Injectable()
export class WebsiteChatService {
  private readonly logger = new Logger(WebsiteChatService.name);

  constructor(
    private readonly contentValidation: ContentValidationService,
    private readonly runStatusService: RunStatusService,
    private readonly queueService: QueueService,
    private readonly redisService: RedisService,
    private readonly externalMessageService: ExternalMessageService,
    private readonly websiteVisitorService: WebsiteVisitorService,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(AgentConnection)
    private readonly agentConnectionRepository: Repository<AgentConnection>,
    @InjectRepository(Agent)
    private readonly agentRepository: Repository<Agent>,
    @InjectRepository(ExternalCustomerPlatformData)
    private readonly platformDataRepository: Repository<ExternalCustomerPlatformData>,
  ) {}

  /**
   * Process a website chat message according to the backend-worker coordination design
   *
   * This method implements the same sophisticated coordination strategy as in-app chat:
   * 1. Message persistence in database
   * 2. Debouncing with delayed job scheduling
   * 3. Interruption handling for double-texting
   * 4. Lazy run creation to prevent database waste
   *
   * @param messageRequest - The message request data
   * @param websitePayload - Decrypted website key payload
   * @param threadId - ID of the external platform data (thread equivalent for website)
   * @returns Promise<MessageResponseDto> - Response with run information
   */
  async processMessage(param: {
    messageRequest: ExternalMessageRequestDto;
    websitePayload: PayloadLiveChatKey;
    threadId: string;
  }): Promise<{ messageId: string; runId: string }> {
    const { messageRequest, websitePayload, threadId } = param;

    const user = await this.userRepository.findOne({
      where: { id: websitePayload.userId },
    });

    if (!user) {
      throw new AppException(
        CHAT_ERROR_CODES.USER_NOT_FOUND,
        'Website user not found',
      );
    }

    const hasEnoughPoint = user.pointsBalance >= 1; // Assuming 1 point is required for processing

    if (!hasEnoughPoint) {
      throw new AppException(
        CHAT_ERROR_CODES.NOT_ENOUGH_POINTS,
        'Not enough points to process message',
      );
    }

    this.logger.log('Processing website chat message', {
      threadId,
      websiteId: websitePayload.websiteId,
      websiteOwnerId: websitePayload.userId,
    });

    this.logger.debug('Starting Phase 1: Validation');

    await this.validateThreadAccess(threadId, websitePayload);

    await this.validateMessageContent(messageRequest, websitePayload);

    this.logger.debug('Phase 1 validation completed successfully');

    this.logger.debug('Starting Phase 2: Database Persistence');
    const { messageId, runId, isModification, deletedMessageIds } =
      await this.persistMessage(messageRequest, threadId, websitePayload);

    this.logger.debug('Phase 2 completed', {
      messageId,
      runId,
      isModification,
      deletedMessageIds,
    });

    this.logger.debug('Starting Phase 3: Worker Triggering');
    const jobData = await this.triggerWorkerProcessing({
      runId,
      threadId,
      websitePayload,
    });

    this.logger.debug('Phase 3 completed', {
      threadId,
      messageId,
      runId,
    });

    this.logger.log('Website chat processing completed', {
      messageId,
      runId,
      isModification,
    });

    return { messageId, runId };
  }

  /**
   * Stream chat events via Server-Sent Events for website chat
   * Similar to user chat but adapted for website visitors
   */
  async streamChatEvents(
    res: Response,
    threadId: string,
    runId: string,
    websitePayload: PayloadLiveChatKey,
    fromMessageId?: string,
  ): Promise<void> {
    try {
      // Validate thread access for website
      await this.validateThreadAccess(threadId, websitePayload);

      this.logger.log(
        `🔥 Starting website chat SSE stream for thread ${threadId}, run ${runId}`,
        {
          websiteId: websitePayload.websiteId,
          websiteOwnerId: websitePayload.userId,
        },
      );

      // Set SSE headers
      res.set({
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache, no-transform',
        Connection: 'keep-alive',
        'X-Accel-Buffering': 'no',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control',
      });

      res.flushHeaders();

      const platformThreadId = `${Platform.WEBSITE}:${threadId}`;
      const streamKey = `${Platform.WEBSITE}:agent_stream:${threadId}:${runId}`;

      // Send initial connection event
      res.write('event: connected\n');
      res.write(
        `data: {"type":"connected","threadId":"${threadId}","runId":"${runId}"}\n\n`,
      );

      this.logger.debug('Website SSE connection established', {
        threadId,
        runId,
        platformThreadId,
        streamKey,
        websiteId: websitePayload.websiteId,
      });

      // Stream messages from Redis
      await this.consumeStreamMessages(res, streamKey, threadId, fromMessageId);

      this.logger.debug('Website SSE stream completed', {
        threadId,
        runId,
        websiteId: websitePayload.websiteId,
      });
    } catch (error) {
      this.logger.error('Website SSE stream error', {
        threadId,
        runId,
        websiteId: websitePayload.websiteId,
        error: error.message,
        stack: error.stack,
      });

      throw error;
    } finally {
      // GUARANTEED CLEANUP: This block ensures the response stream is always closed.
      if (!res.writableEnded) {
        res.end();
      }
      this.logger.log(
        `🔚 Website SSE stream session ended for thread ${threadId}, run ${runId}`,
      );
    }
  }

  /**
   * Consume messages from Redis stream and send via SSE for website chat
   * Similar to ChatServiceNew but adapted for website context
   */
  private async consumeStreamMessages(
    res: Response,
    streamKey: string,
    threadId: string,
    fromMessageId?: string,
  ): Promise<void> {
    const client = this.redisService.getDuplicateClient();
    let lastId = fromMessageId || '0-0';

    // This flag is used to gracefully exit the consumption loops.
    let streamShouldEnd = false;

    const sendMessage = (id: string, fields: string[]): void => {
      const payload = this.parseStreamFields(fields);

      res.write(`id: ${id}\n`);
      res.write(`event: ${payload.type}\n`);
      res.write(`data: ${JSON.stringify(payload)}\n\n`);

      this.logger.debug(
        `-> Sent website chat event ${payload.type} for thread ${threadId}`,
      );

      if (
        ['run_complete', 'run_error', 'run_cancelled'].includes(payload.type)
      ) {
        this.logger.log(
          `🏁 Website stream termination event received: ${payload.type}`,
        );
        streamShouldEnd = true;
      }
    };

    try {
      this.logger.log(
        `Starting website consumption for ${threadId}. Initial position: ${lastId}`,
      );

      // 1. CATCH-UP READ: Read any messages missed before connecting.
      const catchUpResponse = await client.xread('STREAMS', streamKey, lastId);
      if (catchUpResponse) {
        const [[, messages]] = catchUpResponse;
        this.logger.log(
          `Replaying ${messages.length} unconsumed messages for website thread ${threadId}.`,
        );
        for (const [id, fields] of messages) {
          sendMessage(id, fields);
          lastId = id;
          if (streamShouldEnd) break; // Exit if a terminal event is found
        }
      }

      res.on('close', () => {
        this.logger.warn(
          `📴 Website client disconnected from SSE for thread ${threadId}`,
        );
        streamShouldEnd = true;
      });

      // 2. LIVE READ: Block and wait for new messages.
      while (!res.writableEnded && !streamShouldEnd) {
        try {
          const liveResponse = await client.xread(
            'BLOCK',
            5000, // 5-second timeout
            'STREAMS',
            streamKey,
            lastId,
          );

          if (liveResponse) {
            const [[, messages]] = liveResponse;
            for (const [id, fields] of messages) {
              sendMessage(id, fields);
              lastId = id;
              if (streamShouldEnd) break; // Exit inner loop
            }
          }
        } catch (error) {
          this.logger.error(
            `💥 Error during live stream read for website thread ${threadId}:`,
            error.message,
          );
          // On read error, break the loop and allow finally to clean up.
          break;
        }
      }
    } catch (error) {
      // This catches errors from the initial catch-up read or other unexpected issues.
      this.logger.error(
        `💥 Fatal error in website stream consumption for ${threadId}:`,
        error.message,
      );
      // Propagate the error to the calling method to be handled there.
      throw error;
    } finally {
      // GUARANTEED CLEANUP: This block ensures the Redis client is always closed.
      try {
        await client.quit();
        this.logger.debug(
          `✅ Redis client cleaned up for website thread ${threadId}`,
        );
      } catch (cleanupError) {
        this.logger.error(
          `⚠️ Error cleaning up Redis client for website thread ${threadId}:`,
          cleanupError.message,
        );
      }
      this.logger.log(
        `🛑 Website SSE consumption loop ended for thread ${threadId}`,
      );
    }
  }

  /**
   * Parse Redis stream fields to extract worker events
   * Worker stores events as: ['event', '{"type":"text_message_start","timestamp":...}']
   */
  private parseStreamFields(fields: string[]): Record<string, any> {
    for (let i = 0; i < fields.length; i += 2) {
      const key = fields[i];
      const value = fields[i + 1];

      if (key === 'event') {
        try {
          return JSON.parse(value);
        } catch (error) {
          this.logger.error('Failed to parse website stream event JSON:', {
            value,
            error: error.message,
          });
          return { type: 'parse_error', error: 'Invalid JSON in stream' };
        }
      }
    }

    this.logger.warn('No event field found in website stream fields:', fields);
    return { type: 'unknown', fields };
  }

  /**
   * Create or update message in database (delegated to ExternalMessageService)
   * Part of Phase 2: Database Persistence
   */
  @Transactional()
  private async persistMessage(
    messageRequest: ExternalMessageRequestDto,
    threadId: string,
    websitePayload: PayloadLiveChatKey,
  ): Promise<{
    messageId: string;
    runId: string;
    isModification: boolean;
    deletedMessageIds: string[];
  }> {
    this.logger.debug('Starting Phase 2: Database Persistence (delegated)', {
      threadId,
    });

    // Delegate to ExternalMessageService
    const result = await this.externalMessageService.persistMessage(
      messageRequest,
      threadId,
      websitePayload,
    );

    // Ensure messageId is defined (should always be for successful external message creation)
    if (!result.messageId) {
      throw new AppException(
        CHAT_ERROR_CODES.MESSAGE_NOT_FOUND,
        'Failed to create external message - no messageId returned',
      );
    }

    this.logger.debug('Phase 2: Database Persistence completed', {
      messageId: result.messageId,
      runId: result.runId,
      isModification: result.isModification,
      deletedMessageIds: result.deletedMessageIds,
      threadId,
    });

    return {
      messageId: result.messageId,
      runId: result.runId,
      isModification: result.isModification,
      deletedMessageIds: result.deletedMessageIds,
    };
  }

  /**
   * Handle stream interruption via Redis pub/sub
   * Part of Phase 3: Worker Triggering
   *
   * Sends MESSAGE_INTERRUPT cancellation when user sends new message during processing
   */
  private async handleStreamInterruption(
    threadId: string,
    newRunId: string,
  ): Promise<void> {
    const platformThreadId = `website:${threadId}`;
    const currentStatus =
      await this.runStatusService.getRunStatus(platformThreadId);

    this.logger.debug('Checking for active run during interruption', {
      threadId,
      newRunId,
      currentStatus: currentStatus?.status,
      currentRunId: currentStatus?.metadata.runId,
      hasActiveRun: currentStatus?.status === 'active',
    });

    if (currentStatus && currentStatus.status === 'active') {
      // Cancel the current run with MESSAGE_INTERRUPT reason
      const cancelSuccess = await this.runStatusService.cancelRun(
        platformThreadId,
        CancelReason.MESSAGE_INTERRUPT,
        currentStatus.metadata.runId,
      );

      if (cancelSuccess) {
        this.logger.debug('Sent message interrupt cancellation', {
          threadId,
          cancelledRunId: currentStatus.metadata.runId,
          newRunId,
          reason: CancelReason.MESSAGE_INTERRUPT,
        });
      } else {
        this.logger.error('Failed to cancel active run during interruption', {
          threadId,
          currentRunId: currentStatus.metadata.runId,
          newRunId,
        });
      }
    } else {
      this.logger.debug('No active run found, skipping interruption', {
        threadId,
        newRunId,
        currentStatus: currentStatus?.status || 'none',
      });
    }
  }

  /**
   * Validate thread access and ownership
   */
  private async validateThreadAccess(
    threadId: string,
    websitePayload: PayloadLiveChatKey,
  ): Promise<ExternalCustomerPlatformData> {
    this.logger.debug('Validating website thread access', {
      threadId,
      websiteId: websitePayload.websiteId,
      ownerId: websitePayload.userId,
    });

    // Find the external platform data (equivalent to thread for website)
    const platformData = await this.platformDataRepository.findOne({
      where: {
        id: threadId,
        platform: Platform.WEBSITE,
        // Validate that the thread belongs to the website owner
        userId: websitePayload.userId,
      },
      relations: ['userConvertCustomer'],
    });

    if (!platformData) {
      throw new AppException(
        CHAT_ERROR_CODES.THREAD_NOT_FOUND,
        'Website thread not found or access denied',
      );
    }

    this.logger.debug('Website thread access validation successful', {
      threadId,
      platformDataId: platformData.id,
      platform: platformData.platform,
    });

    return platformData;
  }

  /**
   * Validate message content blocks
   */
  private async validateMessageContent(
    messageRequest: ExternalMessageRequestDto,
    websitePayload: PayloadLiveChatKey,
  ): Promise<void> {
    this.logger.debug('Validating website message content');

    if (messageRequest.contentBlocks.type === MessageContentType.TEXT) {
      return;
    }

    if (messageRequest.contentBlocks.type === MessageContentType.ATTACHMENT) {
      await this.contentValidation.validateFileExistenceInContentBlocks(
        messageRequest.contentBlocks.attachments as AttachmentContentDto[],
        { userId: websitePayload.userId }, // Use website owner's ID for file validation
      );
      return;
    }

    // Tool call decisions not supported for website chat
    if (
      messageRequest.contentBlocks.type ===
      MessageContentType.TOOL_CALL_DECISION
    ) {
      throw new AppException(
        CHAT_ERROR_CODES.UNSUPPORTED_CONTENT_TYPE,
        'Tool call decisions not supported for website chat',
      );
    }

    this.logger.debug(
      'Website message content validation completed successfully',
    );
  }

  /**
   * Trigger worker processing via BullMQ
   * Part of Phase 3: Worker Triggering
   */
  private async triggerWorkerProcessing(param: {
    runId: string;
    threadId: string;
    websitePayload: PayloadLiveChatKey;
  }): Promise<WebsiteJobData> {
    const { runId, threadId, websitePayload } = param;

    const delay = 2500; // Fixed delay for website chat (no tool call logic)
    const platformThreadId = `${Platform.WEBSITE}:${threadId}`;
    const keys = {
      platformThreadId,
      runStatusKey: `run_status:${Platform.WEBSITE}:${threadId}`,
      streamKey: `${Platform.WEBSITE}:agent_stream:${threadId}:${runId}`,
    };

    // 🔒 VALIDATE KEYS BEFORE USING THEM
    this.validateRedisKeys(keys, { threadId, runId });

    // Get platform data to build visitor context
    const platformData = await this.platformDataRepository.findOne({
      where: { id: threadId },
      relations: ['userConvertCustomer'],
    });

    if (!platformData) {
      throw new AppException(
        CHAT_ERROR_CODES.THREAD_NOT_FOUND,
        'Platform data not found',
      );
    }

    // Resolve website agents using strategy pattern
    const { mainAgentId, plannerAgentId } = await this.resolveWebsiteAgents(
      websitePayload.websiteId,
    );

    // Build comprehensive website job data (delegated to WebsiteVisitorService)
    const websiteOwner = await this.websiteVisitorService.buildWebsiteOwnerInfo(
      websitePayload.userId,
    );
    const websiteVisitor =
      await this.websiteVisitorService.buildWebsiteVisitorInfo(threadId);
    const websiteInfo =
      await this.websiteVisitorService.getWebsiteInfo(threadId);

    const jobData: WebsiteJobData = {
      runId: runId,
      threadId,
      mainAgentId,
      plannerAgentId,
      platform: Platform.WEBSITE,
      keys,
      humanInfo: {
        websiteOwner,
        websiteVisitor,
        websiteInfo,
      },
    };

    // Check if worker is already actively processing this conversation
    const isRunActive =
      await this.runStatusService.isRunActive(platformThreadId);

    if (isRunActive) {
      this.logger.debug('Worker already processing, sending interruption', {
        threadId,
        platformThreadId,
        currentRunStatus:
          await this.runStatusService.getRunStatus(platformThreadId),
        newRunId: runId,
      });
      await this.handleStreamInterruption(threadId, runId);
    }

    await this.queueService.addWebsiteAiJob(jobData, {
      delay,
      jobId: platformThreadId,
      attempts: 1,
      removeOnComplete: true,
      removeOnFail: true,
    });

    this.logger.debug('Triggered website worker processing', {
      runId,
      threadId,
      platformThreadId,
      websiteId: websitePayload.websiteId,
      ownerId: websitePayload.userId,
    });

    return jobData;
  }

  /**
   * Resolve website agent IDs using strategy pattern
   * Finds main agent via AgentConnection and planner agent via strategyId
   */
  private async resolveWebsiteAgents(websiteId: string): Promise<{
    mainAgentId: string;
    plannerAgentId?: string;
  }> {
    this.logger.debug('Resolving website agents', { websiteId });

    // 1. Find agent connected to website via integration_id
    const connection = await this.agentConnectionRepository.findOne({
      where: { integrationId: websiteId },
    });

    if (!connection) {
      throw new AppException(
        CHAT_ERROR_CODES.THREAD_VALIDATION_FAILED,
        `No agent connected to website: ${websiteId}`,
      );
    }

    // 2. Fetch agent with strategy info
    const agent = await this.agentRepository.findOne({
      where: { id: connection.agentId, active: true },
    });

    if (!agent) {
      throw new AppException(
        CHAT_ERROR_CODES.THREAD_VALIDATION_FAILED,
        `Connected agent not found or inactive: ${connection.agentId}`,
      );
    }

    // 3. Validate strategy agent if exists
    if (agent.strategyId) {
      const strategyAgent = await this.agentRepository.findOne({
        where: { id: agent.strategyId, active: true },
      });

      if (!strategyAgent) {
        this.logger.warn(
          'Strategy agent not found or inactive, proceeding without planner',
          {
            websiteId,
            mainAgentId: agent.id,
            strategyId: agent.strategyId,
          },
        );
      }
    }

    const result = {
      mainAgentId: agent.id,
      plannerAgentId: agent.strategyId || undefined,
    };

    this.logger.debug('Website agents resolved', {
      websiteId,
      mainAgentId: result.mainAgentId,
      plannerAgentId: result.plannerAgentId,
      hasStrategy: !!agent.strategyId,
    });

    return result;
  }

  /**
   * Validate Redis keys format and content using Zod schema
   * Ensures all keys follow expected patterns before job creation
   * 🔒 SECURITY: Prevents malformed keys from reaching workers
   */
  private validateRedisKeys(
    keys: WebsiteJobData['keys'],
    context: { threadId: string; runId: string },
  ): void {
    const { threadId, runId } = context;

    const RedisKeysSchema = z
      .object({
        platformThreadId: z.literal(`website:${threadId}`),
        runStatusKey: z.literal(`run_status:website:${threadId}`),
        streamKey: z.literal(`website:agent_stream:${threadId}:${runId}`),
      })
      .refine((keys) => Object.values(keys).every((key) => key.length <= 200), {
        message: 'All keys must be 200 characters or less',
      });

    try {
      RedisKeysSchema.parse(keys);
      this.logger.debug('Website Redis keys validation passed', {
        threadId,
        runId,
        keys,
      });
    } catch (error) {
      this.logger.error('Website Redis keys validation failed', {
        threadId,
        runId,
        keys,
        error: error.message,
      });
      throw new AppException(
        CHAT_ERROR_CODES.THREAD_VALIDATION_FAILED,
        `Invalid Redis key format: ${error.message}`,
      );
    }
  }
}
