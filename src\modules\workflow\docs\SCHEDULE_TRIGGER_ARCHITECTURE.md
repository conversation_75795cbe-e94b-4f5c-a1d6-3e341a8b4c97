# Schedule Trigger Architecture - No Database Approach

## 🎯 Concept Overview

Schedule node hoạt động như một **trigger tự động** tương tự webhook:
- Khi workflow **activate** → Schedule node đăng ký với in-memory scheduler
- Khi **time matches** → Tự động đẩy job vào queue execute workflow
- **No database tables** → Sử dụng workflow/node data có sẵn

## 🏗️ Architecture Design

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Workflow      │───▶│  In-Memory      │───▶│   Job Queue     │
│  (Schedule Node)│    │   Scheduler     │    │  (Bull/Redis)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       ▼                       ▼
         │              ┌─────────────────┐    ┌─────────────────┐
         │              │   Node-Cron     │    │ Workflow Engine │
         │              │   (Time Engine) │    │  (Execution)    │
         │              └─────────────────┘    └─────────────────┘
         │
         ▼
┌─────────────────┐
│   Workflow DB   │ ← Existing tables only
│ (nodes/workflows)│
└─────────────────┘
```

## 🔧 Implementation Strategy

### 1. In-Memory Scheduler Service

```typescript
@Injectable()
export class InMemorySchedulerService {
    private scheduledTasks = new Map<string, {
        cronTask: ScheduledTask;
        workflowId: string;
        nodeId: string;
        config: IScheduleParameters;
    }>();
    
    constructor(
        private readonly queueService: WorkflowQueueService,
        private readonly workflowService: WorkflowService
    ) {}
    
    /**
     * Register schedule khi workflow activate
     */
    async registerScheduleNode(
        workflowId: string, 
        nodeId: string, 
        scheduleConfig: IScheduleParameters
    ): Promise<void> {
        const scheduleKey = `${workflowId}:${nodeId}`;
        
        // Build cron expression từ config
        const cronExpression = this.buildCronExpression(scheduleConfig);
        
        // Create cron task
        const cronTask = cron.schedule(cronExpression, async () => {
            await this.triggerWorkflowExecution(workflowId, nodeId, scheduleConfig);
        }, {
            scheduled: false,
            timezone: this.getTimezone(scheduleConfig)
        });
        
        // Store in memory
        this.scheduledTasks.set(scheduleKey, {
            cronTask,
            workflowId,
            nodeId,
            config: scheduleConfig
        });
        
        // Start if active
        if (scheduleConfig.is_active) {
            cronTask.start();
        }
        
        this.logger.log(`Schedule registered: ${scheduleKey}`);
    }
    
    /**
     * Unregister khi workflow deactivate
     */
    async unregisterScheduleNode(workflowId: string, nodeId: string): Promise<void> {
        const scheduleKey = `${workflowId}:${nodeId}`;
        const task = this.scheduledTasks.get(scheduleKey);
        
        if (task) {
            task.cronTask.stop();
            task.cronTask.destroy();
            this.scheduledTasks.delete(scheduleKey);
            this.logger.log(`Schedule unregistered: ${scheduleKey}`);
        }
    }
    
    /**
     * Trigger workflow execution
     */
    private async triggerWorkflowExecution(
        workflowId: string, 
        nodeId: string, 
        config: IScheduleParameters
    ): Promise<void> {
        try {
            // Check overlap behavior
            if (!await this.shouldExecute(workflowId, config.overlap_behavior)) {
                this.logger.log(`Skipping execution due to overlap: ${workflowId}`);
                return;
            }
            
            // Add job to queue
            await this.queueService.addScheduledWorkflowJob({
                workflowId,
                triggerNodeId: nodeId,
                executionBehavior: config.execution_behavior,
                targetNodeId: config.target_node_id,
                eventName: config.event_name,
                inputData: config.input_data || {},
                triggeredBy: 'schedule',
                timestamp: Date.now()
            });
            
            this.logger.log(`Schedule triggered workflow: ${workflowId}`);
            
        } catch (error) {
            this.logger.error(`Schedule execution failed: ${workflowId}`, error);
        }
    }
    
    private buildCronExpression(config: IScheduleParameters): string {
        switch (config.schedule_type) {
            case EScheduleType.ONCE:
                // Convert to cron for specific datetime
                const date = new Date(config.once_config.datetime);
                return `${date.getSeconds()} ${date.getMinutes()} ${date.getHours()} ${date.getDate()} ${date.getMonth() + 1} *`;
                
            case EScheduleType.DAILY:
                return `0 ${config.daily_config.minute} ${config.daily_config.hour} * * *`;
                
            case EScheduleType.WEEKLY:
                const days = config.weekly_config.days_of_week.join(',');
                return `0 ${config.weekly_config.minute} ${config.weekly_config.hour} * * ${days}`;
                
            case EScheduleType.MONTHLY:
                const day = config.monthly_config.day_of_month === 'last' ? 'L' : config.monthly_config.day_of_month;
                return `0 ${config.monthly_config.minute} ${config.monthly_config.hour} ${day} * *`;
                
            case EScheduleType.INTERVAL:
                return this.buildIntervalCron(config.interval_config);
                
            case EScheduleType.CRON:
                return config.cron_config.expression;
                
            default:
                throw new Error(`Unsupported schedule type: ${config.schedule_type}`);
        }
    }
    
    private buildIntervalCron(intervalConfig: IIntervalConfig): string {
        const { value, type } = intervalConfig;
        
        switch (type) {
            case EIntervalType.SECONDS:
                return `*/${value} * * * * *`;
            case EIntervalType.MINUTES:
                return `0 */${value} * * * *`;
            case EIntervalType.HOURS:
                return `0 0 */${value} * * *`;
            case EIntervalType.DAYS:
                return `0 0 0 */${value} * *`;
            default:
                throw new Error(`Unsupported interval type: ${type}`);
        }
    }
    
    private getTimezone(config: IScheduleParameters): string {
        // Priority: config timezone > workflow timezone > instance timezone
        return config.once_config?.timezone || 
               config.daily_config?.timezone || 
               config.weekly_config?.timezone || 
               config.monthly_config?.timezone || 
               'Asia/Ho_Chi_Minh';
    }
    
    private async shouldExecute(workflowId: string, overlapBehavior: EOverlapBehavior): Promise<boolean> {
        const runningExecutions = await this.queueService.getRunningWorkflowJobs(workflowId);
        
        switch (overlapBehavior) {
            case EOverlapBehavior.SKIP:
                return runningExecutions.length === 0;
            case EOverlapBehavior.ALLOW:
                return true;
            case EOverlapBehavior.REPLACE:
                await this.queueService.cancelRunningWorkflowJobs(workflowId);
                return true;
            case EOverlapBehavior.QUEUE:
                return true;
        }
    }
}
```

### 2. Workflow Lifecycle Integration

```typescript
@Injectable()
export class WorkflowLifecycleService {
    constructor(
        private readonly schedulerService: InMemorySchedulerService,
        private readonly nodeService: NodeService
    ) {}
    
    /**
     * Khi workflow được activate
     */
    async activateWorkflow(workflowId: string): Promise<void> {
        // Find all schedule nodes in workflow
        const scheduleNodes = await this.nodeService.findScheduleNodes(workflowId);
        
        for (const node of scheduleNodes) {
            const scheduleConfig = node.parameters as IScheduleParameters;
            
            await this.schedulerService.registerScheduleNode(
                workflowId,
                node.id,
                scheduleConfig
            );
        }
        
        this.logger.log(`Activated ${scheduleNodes.length} schedule nodes for workflow: ${workflowId}`);
    }
    
    /**
     * Khi workflow được deactivate
     */
    async deactivateWorkflow(workflowId: string): Promise<void> {
        const scheduleNodes = await this.nodeService.findScheduleNodes(workflowId);
        
        for (const node of scheduleNodes) {
            await this.schedulerService.unregisterScheduleNode(workflowId, node.id);
        }
        
        this.logger.log(`Deactivated ${scheduleNodes.length} schedule nodes for workflow: ${workflowId}`);
    }
    
    /**
     * Khi node được update
     */
    async updateScheduleNode(workflowId: string, nodeId: string, newConfig: IScheduleParameters): Promise<void> {
        // Unregister old schedule
        await this.schedulerService.unregisterScheduleNode(workflowId, nodeId);
        
        // Register new schedule
        await this.schedulerService.registerScheduleNode(workflowId, nodeId, newConfig);
        
        this.logger.log(`Updated schedule node: ${workflowId}:${nodeId}`);
    }
}
```

### 3. Queue Service Integration

```typescript
@Injectable()
export class WorkflowQueueService {
    constructor(
        @InjectQueue('workflow-execution') 
        private workflowQueue: Queue
    ) {}
    
    async addScheduledWorkflowJob(jobData: ScheduledWorkflowJobData): Promise<void> {
        const job = await this.workflowQueue.add(
            'execute-scheduled-workflow',
            jobData,
            {
                attempts: 3,
                backoff: {
                    type: 'exponential',
                    delay: 60000
                },
                removeOnComplete: 100,
                removeOnFail: 50
            }
        );
        
        this.logger.log(`Scheduled workflow job added: ${job.id}`);
    }
    
    async getRunningWorkflowJobs(workflowId: string): Promise<Job[]> {
        const activeJobs = await this.workflowQueue.getActive();
        return activeJobs.filter(job => job.data.workflowId === workflowId);
    }
    
    async cancelRunningWorkflowJobs(workflowId: string): Promise<void> {
        const runningJobs = await this.getRunningWorkflowJobs(workflowId);
        
        for (const job of runningJobs) {
            await job.remove();
        }
    }
}

interface ScheduledWorkflowJobData {
    workflowId: string;
    triggerNodeId: string;
    executionBehavior: EExecutionBehavior;
    targetNodeId?: string;
    eventName?: string;
    inputData: Record<string, any>;
    triggeredBy: 'schedule';
    timestamp: number;
}
```

### 4. Application Bootstrap

```typescript
@Injectable()
export class ScheduleBootstrapService implements OnApplicationBootstrap {
    constructor(
        private readonly workflowService: WorkflowService,
        private readonly lifecycleService: WorkflowLifecycleService
    ) {}
    
    async onApplicationBootstrap() {
        // Load all active workflows with schedule nodes
        const activeWorkflows = await this.workflowService.findActiveWorkflowsWithScheduleNodes();
        
        for (const workflow of activeWorkflows) {
            await this.lifecycleService.activateWorkflow(workflow.id);
        }
        
        this.logger.log(`Bootstrapped ${activeWorkflows.length} workflows with schedule nodes`);
    }
}
```

## 🎯 Key Benefits

### 1. **No Database Overhead**
- Không cần bảng schedules riêng
- Sử dụng workflow/node data có sẵn
- Giảm complexity database schema

### 2. **Memory Efficient**
- In-memory scheduler với node-cron
- Chỉ store active schedules
- Auto cleanup khi workflow deactivate

### 3. **Simple Integration**
- Tích hợp với workflow lifecycle
- Sử dụng existing queue system
- Consistent với webhook pattern

### 4. **Scalable Design**
- Distributed queue với Redis
- Horizontal scaling support
- Fault tolerance với retry logic

## 🔄 Execution Flow

1. **Workflow Activate** → Register schedule nodes với in-memory scheduler
2. **Time Match** → Cron trigger executes callback
3. **Queue Job** → Add workflow execution job to Bull queue
4. **Execute** → Queue worker picks up và executes workflow
5. **Workflow Deactivate** → Unregister và cleanup schedules

## 🛡️ Error Handling

- **Cron failures**: Automatic retry với exponential backoff
- **Queue failures**: Bull built-in retry mechanism
- **Memory cleanup**: Proper cleanup on workflow deactivate
- **Bootstrap recovery**: Reload schedules on app restart

Approach này đơn giản, hiệu quả và không cần thêm database tables!
