import { Injectable, Logger } from '@nestjs/common';
import { ZaloPersonalIntegrationService } from './zalo-personal-integration.service';
import { AppException, ErrorCode } from '@/common/exceptions';

/**
 * Interface cho webhook data từ automation-web
 */
export interface ZaloPersonalLoginWebhookData {
  userId: number;
  zaloUserId: string;
  displayName: string;
  avatarUrl?: string;
  integrationId: string;
  sessionCookies?: string;
  browserFingerprint?: string;
}

/**
 * Service xử lý webhook từ automation-web cho Zalo Personal
 */
@Injectable()
export class ZaloPersonalWebhookService {
  private readonly logger = new Logger(ZaloPersonalWebhookService.name);

  constructor(
    private readonly zaloPersonalIntegrationService: ZaloPersonalIntegrationService,
  ) {}

  /**
   * Xử lý webhook khi đăng nhập thành công từ automation-web
   */
  async handleLoginSuccessWebhook(webhookData: ZaloPersonalLoginWebhookData) {
    try {
      this.logger.log(`Processing login success webhook for user ${webhookData.userId}`);

      // Validate webhook data
      this.validateWebhookData(webhookData);

      // Tạo hoặc cập nhật Zalo Personal Integration
      const result = await this.zaloPersonalIntegrationService.createFromAutomationWebLogin({
        userId: webhookData.userId,
        zaloUserId: webhookData.zaloUserId,
        displayName: webhookData.displayName,
        avatarUrl: webhookData.avatarUrl,
        integrationId: webhookData.integrationId,
        sessionCookies: webhookData.sessionCookies,
        browserFingerprint: webhookData.browserFingerprint,
      });

      this.logger.log(`Successfully processed webhook for user ${webhookData.userId}, integration ID: ${result.id}`);

      return result;
    } catch (error) {
      this.logger.error(`Failed to process login success webhook: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Validate webhook data
   */
  private validateWebhookData(webhookData: ZaloPersonalLoginWebhookData) {
    if (!webhookData.userId) {
      throw new AppException(ErrorCode.VALIDATION_ERROR, 'userId is required');
    }

    if (!webhookData.zaloUserId) {
      throw new AppException(ErrorCode.VALIDATION_ERROR, 'zaloUserId is required');
    }

    if (!webhookData.displayName) {
      throw new AppException(ErrorCode.VALIDATION_ERROR, 'displayName is required');
    }

    if (!webhookData.integrationId) {
      throw new AppException(ErrorCode.VALIDATION_ERROR, 'integrationId is required');
    }
  }

  /**
   * Xử lý webhook khi có lỗi đăng nhập
   */
  async handleLoginErrorWebhook(webhookData: {
    userId: number;
    integrationId: string;
    error: string;
    errorMessage: string;
  }) {
    try {
      this.logger.warn(`Processing login error webhook for user ${webhookData.userId}: ${webhookData.error}`);

      // TODO: Có thể cập nhật trạng thái integration thành error
      // hoặc gửi notification cho user

      return {
        success: true,
        message: 'Login error webhook processed',
      };
    } catch (error) {
      this.logger.error(`Failed to process login error webhook: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xử lý webhook khi session hết hạn
   */
  async handleSessionExpiredWebhook(webhookData: {
    userId: number;
    integrationId: string;
    sessionId: string;
  }) {
    try {
      this.logger.warn(`Processing session expired webhook for user ${webhookData.userId}`);

      // TODO: Có thể cập nhật trạng thái integration thành expired
      // hoặc gửi notification cho user để đăng nhập lại

      return {
        success: true,
        message: 'Session expired webhook processed',
      };
    } catch (error) {
      this.logger.error(`Failed to process session expired webhook: ${error.message}`, error.stack);
      throw error;
    }
  }
}
