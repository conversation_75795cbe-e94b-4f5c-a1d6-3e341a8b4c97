/**
 * @file Facebook Page Validation Functions
 * 
 * <PERSON><PERSON><PERSON> nghĩa validation functions cho Facebook Page integration
 * Theo patterns từ Make.com chuẩn
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

import { EFacebookPageOperation, EFacebookPhotoAction } from './facebook-page.types';
import { IFacebookPageParameters, IListPostsParameters, IGetPostParameters, IGetPostReactionsParameters, ICreatePostParameters, ICreatePostWithPhotosParameters, IUpdatePostParameters, IDeletePostParameters, ILikePostParameters, IUnlikePostParameters, IListVideosParameters, IGetVideoParameters, IUploadVideoParameters, IUpdateVideoParameters, IDeleteVideoParameters, IListPhotosParameters, IGetPhotoParameters, IUploadPhotoParameters, IDeletePhotoParameters, IListCommentsParameters, IGetCommentParameters, ICreateCommentParameters, IUpdateCommentParameters, IDeleteCommentParameters, IGetPageParameters } from './facebook-page.interface';

// =================================================================
// FACEBOOK PAGE VALIDATION FUNCTIONS
// =================================================================

/**
 * Validate Facebook Page parameters (detailed validation)
 */
export function validateFacebookPageParametersDetailed(
    params: Partial<IFacebookPageParameters>
): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check required integration_id
    if (!params.integration_id) {
        errors.push('Integration ID is required');
    }

    // Check required operation
    if (!params.operation) {
        errors.push('Operation is required');
    }

    // Operation specific validation
    switch (params.operation) {
        case EFacebookPageOperation.LIST_POSTS:
            validateListPostsParameters(params as IListPostsParameters, errors);
            break;

        case EFacebookPageOperation.GET_POST:
            validateGetPostParameters(params as IGetPostParameters, errors);
            break;

        case EFacebookPageOperation.GET_POST_REACTIONS:
            validateGetPostReactionsParameters(params as IGetPostReactionsParameters, errors);
            break;

        case EFacebookPageOperation.CREATE_POST:
            validateCreatePostParameters(params as ICreatePostParameters, errors);
            break;

        case EFacebookPageOperation.CREATE_POST_WITH_PHOTOS:
            validateCreatePostWithPhotosParameters(params as ICreatePostWithPhotosParameters, errors);
            break;

        case EFacebookPageOperation.UPDATE_POST:
            validateUpdatePostParameters(params as IUpdatePostParameters, errors);
            break;

        case EFacebookPageOperation.DELETE_POST:
            validateDeletePostParameters(params as IDeletePostParameters, errors);
            break;

        case EFacebookPageOperation.LIKE_POST:
            validateLikePostParameters(params as ILikePostParameters, errors);
            break;

        case EFacebookPageOperation.UNLIKE_POST:
            validateUnlikePostParameters(params as IUnlikePostParameters, errors);
            break;

        case EFacebookPageOperation.LIST_VIDEOS:
            validateListVideosParameters(params as IListVideosParameters, errors);
            break;

        case EFacebookPageOperation.GET_VIDEO:
            validateGetVideoParameters(params as IGetVideoParameters, errors);
            break;

        case EFacebookPageOperation.UPLOAD_VIDEO:
            validateUploadVideoParameters(params as IUploadVideoParameters, errors);
            break;

        case EFacebookPageOperation.UPDATE_VIDEO:
            validateUpdateVideoParameters(params as IUpdateVideoParameters, errors);
            break;

        case EFacebookPageOperation.DELETE_VIDEO:
            validateDeleteVideoParameters(params as IDeleteVideoParameters, errors);
            break;

        case EFacebookPageOperation.LIST_PHOTOS:
            validateListPhotosParameters(params as IListPhotosParameters, errors);
            break;

        case EFacebookPageOperation.GET_PHOTO:
            validateGetPhotoParameters(params as IGetPhotoParameters, errors);
            break;

        case EFacebookPageOperation.UPLOAD_PHOTO:
            validateUploadPhotoParameters(params as IUploadPhotoParameters, errors);
            break;

        case EFacebookPageOperation.DELETE_PHOTO:
            validateDeletePhotoParameters(params as IDeletePhotoParameters, errors);
            break;

        case EFacebookPageOperation.LIST_COMMENTS:
            validateListCommentsParameters(params as IListCommentsParameters, errors);
            break;

        case EFacebookPageOperation.GET_COMMENT:
            validateGetCommentParameters(params as IGetCommentParameters, errors);
            break;

        case EFacebookPageOperation.CREATE_COMMENT:
            validateCreateCommentParameters(params as ICreateCommentParameters, errors);
            break;

        case EFacebookPageOperation.UPDATE_COMMENT:
            validateUpdateCommentParameters(params as IUpdateCommentParameters, errors);
            break;

        case EFacebookPageOperation.DELETE_COMMENT:
            validateDeleteCommentParameters(params as IDeleteCommentParameters, errors);
            break;

        case EFacebookPageOperation.GET_PAGE:
            validateGetPageParameters(params as IGetPageParameters, errors);
            break;

        // Add more operation validations here as we implement them
        default:
            // For now, just validate basic requirements
            break;
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}

/**
 * Validate List Posts parameters
 */
function validateListPostsParameters(
    params: IListPostsParameters,
    errors: string[]
): void {
    // Page ID is required
    if (!params.page_id) {
        errors.push('Page ID is required for List Posts operation');
    }

    // Limit is required and must be within range
    if (!params.limit) {
        errors.push('Limit is required for List Posts operation');
    } else if (params.limit < 1 || params.limit > 500) {
        errors.push('Limit must be between 1 and 500');
    }

    // Include hidden validation (optional)
    if (params.include_hidden !== undefined &&
        !['yes', 'no', 'empty'].includes(params.include_hidden.toString())) {
        errors.push('Include hidden must be "yes", "no", or "empty"');
    }
}

/**
 * Validate Get a Post parameters
 */
function validateGetPostParameters(
    params: IGetPostParameters,
    errors: string[]
): void {
    // Page ID is required
    if (!params.page_id) {
        errors.push('Page ID is required for Get a Post operation');
    }

    // Post ID is required
    if (!params.post_id) {
        errors.push('Post ID is required for Get a Post operation');
    }

    // Validate Post ID format (should be numeric or page_id_postid format)
    if (params.post_id && !validatePostId(params.post_id)) {
        errors.push('Post ID must be in valid format (numeric or page_id_postid)');
    }
}

/**
 * Validate Get Post Reactions parameters
 */
function validateGetPostReactionsParameters(
    params: IGetPostReactionsParameters,
    errors: string[]
): void {
    // Page ID is required
    if (!params.page_id) {
        errors.push('Page ID is required for Get Post Reactions operation');
    }

    // Post ID is required
    if (!params.post_id) {
        errors.push('Post ID is required for Get Post Reactions operation');
    }

    // Validate Post ID format
    if (params.post_id && !validatePostId(params.post_id)) {
        errors.push('Post ID must be in valid format (numeric or page_id_postid)');
    }

    // Validate limit if provided
    if (params.limit !== undefined && (params.limit < 1 || params.limit > 100)) {
        errors.push('Limit must be between 1 and 100 for Get Post Reactions operation');
    }
}

/**
 * Validate Create a Post parameters
 */
function validateCreatePostParameters(
    params: ICreatePostParameters,
    errors: string[]
): void {
    // Page ID is required
    if (!params.page_id) {
        errors.push('Page ID is required for Create a Post operation');
    }

    // At least message or link must be provided
    if (!params.message && !params.link) {
        errors.push('Either message or link must be provided for Create a Post operation');
    }

    // Validate scheduled publish time format if provided
    if (params.scheduled_publish_time && !isValidISOString(params.scheduled_publish_time)) {
        errors.push('Scheduled publish time must be a valid ISO string');
    }

    // If scheduled publish time is provided, published should be false
    if (params.scheduled_publish_time && params.published !== false) {
        errors.push('Published must be false when scheduled publish time is provided');
    }
}

/**
 * Validate Create a Post with Photos parameters
 */
function validateCreatePostWithPhotosParameters(
    params: ICreatePostWithPhotosParameters,
    errors: string[]
): void {
    // Page ID is required
    if (!params.page_id) {
        errors.push('Page ID is required for Create a Post with Photos operation');
    }

    // Photos array is required and must have at least 1 item
    if (!params.photos || !Array.isArray(params.photos) || params.photos.length === 0) {
        errors.push('At least one photo is required for Create a Post with Photos operation');
    } else {
        // Validate each photo item
        params.photos.forEach((photo, index) => {
            if (!photo.action) {
                errors.push(`Photo ${index + 1}: Action is required`);
            } else {
                // Validate based on action type
                if (photo.action === EFacebookPhotoAction.UPLOAD) {
                    if (!photo.file) {
                        errors.push(`Photo ${index + 1}: File is required for upload action`);
                    } else {
                        if (!photo.file.filename) {
                            errors.push(`Photo ${index + 1}: File name is required`);
                        }
                        if (!photo.file.data) {
                            errors.push(`Photo ${index + 1}: File data is required`);
                        }
                    }
                } else if (photo.action === EFacebookPhotoAction.DOWNLOAD_FROM_URL) {
                    if (!photo.url) {
                        errors.push(`Photo ${index + 1}: URL is required for download from URL action`);
                    } else if (!isValidUrl(photo.url)) {
                        errors.push(`Photo ${index + 1}: URL must be a valid URL`);
                    }
                }
            }
        });
    }

    // Validate scheduled publish time format if provided
    if (params.scheduled_publish_time && !isValidISOString(params.scheduled_publish_time)) {
        errors.push('Scheduled publish time must be a valid ISO string');
    }

    // If scheduled publish time is provided, published should be false
    if (params.scheduled_publish_time && params.published !== false) {
        errors.push('Published must be false when scheduled publish time is provided');
    }
}

/**
 * Validate Update a Post parameters
 */
function validateUpdatePostParameters(
    params: IUpdatePostParameters,
    errors: string[]
): void {
    // Page ID is required
    if (!params.page_id) {
        errors.push('Page ID is required for Update a Post operation');
    }

    // Post ID is required
    if (!params.post_id) {
        errors.push('Post ID is required for Update a Post operation');
    }

    // Validate Post ID format
    if (params.post_id && !validatePostId(params.post_id)) {
        errors.push('Post ID must be in valid format (numeric or page_id_postid)');
    }

    // Message is optional but if provided should not be empty
    if (params.message !== undefined && params.message.trim() === '') {
        errors.push('Message cannot be empty if provided');
    }
}

/**
 * Validate Delete a Post parameters
 */
function validateDeletePostParameters(
    params: IDeletePostParameters,
    errors: string[]
): void {
    // Page ID is required
    if (!params.page_id) {
        errors.push('Page ID is required for Delete a Post operation');
    }

    // Post ID is required
    if (!params.post_id) {
        errors.push('Post ID is required for Delete a Post operation');
    }

    // Validate Post ID format
    if (params.post_id && !validatePostId(params.post_id)) {
        errors.push('Post ID must be in valid format (numeric or page_id_postid)');
    }
}

/**
 * Validate Like a Post parameters
 */
function validateLikePostParameters(
    params: ILikePostParameters,
    errors: string[]
): void {
    // Page ID is required
    if (!params.page_id) {
        errors.push('Page ID is required for Like a Post operation');
    }

    // Post ID is required
    if (!params.post_id) {
        errors.push('Post ID is required for Like a Post operation');
    }

    // Validate Post ID format
    if (params.post_id && !validatePostId(params.post_id)) {
        errors.push('Post ID must be in valid format (numeric or page_id_postid)');
    }
}

/**
 * Validate Unlike a Post parameters
 */
function validateUnlikePostParameters(
    params: IUnlikePostParameters,
    errors: string[]
): void {
    // Page ID is required
    if (!params.page_id) {
        errors.push('Page ID is required for Unlike a Post operation');
    }

    // Post ID is required
    if (!params.post_id) {
        errors.push('Post ID is required for Unlike a Post operation');
    }

    // Validate Post ID format
    if (params.post_id && !validatePostId(params.post_id)) {
        errors.push('Post ID must be in valid format (numeric or page_id_postid)');
    }
}

/**
 * Validate List Videos parameters
 */
function validateListVideosParameters(
    params: IListVideosParameters,
    errors: string[]
): void {
    // Page ID is required
    if (!params.page_id) {
        errors.push('Page ID is required for List Videos operation');
    }

    // Type is required
    if (!params.type) {
        errors.push('Type is required for List Videos operation');
    }

    // Limit is required and must be within range
    if (!params.limit) {
        errors.push('Limit is required for List Videos operation');
    } else if (params.limit < 1 || params.limit > 500) {
        errors.push('Limit must be between 1 and 500');
    }
}

/**
 * Validate Get a Video parameters
 */
function validateGetVideoParameters(
    params: IGetVideoParameters,
    errors: string[]
): void {
    // Page ID is required
    if (!params.page_id) {
        errors.push('Page ID is required for Get a Video operation');
    }

    // Video ID is required
    if (!params.video_id) {
        errors.push('Video ID is required for Get a Video operation');
    }

    // Validate Video ID format (should be numeric)
    if (params.video_id && !validateVideoId(params.video_id)) {
        errors.push('Video ID must be a valid numeric string');
    }
}

/**
 * Validate Upload a Video parameters
 */
function validateUploadVideoParameters(
    params: IUploadVideoParameters,
    errors: string[]
): void {
    // Page ID is required
    if (!params.page_id) {
        errors.push('Page ID is required for Upload a Video operation');
    }

    // Type is required
    if (!params.type) {
        errors.push('Type is required for Upload a Video operation');
    }

    // File is required
    if (!params.file) {
        errors.push('File is required for Upload a Video operation');
    } else {
        // File name is required
        if (!params.file.filename) {
            errors.push('File name is required for Upload a Video operation');
        }

        // File data is required
        if (!params.file.data) {
            errors.push('File data is required for Upload a Video operation');
        }
    }

    // Description is optional but if provided should not be empty
    if (params.description !== undefined && params.description.trim() === '') {
        errors.push('Description cannot be empty if provided');
    }
}

/**
 * Validate Update a Video parameters
 */
function validateUpdateVideoParameters(
    params: IUpdateVideoParameters,
    errors: string[]
): void {
    // Page ID is required
    if (!params.page_id) {
        errors.push('Page ID is required for Update a Video operation');
    }

    // Video ID is required
    if (!params.video_id) {
        errors.push('Video ID is required for Update a Video operation');
    }

    // Validate Video ID format
    if (params.video_id && !validateVideoId(params.video_id)) {
        errors.push('Video ID must be a valid numeric string');
    }

    // Title is optional but if provided should not be empty
    if (params.title !== undefined && params.title.trim() === '') {
        errors.push('Title cannot be empty if provided');
    }

    // Description is optional but if provided should not be empty
    if (params.description !== undefined && params.description.trim() === '') {
        errors.push('Description cannot be empty if provided');
    }
}

/**
 * Validate Delete a Video parameters
 */
function validateDeleteVideoParameters(
    params: IDeleteVideoParameters,
    errors: string[]
): void {
    // Page ID is required
    if (!params.page_id) {
        errors.push('Page ID is required for Delete a Video operation');
    }

    // Video ID is required
    if (!params.video_id) {
        errors.push('Video ID is required for Delete a Video operation');
    }

    // Validate Video ID format
    if (params.video_id && !validateVideoId(params.video_id)) {
        errors.push('Video ID must be a valid numeric string');
    }
}

/**
 * Validate List Photos parameters
 */
function validateListPhotosParameters(
    params: IListPhotosParameters,
    errors: string[]
): void {
    // Page ID is required
    if (!params.page_id) {
        errors.push('Page ID is required for List Photos operation');
    }

    // Limit is required and must be within range
    if (!params.limit) {
        errors.push('Limit is required for List Photos operation');
    } else if (params.limit < 1 || params.limit > 500) {
        errors.push('Limit must be between 1 and 500');
    }
}

/**
 * Validate Get a Photo parameters
 */
function validateGetPhotoParameters(
    params: IGetPhotoParameters,
    errors: string[]
): void {
    // Page ID is required
    if (!params.page_id) {
        errors.push('Page ID is required for Get a Photo operation');
    }

    // Photo ID is required
    if (!params.photo_id) {
        errors.push('Photo ID is required for Get a Photo operation');
    }

    // Validate Photo ID format (should be numeric)
    if (params.photo_id && !validatePhotoId(params.photo_id)) {
        errors.push('Photo ID must be a valid numeric string');
    }
}

/**
 * Validate Upload a Photo parameters
 */
function validateUploadPhotoParameters(
    params: IUploadPhotoParameters,
    errors: string[]
): void {
    // Page ID is required
    if (!params.page_id) {
        errors.push('Page ID is required for Upload a Photo operation');
    }

    // File is required
    if (!params.file) {
        errors.push('File is required for Upload a Photo operation');
    } else {
        // File name is required
        if (!params.file.filename) {
            errors.push('File name is required for Upload a Photo operation');
        }

        // File data is required
        if (!params.file.data) {
            errors.push('File data is required for Upload a Photo operation');
        }
    }

    // Message is optional but if provided should not be empty
    if (params.message !== undefined && params.message.trim() === '') {
        errors.push('Message cannot be empty if provided');
    }
}

/**
 * Validate Delete a Photo parameters
 */
function validateDeletePhotoParameters(
    params: IDeletePhotoParameters,
    errors: string[]
): void {
    // Page ID is required
    if (!params.page_id) {
        errors.push('Page ID is required for Delete a Photo operation');
    }

    // Photo ID is required
    if (!params.photo_id) {
        errors.push('Photo ID is required for Delete a Photo operation');
    }

    // Validate Photo ID format
    if (params.photo_id && !validatePhotoId(params.photo_id)) {
        errors.push('Photo ID must be a valid numeric string');
    }
}

/**
 * Validate List Comments parameters
 */
function validateListCommentsParameters(
    params: IListCommentsParameters,
    errors: string[]
): void {
    // Page ID is required
    if (!params.page_id) {
        errors.push('Page ID is required for List Comments operation');
    }

    // Filter is required
    if (!params.filter) {
        errors.push('Filter is required for List Comments operation');
    }

    // Limit is required and must be within range
    if (!params.limit) {
        errors.push('Limit is required for List Comments operation');
    } else if (params.limit < 1 || params.limit > 500) {
        errors.push('Limit must be between 1 and 500');
    }

    // Order is optional but if provided should not be empty
    if (params.order !== undefined && params.order.trim() === '') {
        errors.push('Order cannot be empty if provided');
    }
}

/**
 * Validate Get a Comment parameters
 */
function validateGetCommentParameters(
    params: IGetCommentParameters,
    errors: string[]
): void {
    // Page ID is required
    if (!params.page_id) {
        errors.push('Page ID is required for Get a Comment operation');
    }

    // Comment ID is required
    if (!params.comment_id) {
        errors.push('Comment ID is required for Get a Comment operation');
    }

    // Validate Comment ID format (should be numeric)
    if (params.comment_id && !validateCommentId(params.comment_id)) {
        errors.push('Comment ID must be a valid numeric string');
    }
}

/**
 * Validate Create a Comment parameters
 */
function validateCreateCommentParameters(
    params: ICreateCommentParameters,
    errors: string[]
): void {
    // Page ID is required
    if (!params.page_id) {
        errors.push('Page ID is required for Create a Comment operation');
    }

    // Message is optional but if provided should not be empty
    if (params.message !== undefined && params.message.trim() === '') {
        errors.push('Message cannot be empty if provided');
    }

    // Attachment type is optional but if provided should not be empty
    if (params.attachment_type !== undefined && params.attachment_type.trim() === '') {
        errors.push('Attachment type cannot be empty if provided');
    }
}

/**
 * Validate Update a Comment parameters
 */
function validateUpdateCommentParameters(
    params: IUpdateCommentParameters,
    errors: string[]
): void {
    // Page ID is required
    if (!params.page_id) {
        errors.push('Page ID is required for Update a Comment operation');
    }

    // Comment ID is required
    if (!params.comment_id) {
        errors.push('Comment ID is required for Update a Comment operation');
    }

    // Validate Comment ID format
    if (params.comment_id && !validateCommentId(params.comment_id)) {
        errors.push('Comment ID must be a valid numeric string');
    }

    // Message is optional but if provided should not be empty
    if (params.message !== undefined && params.message.trim() === '') {
        errors.push('Message cannot be empty if provided');
    }

    // Attachment type is optional but if provided should not be empty
    if (params.attachment_type !== undefined && params.attachment_type.trim() === '') {
        errors.push('Attachment type cannot be empty if provided');
    }
}

/**
 * Validate Delete a Comment parameters
 */
function validateDeleteCommentParameters(
    params: IDeleteCommentParameters,
    errors: string[]
): void {
    // Page ID is required
    if (!params.page_id) {
        errors.push('Page ID is required for Delete a Comment operation');
    }

    // Comment ID is required
    if (!params.comment_id) {
        errors.push('Comment ID is required for Delete a Comment operation');
    }

    // Validate Comment ID format
    if (params.comment_id && !validateCommentId(params.comment_id)) {
        errors.push('Comment ID must be a valid numeric string');
    }
}

/**
 * Validate Get a Page parameters
 */
function validateGetPageParameters(
    params: IGetPageParameters,
    errors: string[]
): void {
    // Page ID is required
    if (!params.page_id) {
        errors.push('Page ID is required for Get a Page operation');
    }
}

/**
 * Type guard to check if parameters are Facebook Page parameters (detailed check)
 */
export function isFacebookPageParametersDetailed(
    params: any
): params is IFacebookPageParameters {
    return params && 
           typeof params.operation === 'string' &&
           Object.values(EFacebookPageOperation).includes(params.operation);
}

/**
 * Type guard to check if parameters are List Posts parameters
 */
export function isListPostsParameters(
    params: any
): params is IListPostsParameters {
    return params &&
           params.operation === EFacebookPageOperation.LIST_POSTS &&
           typeof params.page_id === 'string' &&
           typeof params.limit === 'number';
}

/**
 * Type guard to check if parameters are Get a Post parameters
 */
export function isGetPostParameters(
    params: any
): params is IGetPostParameters {
    return params &&
           params.operation === EFacebookPageOperation.GET_POST &&
           typeof params.page_id === 'string' &&
           typeof params.post_id === 'string';
}

/**
 * Type guard to check if parameters are Get Post Reactions parameters
 */
export function isGetPostReactionsParameters(
    params: any
): params is IGetPostReactionsParameters {
    return params &&
           params.operation === EFacebookPageOperation.GET_POST_REACTIONS &&
           typeof params.page_id === 'string' &&
           typeof params.post_id === 'string';
}

/**
 * Type guard to check if parameters are Create a Post parameters
 */
export function isCreatePostParameters(
    params: any
): params is ICreatePostParameters {
    return params &&
           params.operation === EFacebookPageOperation.CREATE_POST &&
           typeof params.page_id === 'string';
}

/**
 * Type guard to check if parameters are Create a Post with Photos parameters
 */
export function isCreatePostWithPhotosParameters(
    params: any
): params is ICreatePostWithPhotosParameters {
    return params &&
           params.operation === EFacebookPageOperation.CREATE_POST_WITH_PHOTOS &&
           typeof params.page_id === 'string' &&
           Array.isArray(params.photos);
}

/**
 * Type guard to check if parameters are Update a Post parameters
 */
export function isUpdatePostParameters(
    params: any
): params is IUpdatePostParameters {
    return params &&
           params.operation === EFacebookPageOperation.UPDATE_POST &&
           typeof params.page_id === 'string' &&
           typeof params.post_id === 'string';
}

/**
 * Type guard to check if parameters are Delete a Post parameters
 */
export function isDeletePostParameters(
    params: any
): params is IDeletePostParameters {
    return params &&
           params.operation === EFacebookPageOperation.DELETE_POST &&
           typeof params.page_id === 'string' &&
           typeof params.post_id === 'string';
}

/**
 * Type guard to check if parameters are Like a Post parameters
 */
export function isLikePostParameters(
    params: any
): params is ILikePostParameters {
    return params &&
           params.operation === EFacebookPageOperation.LIKE_POST &&
           typeof params.page_id === 'string' &&
           typeof params.post_id === 'string';
}

/**
 * Type guard to check if parameters are Unlike a Post parameters
 */
export function isUnlikePostParameters(
    params: any
): params is IUnlikePostParameters {
    return params &&
           params.operation === EFacebookPageOperation.UNLIKE_POST &&
           typeof params.page_id === 'string' &&
           typeof params.post_id === 'string';
}

/**
 * Type guard to check if parameters are List Videos parameters
 */
export function isListVideosParameters(
    params: any
): params is IListVideosParameters {
    return params &&
           params.operation === EFacebookPageOperation.LIST_VIDEOS &&
           typeof params.page_id === 'string' &&
           typeof params.type === 'string' &&
           typeof params.limit === 'number';
}

/**
 * Type guard to check if parameters are Get a Video parameters
 */
export function isGetVideoParameters(
    params: any
): params is IGetVideoParameters {
    return params &&
           params.operation === EFacebookPageOperation.GET_VIDEO &&
           typeof params.page_id === 'string' &&
           typeof params.video_id === 'string';
}

/**
 * Type guard to check if parameters are Upload a Video parameters
 */
export function isUploadVideoParameters(
    params: any
): params is IUploadVideoParameters {
    return params &&
           params.operation === EFacebookPageOperation.UPLOAD_VIDEO &&
           typeof params.page_id === 'string' &&
           typeof params.type === 'string' &&
           params.file &&
           typeof params.file.filename === 'string' &&
           params.file.data;
}

/**
 * Type guard to check if parameters are Update a Video parameters
 */
export function isUpdateVideoParameters(
    params: any
): params is IUpdateVideoParameters {
    return params &&
           params.operation === EFacebookPageOperation.UPDATE_VIDEO &&
           typeof params.page_id === 'string' &&
           typeof params.video_id === 'string';
}

/**
 * Type guard to check if parameters are Delete a Video parameters
 */
export function isDeleteVideoParameters(
    params: any
): params is IDeleteVideoParameters {
    return params &&
           params.operation === EFacebookPageOperation.DELETE_VIDEO &&
           typeof params.page_id === 'string' &&
           typeof params.video_id === 'string';
}

/**
 * Type guard to check if parameters are List Photos parameters
 */
export function isListPhotosParameters(
    params: any
): params is IListPhotosParameters {
    return params &&
           params.operation === EFacebookPageOperation.LIST_PHOTOS &&
           typeof params.page_id === 'string' &&
           typeof params.limit === 'number';
}

/**
 * Type guard to check if parameters are Get a Photo parameters
 */
export function isGetPhotoParameters(
    params: any
): params is IGetPhotoParameters {
    return params &&
           params.operation === EFacebookPageOperation.GET_PHOTO &&
           typeof params.page_id === 'string' &&
           typeof params.photo_id === 'string';
}

/**
 * Type guard to check if parameters are Upload a Photo parameters
 */
export function isUploadPhotoParameters(
    params: any
): params is IUploadPhotoParameters {
    return params &&
           params.operation === EFacebookPageOperation.UPLOAD_PHOTO &&
           typeof params.page_id === 'string' &&
           params.file &&
           typeof params.file.filename === 'string' &&
           params.file.data;
}

/**
 * Type guard to check if parameters are Delete a Photo parameters
 */
export function isDeletePhotoParameters(
    params: any
): params is IDeletePhotoParameters {
    return params &&
           params.operation === EFacebookPageOperation.DELETE_PHOTO &&
           typeof params.page_id === 'string' &&
           typeof params.photo_id === 'string';
}

/**
 * Type guard to check if parameters are List Comments parameters
 */
export function isListCommentsParameters(
    params: any
): params is IListCommentsParameters {
    return params &&
           params.operation === EFacebookPageOperation.LIST_COMMENTS &&
           typeof params.page_id === 'string' &&
           typeof params.filter === 'string' &&
           typeof params.limit === 'number';
}

/**
 * Type guard to check if parameters are Get a Comment parameters
 */
export function isGetCommentParameters(
    params: any
): params is IGetCommentParameters {
    return params &&
           params.operation === EFacebookPageOperation.GET_COMMENT &&
           typeof params.page_id === 'string' &&
           typeof params.comment_id === 'string';
}

/**
 * Type guard to check if parameters are Create a Comment parameters
 */
export function isCreateCommentParameters(
    params: any
): params is ICreateCommentParameters {
    return params &&
           params.operation === EFacebookPageOperation.CREATE_COMMENT &&
           typeof params.page_id === 'string';
}

/**
 * Type guard to check if parameters are Update a Comment parameters
 */
export function isUpdateCommentParameters(
    params: any
): params is IUpdateCommentParameters {
    return params &&
           params.operation === EFacebookPageOperation.UPDATE_COMMENT &&
           typeof params.page_id === 'string' &&
           typeof params.comment_id === 'string';
}

/**
 * Type guard to check if parameters are Delete a Comment parameters
 */
export function isDeleteCommentParameters(
    params: any
): params is IDeleteCommentParameters {
    return params &&
           params.operation === EFacebookPageOperation.DELETE_COMMENT &&
           typeof params.page_id === 'string' &&
           typeof params.comment_id === 'string';
}

/**
 * Type guard to check if parameters are Get a Page parameters
 */
export function isGetPageParameters(
    params: any
): params is IGetPageParameters {
    return params &&
           params.operation === EFacebookPageOperation.GET_PAGE &&
           typeof params.page_id === 'string';
}

/**
 * Validate page ID format
 */
export function validatePageId(pageId: string): boolean {
    // Facebook Page ID should be numeric string
    return /^\d+$/.test(pageId);
}

/**
 * Validate limit value
 */
export function validateLimit(limit: number): boolean {
    return Number.isInteger(limit) && limit >= 1 && limit <= 500;
}

/**
 * Validate post ID format
 */
export function validatePostId(postId: string): boolean {
    // Facebook Post ID can be:
    // 1. Numeric string (e.g., "123456789")
    // 2. Page_ID_Post_ID format (e.g., "123456789_987654321")
    return /^\d+$/.test(postId) || /^\d+_\d+$/.test(postId);
}

/**
 * Validate ISO string format
 */
export function isValidISOString(dateString: string): boolean {
    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date.getTime()) && date.toISOString() === dateString;
}

/**
 * Validate URL format
 */
export function isValidUrl(url: string): boolean {
    try {
        new URL(url);
        return true;
    } catch {
        return false;
    }
}

/**
 * Validate video ID format
 */
export function validateVideoId(videoId: string): boolean {
    // Facebook Video ID should be numeric string
    return /^\d+$/.test(videoId);
}

/**
 * Validate photo ID format
 */
export function validatePhotoId(photoId: string): boolean {
    // Facebook Photo ID should be numeric string
    return /^\d+$/.test(photoId);
}

/**
 * Validate comment ID format
 */
export function validateCommentId(commentId: string): boolean {
    // Facebook Comment ID should be numeric string
    return /^\d+$/.test(commentId);
}

/**
 * Sanitize parameters for Facebook Page operations
 */
export function sanitizeFacebookPageParameters(
    params: Partial<IFacebookPageParameters>
): Partial<IFacebookPageParameters> {
    const sanitized = { ...params };

    // Sanitize page_id
    if (sanitized.page_id) {
        sanitized.page_id = sanitized.page_id.toString().trim();
    }

    // Sanitize post_id for Get a Post and Get Post Reactions
    if (sanitized.operation === EFacebookPageOperation.GET_POST) {
        const postParams = sanitized as IGetPostParameters;
        if (postParams.post_id) {
            postParams.post_id = postParams.post_id.toString().trim();
        }
    }

    if (sanitized.operation === EFacebookPageOperation.GET_POST_REACTIONS) {
        const reactionsParams = sanitized as IGetPostReactionsParameters;
        if (reactionsParams.post_id) {
            reactionsParams.post_id = reactionsParams.post_id.toString().trim();
        }
        // Sanitize limit for Get Post Reactions
        if (reactionsParams.limit) {
            reactionsParams.limit = Math.max(1, Math.min(100, Math.floor(reactionsParams.limit)));
        }
    }

    // Sanitize limit for List Posts
    if (sanitized.operation === EFacebookPageOperation.LIST_POSTS) {
        const listParams = sanitized as IListPostsParameters;
        if (listParams.limit) {
            listParams.limit = Math.max(1, Math.min(500, Math.floor(listParams.limit)));
        }
    }

    // Sanitize Create a Post parameters
    if (sanitized.operation === EFacebookPageOperation.CREATE_POST) {
        const createPostParams = sanitized as ICreatePostParameters;

        // Trim message and link
        if (createPostParams.message) {
            createPostParams.message = createPostParams.message.trim();
        }
        if (createPostParams.link) {
            createPostParams.link = createPostParams.link.trim();
        }

        // Ensure published is boolean
        if (createPostParams.published !== undefined) {
            createPostParams.published = Boolean(createPostParams.published);
        }
    }

    // Sanitize Create a Post with Photos parameters
    if (sanitized.operation === EFacebookPageOperation.CREATE_POST_WITH_PHOTOS) {
        const createPostWithPhotosParams = sanitized as ICreatePostWithPhotosParameters;

        // Trim message
        if (createPostWithPhotosParams.message) {
            createPostWithPhotosParams.message = createPostWithPhotosParams.message.trim();
        }

        // Sanitize photos array
        if (createPostWithPhotosParams.photos && Array.isArray(createPostWithPhotosParams.photos)) {
            createPostWithPhotosParams.photos = createPostWithPhotosParams.photos.map(photo => {
                const sanitizedPhoto = { ...photo };

                // Trim caption
                if (sanitizedPhoto.caption) {
                    sanitizedPhoto.caption = sanitizedPhoto.caption.trim();
                }

                // Trim URL for download action
                if (sanitizedPhoto.action === EFacebookPhotoAction.DOWNLOAD_FROM_URL && sanitizedPhoto.url) {
                    sanitizedPhoto.url = sanitizedPhoto.url.trim();
                }

                // Trim filename for upload action
                if (sanitizedPhoto.action === EFacebookPhotoAction.UPLOAD && sanitizedPhoto.file?.filename) {
                    sanitizedPhoto.file.filename = sanitizedPhoto.file.filename.trim();
                }

                return sanitizedPhoto;
            });
        }

        // Ensure published is boolean
        if (createPostWithPhotosParams.published !== undefined) {
            createPostWithPhotosParams.published = Boolean(createPostWithPhotosParams.published);
        }
    }

    // Sanitize Update a Post parameters
    if (sanitized.operation === EFacebookPageOperation.UPDATE_POST) {
        const updatePostParams = sanitized as IUpdatePostParameters;

        // Trim message
        if (updatePostParams.message) {
            updatePostParams.message = updatePostParams.message.trim();
        }

        // Trim post_id
        if (updatePostParams.post_id) {
            updatePostParams.post_id = updatePostParams.post_id.trim();
        }
    }

    // Sanitize Delete a Post parameters
    if (sanitized.operation === EFacebookPageOperation.DELETE_POST) {
        const deletePostParams = sanitized as IDeletePostParameters;

        // Trim post_id
        if (deletePostParams.post_id) {
            deletePostParams.post_id = deletePostParams.post_id.trim();
        }
    }

    // Sanitize Like a Post parameters
    if (sanitized.operation === EFacebookPageOperation.LIKE_POST) {
        const likePostParams = sanitized as ILikePostParameters;

        // Trim post_id
        if (likePostParams.post_id) {
            likePostParams.post_id = likePostParams.post_id.trim();
        }
    }

    // Sanitize Unlike a Post parameters
    if (sanitized.operation === EFacebookPageOperation.UNLIKE_POST) {
        const unlikePostParams = sanitized as IUnlikePostParameters;

        // Trim post_id
        if (unlikePostParams.post_id) {
            unlikePostParams.post_id = unlikePostParams.post_id.trim();
        }
    }

    // Sanitize List Videos parameters
    if (sanitized.operation === EFacebookPageOperation.LIST_VIDEOS) {
        const listVideosParams = sanitized as IListVideosParameters;

        // Trim type
        if (listVideosParams.type) {
            listVideosParams.type = listVideosParams.type.trim();
        }

        // Sanitize limit
        if (listVideosParams.limit) {
            listVideosParams.limit = Math.max(1, Math.min(500, Math.floor(listVideosParams.limit)));
        }
    }

    // Sanitize Get a Video parameters
    if (sanitized.operation === EFacebookPageOperation.GET_VIDEO) {
        const getVideoParams = sanitized as IGetVideoParameters;

        // Trim video_id
        if (getVideoParams.video_id) {
            getVideoParams.video_id = getVideoParams.video_id.trim();
        }
    }

    // Sanitize Upload a Video parameters
    if (sanitized.operation === EFacebookPageOperation.UPLOAD_VIDEO) {
        const uploadVideoParams = sanitized as IUploadVideoParameters;

        // Trim type
        if (uploadVideoParams.type) {
            uploadVideoParams.type = uploadVideoParams.type.trim();
        }

        // Trim description
        if (uploadVideoParams.description) {
            uploadVideoParams.description = uploadVideoParams.description.trim();
        }

        // Trim filename
        if (uploadVideoParams.file?.filename) {
            uploadVideoParams.file.filename = uploadVideoParams.file.filename.trim();
        }
    }

    // Sanitize Update a Video parameters
    if (sanitized.operation === EFacebookPageOperation.UPDATE_VIDEO) {
        const updateVideoParams = sanitized as IUpdateVideoParameters;

        // Trim video_id
        if (updateVideoParams.video_id) {
            updateVideoParams.video_id = updateVideoParams.video_id.trim();
        }

        // Trim title
        if (updateVideoParams.title) {
            updateVideoParams.title = updateVideoParams.title.trim();
        }

        // Trim description
        if (updateVideoParams.description) {
            updateVideoParams.description = updateVideoParams.description.trim();
        }
    }

    // Sanitize Delete a Video parameters
    if (sanitized.operation === EFacebookPageOperation.DELETE_VIDEO) {
        const deleteVideoParams = sanitized as IDeleteVideoParameters;

        // Trim video_id
        if (deleteVideoParams.video_id) {
            deleteVideoParams.video_id = deleteVideoParams.video_id.trim();
        }
    }

    // Sanitize List Photos parameters
    if (sanitized.operation === EFacebookPageOperation.LIST_PHOTOS) {
        const listPhotosParams = sanitized as IListPhotosParameters;

        // Sanitize limit
        if (listPhotosParams.limit) {
            listPhotosParams.limit = Math.max(1, Math.min(500, Math.floor(listPhotosParams.limit)));
        }
    }

    // Sanitize Get a Photo parameters
    if (sanitized.operation === EFacebookPageOperation.GET_PHOTO) {
        const getPhotoParams = sanitized as IGetPhotoParameters;

        // Trim photo_id
        if (getPhotoParams.photo_id) {
            getPhotoParams.photo_id = getPhotoParams.photo_id.trim();
        }
    }

    // Sanitize Upload a Photo parameters
    if (sanitized.operation === EFacebookPageOperation.UPLOAD_PHOTO) {
        const uploadPhotoParams = sanitized as IUploadPhotoParameters;

        // Trim message
        if (uploadPhotoParams.message) {
            uploadPhotoParams.message = uploadPhotoParams.message.trim();
        }

        // Trim filename
        if (uploadPhotoParams.file?.filename) {
            uploadPhotoParams.file.filename = uploadPhotoParams.file.filename.trim();
        }
    }

    // Sanitize Delete a Photo parameters
    if (sanitized.operation === EFacebookPageOperation.DELETE_PHOTO) {
        const deletePhotoParams = sanitized as IDeletePhotoParameters;

        // Trim photo_id
        if (deletePhotoParams.photo_id) {
            deletePhotoParams.photo_id = deletePhotoParams.photo_id.trim();
        }
    }

    // Sanitize List Comments parameters
    if (sanitized.operation === EFacebookPageOperation.LIST_COMMENTS) {
        const listCommentsParams = sanitized as IListCommentsParameters;

        // Trim filter
        if (listCommentsParams.filter) {
            listCommentsParams.filter = listCommentsParams.filter.trim();
        }

        // Trim order
        if (listCommentsParams.order) {
            listCommentsParams.order = listCommentsParams.order.trim();
        }

        // Sanitize limit
        if (listCommentsParams.limit) {
            listCommentsParams.limit = Math.max(1, Math.min(500, Math.floor(listCommentsParams.limit)));
        }
    }

    // Sanitize Get a Comment parameters
    if (sanitized.operation === EFacebookPageOperation.GET_COMMENT) {
        const getCommentParams = sanitized as IGetCommentParameters;

        // Trim comment_id
        if (getCommentParams.comment_id) {
            getCommentParams.comment_id = getCommentParams.comment_id.trim();
        }
    }

    // Sanitize Create a Comment parameters
    if (sanitized.operation === EFacebookPageOperation.CREATE_COMMENT) {
        const createCommentParams = sanitized as ICreateCommentParameters;

        // Trim message
        if (createCommentParams.message) {
            createCommentParams.message = createCommentParams.message.trim();
        }

        // Trim attachment_type
        if (createCommentParams.attachment_type) {
            createCommentParams.attachment_type = createCommentParams.attachment_type.trim();
        }
    }

    // Sanitize Update a Comment parameters
    if (sanitized.operation === EFacebookPageOperation.UPDATE_COMMENT) {
        const updateCommentParams = sanitized as IUpdateCommentParameters;

        // Trim comment_id
        if (updateCommentParams.comment_id) {
            updateCommentParams.comment_id = updateCommentParams.comment_id.trim();
        }

        // Trim message
        if (updateCommentParams.message) {
            updateCommentParams.message = updateCommentParams.message.trim();
        }

        // Trim attachment_type
        if (updateCommentParams.attachment_type) {
            updateCommentParams.attachment_type = updateCommentParams.attachment_type.trim();
        }
    }

    // Sanitize Delete a Comment parameters
    if (sanitized.operation === EFacebookPageOperation.DELETE_COMMENT) {
        const deleteCommentParams = sanitized as IDeleteCommentParameters;

        // Trim comment_id
        if (deleteCommentParams.comment_id) {
            deleteCommentParams.comment_id = deleteCommentParams.comment_id.trim();
        }
    }

    return sanitized;
}
