import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In, IsNull } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import * as crypto from 'crypto';
import { Platform, MessageRole } from '@/shared';
import { AppException } from '@/common';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { CdnService } from '@/shared/services/cdn.service';
import { Media } from '@/modules/data/media/entities/media.entity';
import { KnowledgeFile } from '@/modules/data/knowledge-files/entities/knowledge-file.entity';

// Import entities
import { ExternalConversationMessage } from '@/modules/business/entities/external-conversation-message.entity';
import {
  ExternalConversationMessageAttachment,
  ConversationThreadsAttachmentType,
} from '@/modules/business/entities/external-conversation-message-attachment.entity';

// Import DTOs
import {
  ExternalMessageRequestDto,
  AttachmentContentDto,
  AttachmentContentType,
  MessageContentType,
} from '../dto';
import { QueryMessagesDto } from '../dto';
import { CHAT_ERROR_CODES } from '../exceptions';
import { PayloadLiveChatKey } from '@/modules/integration/interfaces/website.interface';
import { TimeIntervalEnum } from '@/shared/utils';
import { ExternalMessageAttachmentDto } from '../dto/external-message-attachment.dto';
import { ExternalMessageWithAttachmentsDto } from '../dto/external-message-with-attachments.dto';

/**
 * Service for managing external conversation messages (website visitors)
 * Handles CRUD operations, editing, replies, and attachments for external messages
 */
@Injectable()
export class ExternalMessageService {
  private readonly logger = new Logger(ExternalMessageService.name);

  constructor(
    @InjectRepository(ExternalConversationMessage)
    private readonly messageRepository: Repository<ExternalConversationMessage>,
    @InjectRepository(ExternalConversationMessageAttachment)
    private readonly attachmentRepository: Repository<ExternalConversationMessageAttachment>,
    @InjectRepository(Media)
    private readonly mediaRepository: Repository<Media>,
    @InjectRepository(KnowledgeFile)
    private readonly knowledgeFileRepository: Repository<KnowledgeFile>,
    private readonly cdnService: CdnService,
  ) {}

  /**
   * Main entry point for message persistence
   * Handles only new message creation (editing not supported for website visitors)
   */
  @Transactional()
  async persistMessage(
    messageRequest: ExternalMessageRequestDto,
    threadId: string,
    websitePayload: PayloadLiveChatKey,
  ): Promise<{
    messageId?: string;
    runId: string;
    isModification: boolean;
    deletedMessageIds: string[];
  }> {
    this.logger.debug('Starting external message persistence', {
      threadId,
    });

    // Always create new message - no editing support for external messages
    const messageId = await this.createNewMessage(
      messageRequest,
      threadId,
      websitePayload,
    );

    // Generate runId using crypto (no database entity creation)
    const runId = crypto.randomUUID();

    this.logger.debug('External message persistence completed', {
      messageId,
      runId,
      threadId,
    });

    return {
      messageId,
      runId,
      isModification: false, // Always false for external messages
      deletedMessageIds: [], // Always empty for external messages
    };
  }

  /**
   * Create a new external message in the database
   */
  async createNewMessage(
    messageRequest: ExternalMessageRequestDto,
    externalCustomerPlatformDataId: string,
    websitePayload: PayloadLiveChatKey,
  ): Promise<string> {
    this.logger.debug('Creating new external message', {
      externalCustomerPlatformDataId,
    });

    // Create message entity
    const message = this.messageRepository.create({
      text: messageRequest.contentBlocks.text || '',
      externalCustomerPlatformDataId,
      role: MessageRole.USER,
      hasAttachments:
        messageRequest.contentBlocks.type === MessageContentType.ATTACHMENT,
      platform: Platform.WEBSITE,
      processed: false, // Explicitly set to false for worker processing
      createdAt: `${Date.now()}`,
      replyingToMessageId: messageRequest.replyToMessageId,
    });

    // Save to database
    const savedMessage = await this.messageRepository.save(message);

    // Create attachment records if message has file/image content blocks
    if (messageRequest.contentBlocks.type === MessageContentType.ATTACHMENT) {
      await this.createAttachmentRecords(
        messageRequest.contentBlocks.attachments as AttachmentContentDto[],
        savedMessage.id,
        externalCustomerPlatformDataId,
        websitePayload.userId,
      );
    }

    this.logger.debug('Created new external message with attachments', {
      messageId: savedMessage.id,
      externalCustomerPlatformDataId,
      hasAttachments: savedMessage.hasAttachments,
    });

    return savedMessage.id;
  }

  // Message editing methods removed - not supported for website visitors

  /**
   * Create attachment records for file/image content blocks
   */
  async createAttachmentRecords(
    contentBlocks: AttachmentContentDto[],
    messageId: string,
    externalCustomerPlatformDataId: string,
    userId: number,
  ): Promise<void> {
    const attachmentBlocks = contentBlocks.filter(
      (block) =>
        block.type === AttachmentContentType.IMAGE ||
        block.type === AttachmentContentType.FILE,
    );

    if (attachmentBlocks.length === 0) {
      return; // No attachments to create
    }

    const attachmentRecords = attachmentBlocks.map((block) => {
      return this.attachmentRepository.create({
        externalConversationMessageId: messageId,
        attachmentId: block.fileId, // Extract attachment ID from content block
        mediaType: this.mapContentTypeToAttachmentType(block.type),
        externalCustomerPlatformDataId,
        userId,
      });
    });

    await this.attachmentRepository.save(attachmentRecords);

    this.logger.debug('Created external attachment records', {
      messageId,
      attachmentCount: attachmentRecords.length,
    });
  }

  // Message validation methods removed - editing not supported for website visitors

  /**
   * Get paginated external messages with attachments for a platform data ID
   * Similar to internal message service but without agent avatars
   */
  async getMessages(
    externalCustomerPlatformDataId: string,
    queryDto: QueryMessagesDto,
  ): Promise<PaginatedResult<ExternalMessageWithAttachmentsDto>> {
    const {
      page = 1,
      limit = 20,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
    } = queryDto;
    const skip = (page - 1) * limit;

    this.logger.debug('Getting external messages with attachments', {
      externalCustomerPlatformDataId,
      page,
      limit,
      sortBy,
      sortDirection,
    });

    // Query 1: Get messages using simple repository method (no complex QueryBuilder)
    const [messages, totalItems] = await Promise.all([
      this.messageRepository.find({
        where: {
          externalCustomerPlatformDataId,
          deletedAt: IsNull(),
        },
        order: {
          [sortBy]: sortDirection as 'ASC' | 'DESC',
        },
        take: limit,
        skip,
      }),
      this.messageRepository.count({
        where: {
          externalCustomerPlatformDataId,
          deletedAt: IsNull(),
        },
      }),
    ]);

    // Extract message IDs for messages that have attachments
    const messageIdsWithAttachments = messages
      .filter((msg) => msg.hasAttachments)
      .map((msg) => msg.id);

    // Query 2: Get attachments for messages that have them using direct table lookups
    const attachmentsMap: Map<string, ExternalMessageAttachmentDto[]> = new Map();

    if (messageIdsWithAttachments.length > 0) {
      // Get attachment records
      const attachments = await this.attachmentRepository.find({
        where: {
          externalConversationMessageId: In(messageIdsWithAttachments),
        },
      });

      // Extract unique attachment IDs by type
      const imageAttachmentIds = attachments
        .filter(
          (att) => att.mediaType === ConversationThreadsAttachmentType.IMAGE,
        )
        .map((att) => att.attachmentId);

      const fileAttachmentIds = attachments
        .filter(
          (att) =>
            att.mediaType === ConversationThreadsAttachmentType.KNOWLEDGE_FILE,
        )
        .map((att) => att.attachmentId);

      // Get media data and knowledge files in parallel
      const [mediaData, knowledgeFiles] = await Promise.all([
        imageAttachmentIds.length > 0
          ? this.mediaRepository.findBy({ id: In(imageAttachmentIds) })
          : [] as Media[],
        fileAttachmentIds.length > 0
          ? this.knowledgeFileRepository.findBy({ id: In(fileAttachmentIds) })
          : [] as KnowledgeFile[],
      ]);

      // Create lookup maps
      const mediaMap = {};
      const knowledgeMap = {};
      mediaData.forEach((media) => {
        mediaMap[media.id] = media;
      });
      knowledgeFiles.forEach((file) => {
        knowledgeMap[file.id] = file;
      });

      // Group attachments by message ID
      attachments.forEach((att) => {
        const messageId = att.externalConversationMessageId;
        if (!attachmentsMap.has(messageId)) {
          attachmentsMap.set(messageId, []);
        }

        // Get storage key and name from appropriate source
        let storageKey: string = '';
        let name: string = 'Attachment';

        if (att.mediaType === ConversationThreadsAttachmentType.IMAGE) {
          const media = mediaMap[att.attachmentId];
          storageKey = media?.storageKey || '';
          name = media?.name || 'Image';
        } else if (
          att.mediaType === ConversationThreadsAttachmentType.KNOWLEDGE_FILE
        ) {
          const knowledge = knowledgeMap[att.attachmentId];
          storageKey = knowledge?.storageKey || '';
          name = knowledge?.name || 'File';
        }

        const viewUrl = storageKey
          ? this.cdnService.generateUrlView(
              storageKey,
              TimeIntervalEnum.FIVE_MINUTES,
            ) || ''
          : '';

        const attachment = new ExternalMessageAttachmentDto({
          attachmentId: att.attachmentId,
          attachmentType: att.mediaType,
          viewUrl,
          name,
        });

        attachmentsMap.get(messageId)!.push(attachment);
      });
    }

    // Build final response DTOs
    const items: ExternalMessageWithAttachmentsDto[] = messages.map((message) => {
      return new ExternalMessageWithAttachmentsDto({
        messageId: message.id,
        messageText: message.text,
        messageCreatedAt: parseInt(message.createdAt),
        hasAttachments: message.hasAttachments,
        role: message.role,
        attachments:
          message.hasAttachments && attachmentsMap.has(message.id)
            ? attachmentsMap.get(message.id)
            : undefined,
        replyMessageId: message.replyingToMessageId,
      });
    });

    this.logger.debug('Retrieved external messages', {
      messageCount: items.length,
      totalItems,
      attachmentsCount: attachmentsMap.size,
    });

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
        hasItems: totalItems > 0,
      },
    };
  }

  /**
   * Map content block type to conversation attachment type
   */
  private mapContentTypeToAttachmentType(
    type: AttachmentContentType,
  ): ConversationThreadsAttachmentType {
    switch (type) {
      case AttachmentContentType.IMAGE:
        return ConversationThreadsAttachmentType.IMAGE;
      case AttachmentContentType.FILE:
        return ConversationThreadsAttachmentType.KNOWLEDGE_FILE;
      default:
        throw new Error(`Unsupported attachment type: ${type}`);
    }
  }
}
