import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ChatServiceNew } from './services/chat-new.service';
import { ConfigService } from '../../config';
import { ConfigType } from '../../config/constants';
import { RedisConfig } from '../../config/interfaces';
import { AgentModule } from '@modules/agent/agent.module';
import { ModelsModule } from '@modules/models/models.module';
import { ToolsModule } from '@modules/tools/tools.module';
import { GoogleApiModule } from '@shared/services/google/google-api.module';

// Import entities directly from modules that don't export TypeOrmModule
import { KnowledgeFile } from '@modules/data/knowledge-files/entities';
import { Media } from '@modules/data/media/entities';

// Import business module for website chat entities
import { BusinessUserModule } from '@modules/business/user/business-user.module';

import {
  InternalConversationThread,
  InternalConversationMessage,
  InternalConversationMessagesAttachment,
} from './entities';
import { InternalThreadService, InternalMessageService } from './services';
import { WebsiteChatService } from './services/website-chat.service';
import { UserChatController, EmployeeChatController, WebsiteChatController, WebsitePlatformController, GooglePickerController } from './controllers';
import { GooglePickerService } from './services/google-picker.service';
import { MessageValidationService } from './services/message-validation.service';
import { ContentValidationService } from './services/content-validation.service';
import { ChatAgentValidationService } from './services/chat-agent-validation.service';
import { ExternalMessageService } from './services/external-message.service';
import { WebsiteVisitorService } from './services/website-visitor.service';
import { WebsiteWidgetConfigService } from './services/website-widget-config.service';
import { UserModule } from '@modules/user/user.module';

@Module({
  imports: [
    // Import modules that export TypeOrmModule with their entities
    AgentModule,           // Provides all agent entities
    ModelsModule,          // Provides model configuration entities
    ToolsModule,           // Provides tool integration entities
    GoogleApiModule,       // Provides Google services
    UserModule,            // Provides user entities
    BusinessUserModule,    // Provides business entities including external conversation entities

    // Register entities from modules that don't export TypeOrmModule + local entities
    TypeOrmModule.forFeature([
      // Knowledge file and media entities (modules don't export TypeOrmModule)
      KnowledgeFile,
      Media,

      // Internal conversation management entities
      InternalConversationThread,
      InternalConversationMessage,
      InternalConversationMessagesAttachment,
    ]),
    ClientsModule.registerAsync([
      {
        name: 'REDIS_CLIENT',
        useFactory: (configService: ConfigService) => {
          const redisConfig = configService.getConfig<RedisConfig>(ConfigType.Redis);

          // Parse Redis URL to extract host, port, and other options
          const url = new URL(redisConfig.url);

          return {
            transport: Transport.REDIS,
            options: {
              host: url.hostname,
              port: parseInt(url.port) || 6379,
              password: redisConfig.password || url.password,
              db: parseInt(url.pathname.slice(1)) || 0, // Extract DB from URL path
              retryDelayOnFailover: 100,
              maxRetriesPerRequest: 3,
            },
          };
        },
        inject: [ConfigService],
      },
    ]),
  ],
  controllers: [
    UserChatController,
    EmployeeChatController,
    WebsiteChatController,
    WebsitePlatformController,
    GooglePickerController,
  ],
  providers: [
    ChatServiceNew,
    WebsiteChatService,
    InternalThreadService,
    InternalMessageService,
    // Google Picker Service
    GooglePickerService,
    // Phase 1 Validation Services
    MessageValidationService,
    ContentValidationService,
    ChatAgentValidationService,
    // Website chat support services
    ExternalMessageService,
    WebsiteVisitorService,
    WebsiteWidgetConfigService,
  ],
  exports: [
    ChatServiceNew,
    WebsiteChatService,
    InternalThreadService,
    InternalMessageService,
    GooglePickerService,
  ],
})
export class ChatModule {}
