import { Injectable } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { WORKFLOW_ERROR_CODES } from '../exceptions';
import { WORKFLOW_ADMIN_ERROR_CODES } from '../exceptions/workflow.exception';
import { IWorkflowSettings } from '../interfaces';

/**
 * ValidationHelper cho Workflow module
 * Xử lý các validation logic đặc thù cho business workflow
 */
@Injectable()
export class WorkflowValidationHelper {
  /**
   * Validate ownership của workflow cho user
   * @param workflow Workflow entity
   * @param userId ID của user hiện tại
   * @throws AppException nếu không có quyền
   */
  validateUserOwnership(workflow: any, userId: number): void {
    if (!workflow) {
      throw new AppException(
        WORKFLOW_ERROR_CODES.WORKFLOW_NOT_FOUND,
        'Workflow không tồn tại'
      );
    }

    if (workflow.userId !== userId || workflow.employeeId !== null) {
      throw new AppException(
        WORKFLOW_ERROR_CODES.WORKFLOW_OWNERSHIP_ERROR,
        'Workflow không thuộc về bạn'
      );
    }
  }

  /**
   * Validate ownership của workflow cho admin
   * @param workflow Workflow entity
   * @param employeeId ID của employee hiện tại
   * @throws AppException nếu không có quyền
   */
  validateAdminOwnership(workflow: any, employeeId: number): void {
    if (!workflow) {
      throw new AppException(
        WORKFLOW_ADMIN_ERROR_CODES.WORKFLOW_NOT_FOUND,
        'Workflow không tồn tại'
      );
    }

    if (workflow.employeeId !== employeeId || workflow.userId !== null) {
      throw new AppException(
        WORKFLOW_ADMIN_ERROR_CODES.ADMIN_WORKFLOW_EDIT_PERMISSION_DENIED,
        'Admin không có quyền chỉnh sửa workflow này'
      );
    }
  }

  /**
   * Validate admin có thể xem workflow (không cần ownership)
   * @param workflow Workflow entity
   * @throws AppException nếu workflow không tồn tại
   */
  validateAdminViewAccess(workflow: any): void {
    if (!workflow) {
      throw new AppException(
        WORKFLOW_ADMIN_ERROR_CODES.WORKFLOW_NOT_FOUND,
        'Workflow không tồn tại'
      );
    }
  }

  /**
   * Validate workflow name
   * @param name Tên workflow
   * @throws AppException nếu name không hợp lệ
   */
  validateWorkflowName(name: string): void {
    if (!name || typeof name !== 'string') {
      throw new AppException(
        WORKFLOW_ERROR_CODES.WORKFLOW_INVALID_NAME,
        'Tên workflow không được để trống'
      );
    }

    if (name.trim().length === 0) {
      throw new AppException(
        WORKFLOW_ERROR_CODES.WORKFLOW_INVALID_NAME,
        'Tên workflow không được để trống'
      );
    }

    if (name.length > 255) {
      throw new AppException(
        WORKFLOW_ERROR_CODES.WORKFLOW_INVALID_NAME,
        'Tên workflow không được vượt quá 255 ký tự'
      );
    }
  }

  /**
   * Validate workflow settings
   * @param settings Settings của workflow
   * @throws AppException nếu settings không hợp lệ
   */
  validateWorkflowSettings(settings: IWorkflowSettings | null): void {
    if (settings === null || settings === undefined) {
      return; // Settings có thể null
    }

    if (typeof settings !== 'object') {
      throw new AppException(
        WORKFLOW_ERROR_CODES.WORKFLOW_INVALID_SETTINGS,
        'Settings phải là object'
      );
    }

    // Validate notification_type nếu có
    if (settings.notification_type && 
        !['email', 'webhook', 'none'].includes(settings.notification_type)) {
      throw new AppException(
        WORKFLOW_ERROR_CODES.WORKFLOW_INVALID_SETTINGS,
        'notification_type phải là email, webhook hoặc none'
      );
    }

    // Validate timeout nếu có
    if (settings.timeout !== undefined) {
      if (typeof settings.timeout !== 'number' || settings.timeout < 0) {
        throw new AppException(
          WORKFLOW_ERROR_CODES.WORKFLOW_INVALID_SETTINGS,
          'timeout phải là số dương'
        );
      }
    }

    // Validate retry_policy nếu có
    if (settings.retry_policy) {
      const { enabled, max_retries, delay } = settings.retry_policy;
      
      if (typeof enabled !== 'boolean') {
        throw new AppException(
          WORKFLOW_ERROR_CODES.WORKFLOW_INVALID_SETTINGS,
          'retry_policy.enabled phải là boolean'
        );
      }

      if (typeof max_retries !== 'number' || max_retries < 0) {
        throw new AppException(
          WORKFLOW_ERROR_CODES.WORKFLOW_INVALID_SETTINGS,
          'retry_policy.max_retries phải là số dương'
        );
      }

      if (typeof delay !== 'number' || delay < 0) {
        throw new AppException(
          WORKFLOW_ERROR_CODES.WORKFLOW_INVALID_SETTINGS,
          'retry_policy.delay phải là số dương'
        );
      }
    }
  }

  /**
   * Validate danh sách IDs cho bulk operations
   * @param ids Danh sách IDs
   * @throws AppException nếu IDs không hợp lệ
   */
  validateWorkflowIds(ids: string[]): void {
    if (!Array.isArray(ids)) {
      throw new AppException(
        WORKFLOW_ERROR_CODES.WORKFLOW_INVALID_INPUT,
        'IDs phải là mảng'
      );
    }

    if (ids.length === 0) {
      throw new AppException(
        WORKFLOW_ERROR_CODES.WORKFLOW_EMPTY_IDS_LIST,
        'Danh sách IDs không được để trống'
      );
    }

    // Validate từng ID
    for (const id of ids) {
      if (!id || typeof id !== 'string') {
        throw new AppException(
          WORKFLOW_ERROR_CODES.WORKFLOW_INVALID_ID,
          'ID workflow phải là string không rỗng'
        );
      }

      // Validate UUID format (basic check)
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(id)) {
        throw new AppException(
          WORKFLOW_ERROR_CODES.WORKFLOW_INVALID_ID,
          `ID workflow không đúng định dạng UUID: ${id}`
        );
      }
    }

    // Check for duplicates
    const uniqueIds = new Set(ids);
    if (uniqueIds.size !== ids.length) {
      throw new AppException(
        WORKFLOW_ERROR_CODES.WORKFLOW_INVALID_INPUT,
        'Danh sách IDs không được chứa phần tử trùng lặp'
      );
    }
  }

  /**
   * Validate user ID
   * @param userId ID của user
   * @throws AppException nếu userId không hợp lệ
   */
  validateUserId(userId: number): void {
    if (!userId || typeof userId !== 'number' || userId <= 0) {
      throw new AppException(
        WORKFLOW_ERROR_CODES.WORKFLOW_INVALID_INPUT,
        'User ID phải là số dương'
      );
    }
  }

  /**
   * Validate employee ID
   * @param employeeId ID của employee
   * @throws AppException nếu employeeId không hợp lệ
   */
  validateEmployeeId(employeeId: number): void {
    if (!employeeId || typeof employeeId !== 'number' || employeeId <= 0) {
      throw new AppException(
        WORKFLOW_ADMIN_ERROR_CODES.ADMIN_WORKFLOW_INVALID_EMPLOYEE_ID,
        'Employee ID phải là số dương'
      );
    }
  }

  /**
   * Validate create workflow data cho user
   * @param data Dữ liệu tạo workflow
   * @param userId ID của user
   * @throws AppException nếu data không hợp lệ
   */
  validateCreateWorkflowData(data: any, userId: number): void {
    this.validateUserId(userId);
    this.validateWorkflowName(data.name);
    this.validateWorkflowSettings(data.settings);

    // Đảm bảo không có employeeId trong data
    if (data.employeeId !== undefined) {
      throw new AppException(
        WORKFLOW_ERROR_CODES.WORKFLOW_INVALID_INPUT,
        'User không thể tạo workflow với employeeId'
      );
    }
  }

  /**
   * Validate create workflow data cho admin
   * @param data Dữ liệu tạo workflow
   * @param employeeId ID của employee
   * @throws AppException nếu data không hợp lệ
   */
  validateCreateAdminWorkflowData(data: any, employeeId: number): void {
    this.validateEmployeeId(employeeId);
    this.validateWorkflowName(data.name);
    this.validateWorkflowSettings(data.settings);

    // Đảm bảo không có userId trong data
    if (data.userId !== undefined) {
      throw new AppException(
        WORKFLOW_ADMIN_ERROR_CODES.ADMIN_WORKFLOW_INVALID_USER_ASSIGNMENT,
        'Admin không thể tạo workflow với userId'
      );
    }
  }

  /**
   * Validate update workflow data
   * @param data Dữ liệu cập nhật workflow
   * @throws AppException nếu data không hợp lệ
   */
  validateUpdateWorkflowData(data: any): void {
    if (data.name !== undefined) {
      this.validateWorkflowName(data.name);
    }

    if (data.settings !== undefined) {
      this.validateWorkflowSettings(data.settings);
    }

    // Không cho phép thay đổi ownership
    if (data.userId !== undefined || data.employeeId !== undefined) {
      throw new AppException(
        WORKFLOW_ERROR_CODES.WORKFLOW_INVALID_INPUT,
        'Không thể thay đổi ownership của workflow'
      );
    }
  }

  /**
   * Validate pagination parameters
   * @param page Số trang
   * @param limit Số lượng items per page
   * @throws AppException nếu parameters không hợp lệ
   */
  validatePaginationParams(page?: number, limit?: number): void {
    if (page !== undefined) {
      if (typeof page !== 'number' || page < 1) {
        throw new AppException(
          WORKFLOW_ERROR_CODES.WORKFLOW_INVALID_INPUT,
          'Page phải là số dương'
        );
      }
    }

    if (limit !== undefined) {
      if (typeof limit !== 'number' || limit < 1 || limit > 100) {
        throw new AppException(
          WORKFLOW_ERROR_CODES.WORKFLOW_INVALID_INPUT,
          'Limit phải là số từ 1 đến 100'
        );
      }
    }
  }
}
