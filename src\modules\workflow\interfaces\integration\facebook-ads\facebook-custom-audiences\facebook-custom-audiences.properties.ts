/**
 * @file Facebook Custom Audiences Node Properties
 * 
 * Đ<PERSON>nh nghĩa node properties cho Facebook Custom Audiences integration
 * Theo patterns từ Make.com chuẩn
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

import {
    EPropertyType,
    INodeProperty
} from '../../../node-manager.interface';

import {
    EFacebookCustomAudiencesOperation,
    EFacebookCustomAudienceSubtype,
    EFacebookLookalikeAudienceType
} from './facebook-custom-audiences.types';

// =================================================================
// FACEBOOK CUSTOM AUDIENCES NODE PROPERTIES
// =================================================================

/**
 * Facebook Custom Audiences node properties definition
 */
export const FACEBOOK_CUSTOM_AUDIENCES_PROPERTIES: INodeProperty[] = [
    // Operation Selection
    {
        name: 'operation',
        displayName: 'Operation',
        type: EPropertyType.Options,
        required: true,
        default: EFacebookCustomAudiencesOperation.CREATE_CUSTOM_AUDIENCE,
        description: 'Chọn thao tác cần thực hiện',
        options: [
            // Custom Audiences Operations - Các thao tác đối tượng tùy chỉnh
            { name: 'Create a Custom Audience', value: EFacebookCustomAudiencesOperation.CREATE_CUSTOM_AUDIENCE },
            { name: 'Add Emails to a Custom Audience', value: EFacebookCustomAudiencesOperation.ADD_EMAILS_TO_CUSTOM_AUDIENCE },
            { name: 'Add Users to a Custom Audience', value: EFacebookCustomAudiencesOperation.ADD_USERS_TO_CUSTOM_AUDIENCE },
            { name: 'Remove Audience Members', value: EFacebookCustomAudiencesOperation.REMOVE_AUDIENCE_MEMBERS },
            
            // Lookalike Audiences Operations - Các thao tác đối tượng tương tự
            { name: 'Create a Lookalike Audience', value: EFacebookCustomAudiencesOperation.CREATE_LOOKALIKE_AUDIENCE },
            
            // Page Fan Lookalike Audiences - Đối tượng tương tự fan trang
            { name: 'Create a Page Fan Lookalike Audience', value: EFacebookCustomAudiencesOperation.CREATE_PAGE_FAN_LOOKALIKE_AUDIENCE },
            
            // Campaign and Ad Set Lookalike Audience - Đối tượng tương tự chiến dịch
            { name: 'Create a Campaign or Ad Set Conversion Lookalikes', value: EFacebookCustomAudiencesOperation.CREATE_CAMPAIGN_CONVERSION_LOOKALIKES },
            
            // Value-Based Custom Audience - Đối tượng tùy chỉnh dựa trên giá trị
            { name: 'Create a Value-Based Custom Audience', value: EFacebookCustomAudiencesOperation.CREATE_VALUE_BASED_CUSTOM_AUDIENCE },
            { name: 'Populate a Seed Audience', value: EFacebookCustomAudiencesOperation.POPULATE_SEED_AUDIENCE },
            { name: 'Create a Value-Based Lookalike', value: EFacebookCustomAudiencesOperation.CREATE_VALUE_BASED_LOOKALIKE }
        ]
    },

    // Business Manager (Required for most operations)
    {
        name: 'business_manager',
        displayName: 'Business Manager',
        type: EPropertyType.String,
        required: true,
        description: 'Business Manager ID',
        displayOptions: {
            show: {
                operation: [
                    EFacebookCustomAudiencesOperation.CREATE_CUSTOM_AUDIENCE,
                    EFacebookCustomAudiencesOperation.ADD_EMAILS_TO_CUSTOM_AUDIENCE,
                    EFacebookCustomAudiencesOperation.ADD_USERS_TO_CUSTOM_AUDIENCE,
                    EFacebookCustomAudiencesOperation.REMOVE_AUDIENCE_MEMBERS,
                    EFacebookCustomAudiencesOperation.CREATE_LOOKALIKE_AUDIENCE,
                    EFacebookCustomAudiencesOperation.CREATE_PAGE_FAN_LOOKALIKE_AUDIENCE,
                    EFacebookCustomAudiencesOperation.CREATE_CAMPAIGN_CONVERSION_LOOKALIKES,
                    EFacebookCustomAudiencesOperation.CREATE_VALUE_BASED_CUSTOM_AUDIENCE,
                    EFacebookCustomAudiencesOperation.POPULATE_SEED_AUDIENCE,
                    EFacebookCustomAudiencesOperation.CREATE_VALUE_BASED_LOOKALIKE
                ]
            }
        }
    },

    // Name (Required for Create Custom Audience, Create Lookalike Audience, Create Value-Based Custom Audience, and Create Value-Based Lookalike)
    {
        name: 'name',
        displayName: 'Name',
        type: EPropertyType.String,
        required: true,
        description: 'Tên của audience',
        displayOptions: {
            show: {
                operation: [
                    EFacebookCustomAudiencesOperation.CREATE_CUSTOM_AUDIENCE,
                    EFacebookCustomAudiencesOperation.CREATE_LOOKALIKE_AUDIENCE,
                    EFacebookCustomAudiencesOperation.CREATE_VALUE_BASED_CUSTOM_AUDIENCE,
                    EFacebookCustomAudiencesOperation.CREATE_VALUE_BASED_LOOKALIKE
                ]
            }
        }
    },

    // Description (Optional for Create Custom Audience)
    {
        name: 'description',
        displayName: 'Description',
        type: EPropertyType.String,
        description: 'Mô tả cho custom audience',
        displayOptions: {
            show: {
                operation: [
                    EFacebookCustomAudiencesOperation.CREATE_CUSTOM_AUDIENCE
                ]
            }
        }
    },

    // Emails (Required for Add Emails to Custom Audience)
    {
        name: 'emails',
        displayName: 'Emails',
        type: EPropertyType.Array,
        required: true,
        description: 'Danh sách email cần thêm vào custom audience',
        default: {},
        properties: [
            {
                name: 'email',
                displayName: 'Email',
                type: EPropertyType.String,
                required: true,
                description: 'Địa chỉ email'
            }
        ],
        displayOptions: {
            show: {
                operation: [
                    EFacebookCustomAudiencesOperation.ADD_EMAILS_TO_CUSTOM_AUDIENCE
                ]
            }
        }
    },

    // Data (Required for Add Users to Custom Audience)
    {
        name: 'data',
        displayName: 'Data',
        type: EPropertyType.Array,
        required: true,
        description: 'Danh sách dữ liệu người dùng cần thêm vào custom audience',
        default: {},
        properties: [
            {
                name: 'email',
                displayName: 'Email',
                type: EPropertyType.String,
                description: 'Địa chỉ email'
            },
            {
                name: 'phone',
                displayName: 'Phone',
                type: EPropertyType.String,
                description: 'Số điện thoại (loại bỏ ký hiệu, chữ cái, số 0 đầu. Thêm mã quốc gia nếu không có trường COUNTRY)'
            },
            {
                name: 'gender',
                displayName: 'Gender',
                type: EPropertyType.String,
                description: 'Giới tính'
            },
            {
                name: 'birth_year',
                displayName: 'Birth year',
                type: EPropertyType.String,
                description: 'Năm sinh (định dạng YYYY từ 1900 đến năm hiện tại)'
            },
            {
                name: 'birth_month',
                displayName: 'Birth month',
                type: EPropertyType.String,
                description: 'Tháng sinh (định dạng MM: 01-12)'
            },
            {
                name: 'birth_day',
                displayName: 'Birth day',
                type: EPropertyType.String,
                description: 'Ngày sinh (định dạng DD: 01-31)'
            },
            {
                name: 'first_name',
                displayName: 'First name',
                type: EPropertyType.String,
                description: 'Tên (chỉ a-z, không dấu câu, ký tự đặc biệt UTF8)'
            },
            {
                name: 'last_name',
                displayName: 'Last name',
                type: EPropertyType.String,
                description: 'Họ (chỉ a-z, không dấu câu, ký tự đặc biệt UTF8)'
            },
            {
                name: 'first_name_initial',
                displayName: 'First name initial',
                type: EPropertyType.String,
                description: 'Chữ cái đầu tên (chỉ a-z, ký tự đặc biệt UTF8)'
            },
            {
                name: 'state',
                displayName: 'State',
                type: EPropertyType.String,
                description: 'Bang/Tỉnh (mã ANSI 2 ký tự. Chuẩn hóa các bang ngoài US, không dấu câu, không ký tự đặc biệt)'
            },
            {
                name: 'city',
                displayName: 'City',
                type: EPropertyType.String,
                description: 'Thành phố (chỉ a-z, không dấu câu, không ký tự đặc biệt)'
            },
            {
                name: 'zip',
                displayName: 'ZIP',
                type: EPropertyType.String,
                description: 'Mã bưu điện (chỉ 5 số đầu cho US. Định dạng Area/District/Sector cho UK)'
            },
            {
                name: 'country',
                displayName: 'Country',
                type: EPropertyType.String,
                description: 'Quốc gia (mã 2 chữ cái ISO 3166-1 alpha-2)'
            },
            {
                name: 'mobile_advertiser_id',
                displayName: 'Mobile advertiser ID',
                type: EPropertyType.String,
                description: 'ID quảng cáo di động (tất cả lowercase, giữ dấu gạch ngang)'
            },
            {
                name: 'external_id',
                displayName: 'External ID',
                type: EPropertyType.String,
                description: 'ID bên ngoài'
            },
            {
                name: 'lookalike_value',
                displayName: 'Lookalike Value',
                type: EPropertyType.String,
                description: 'Giá trị lookalike'
            },
            {
                name: 'page_scoped_user_id',
                displayName: 'Page Scoped User ID',
                type: EPropertyType.String,
                description: 'ID người dùng trong phạm vi trang'
            },
            {
                name: 'page_id',
                displayName: 'Page ID',
                type: EPropertyType.String,
                description: 'ID trang (bắt buộc nếu có Page Scoped User ID)'
            }
        ],
        displayOptions: {
            show: {
                operation: [
                    EFacebookCustomAudiencesOperation.ADD_USERS_TO_CUSTOM_AUDIENCE
                ]
            }
        }
    },

    // Remove members by (Required for Remove Audience Members)
    {
        name: 'remove_members_by',
        displayName: 'Remove members by',
        type: EPropertyType.String,
        required: true,
        description: 'Cách thức xóa thành viên khỏi custom audience (email hoặc external ID)',
        displayOptions: {
            show: {
                operation: [
                    EFacebookCustomAudiencesOperation.REMOVE_AUDIENCE_MEMBERS
                ]
            }
        }
    },

    // Lookalike specification (Required for Create Lookalike Audience)
    {
        name: 'lookalike_specification',
        displayName: 'Lookalike specification',
        type: EPropertyType.Collection,
        required: true,
        description: 'Thông số cho lookalike audience',
        default: {},
        properties: [
            {
                name: 'source',
                displayName: 'Source',
                type: EPropertyType.Collection,
                required: true,
                description: 'Nguồn audience để tạo lookalike',
                default: {},
                properties: [
                    {
                        name: 'type',
                        displayName: 'Type',
                        type: EPropertyType.Options,
                        required: true,
                        description: 'Loại nguồn',
                        options: [
                            { name: 'Ratio', value: 'ratio' }
                        ],
                        default: 'ratio'
                    },
                    {
                        name: 'starting_ratio',
                        displayName: 'Starting ratio',
                        type: EPropertyType.Number,
                        description: 'Tỷ lệ bắt đầu cho lookalike. Ví dụ: starting ratio 0.01 và ratio 0.02 tạo lookalike từ 1% đến 2% của segment lookalike. Starting ratio phải nhỏ hơn ratio.',
                        minValue: 0.01,
                        maxValue: 0.20
                    },
                    {
                        name: 'ratio',
                        displayName: 'Ratio',
                        type: EPropertyType.Number,
                        required: true,
                        description: '0.01-0.20 incremented by 0.01. Top x% của audience gốc trong quốc gia được chọn.',
                        minValue: 0.01,
                        maxValue: 0.20
                    }
                ]
            },
            {
                name: 'allow_international_seeds',
                displayName: 'Allow international seeds',
                type: EPropertyType.Options,
                description: 'Ít nhất 100 thành viên seed audience từ một quốc gia. Nếu không, cho phép seeds quốc tế = true có nghĩa là Facebook tìm số lượng tối thiểu này ở quốc gia khác. Mặc định false.',
                options: [
                    { name: 'Yes', value: 'Yes' },
                    { name: 'No', value: 'No' },
                    { name: 'Empty', value: 'Empty' }
                ],
                default: 'Empty'
            },
            {
                name: 'location',
                displayName: 'Location',
                type: EPropertyType.Collection,
                required: true,
                description: 'Thông số vị trí cho lookalike audience',
                default: {},
                properties: [
                    {
                        name: 'type',
                        displayName: 'Type',
                        type: EPropertyType.Options,
                        required: true,
                        description: 'Loại location',
                        options: [
                            { name: 'Simple Country', value: 'simple_country' },
                            { name: 'Location Specification', value: 'location_specification' }
                        ],
                        default: 'simple_country'
                    },
                    {
                        name: 'country',
                        displayName: 'Country',
                        type: EPropertyType.String,
                        required: true,
                        description: 'Quốc gia (chỉ cho simple country)',
                        displayOptions: {
                            show: {
                                type: ['simple_country']
                            }
                        }
                    },
                    {
                        name: 'location_specification',
                        displayName: 'Location specification',
                        type: EPropertyType.Collection,
                        required: true,
                        description: 'Thông số vị trí chi tiết (chỉ cho location specification)',
                        default: {},
                        displayOptions: {
                            show: {
                                type: ['location_specification']
                            }
                        },
                        properties: [
                            {
                                name: 'geo_locations',
                                displayName: 'Geo locations',
                                type: EPropertyType.Collection,
                                required: true,
                                description: 'Vị trí địa lý',
                                default: {},
                                properties: [
                                    {
                                        name: 'countries',
                                        displayName: 'Countries',
                                        type: EPropertyType.Array,
                                        description: 'Danh sách quốc gia',
                                        default: {},
                                        properties: [
                                            {
                                                name: 'country',
                                                displayName: 'Country',
                                                type: EPropertyType.String,
                                                required: true,
                                                description: 'Mã quốc gia'
                                            }
                                        ]
                                    },
                                    {
                                        name: 'country_groups',
                                        displayName: 'Country groups',
                                        type: EPropertyType.Array,
                                        description: 'Nhóm quốc gia',
                                        default: {},
                                        properties: [
                                            {
                                                name: 'country_group',
                                                displayName: 'Country group',
                                                type: EPropertyType.String,
                                                required: true,
                                                description: 'Mã nhóm quốc gia'
                                            }
                                        ]
                                    }
                                ]
                            },
                            {
                                name: 'excluded_geo_locations',
                                displayName: 'Excluded geo locations',
                                type: EPropertyType.Collection,
                                description: 'Vị trí địa lý loại trừ',
                                default: {},
                                properties: [
                                    {
                                        name: 'countries',
                                        displayName: 'Countries',
                                        type: EPropertyType.Array,
                                        description: 'Danh sách quốc gia loại trừ',
                                        default: {},
                                        properties: [
                                            {
                                                name: 'country',
                                                displayName: 'Country',
                                                type: EPropertyType.String,
                                                required: true,
                                                description: 'Mã quốc gia'
                                            }
                                        ]
                                    },
                                    {
                                        name: 'country_groups',
                                        displayName: 'Country groups',
                                        type: EPropertyType.Array,
                                        description: 'Nhóm quốc gia loại trừ',
                                        default: {},
                                        properties: [
                                            {
                                                name: 'country_group',
                                                displayName: 'Country group',
                                                type: EPropertyType.String,
                                                required: true,
                                                description: 'Mã nhóm quốc gia'
                                            }
                                        ]
                                    }
                                ]
                            }
                        ]
                    }
                ]
            }
        ],
        displayOptions: {
            show: {
                operation: [
                    EFacebookCustomAudiencesOperation.CREATE_LOOKALIKE_AUDIENCE
                ]
            }
        }
    },

    // Page Fan Lookalike specification (Required for Create Page Fan Lookalike Audience)
    {
        name: 'lookalike_specification',
        displayName: 'Lookalike specification',
        type: EPropertyType.Collection,
        required: true,
        description: 'Thông số cho page fan lookalike audience',
        default: {},
        properties: [
            {
                name: 'page',
                displayName: 'Page',
                type: EPropertyType.String,
                required: true,
                description: 'Page ID để tạo lookalike audience dựa trên fan của trang'
            },
            {
                name: 'country',
                displayName: 'Country',
                type: EPropertyType.String,
                required: true,
                description: 'Quốc gia cho lookalike audience'
            },
            {
                name: 'allow_international_seeds',
                displayName: 'Allow international seeds',
                type: EPropertyType.Options,
                description: 'Cần ít nhất 100 thành viên seed audience từ một quốc gia. Nếu không đạt tối thiểu này, Allow international seeds = true có nghĩa là Facebook tìm số lượng tối thiểu này ở quốc gia khác. Mặc định false.',
                options: [
                    { name: 'Yes', value: 'Yes' },
                    { name: 'No', value: 'No' },
                    { name: 'Empty', value: 'Empty' }
                ],
                default: 'Empty'
            },
            {
                name: 'starting_ratio',
                displayName: 'Starting ratio',
                type: EPropertyType.Number,
                description: 'Tỷ lệ phần trăm bắt đầu của lookalike. Ví dụ: starting_ratio 0.01 và ratio 0.02 sẽ tạo lookalike từ 1% đến 2% của segment lookalike. Giá trị Starting ratio phải luôn nhỏ hơn ratio.',
                minValue: 0.01,
                maxValue: 0.20
            },
            {
                name: 'ratio',
                displayName: 'Ratio',
                type: EPropertyType.Number,
                required: true,
                description: '0.01-0.20 incremented by 0.01. Top x% của audience gốc trong quốc gia được chọn.',
                minValue: 0.01,
                maxValue: 0.20
            }
        ],
        displayOptions: {
            show: {
                operation: [
                    EFacebookCustomAudiencesOperation.CREATE_PAGE_FAN_LOOKALIKE_AUDIENCE
                ]
            }
        }
    },

    // Campaign Conversion Lookalike specification (Required for Create Campaign or Ad Set Conversion Lookalikes)
    {
        name: 'lookalike_specification',
        displayName: 'Lookalike specification',
        type: EPropertyType.Collection,
        required: true,
        description: 'Thông số cho campaign/ad set conversion lookalike audience',
        default: {},
        properties: [
            {
                name: 'country',
                displayName: 'Country',
                type: EPropertyType.String,
                required: true,
                description: 'Quốc gia cho lookalike audience. Chỉ ra audience là campaign conversion lookalike.'
            },
            {
                name: 'allow_international_seeds',
                displayName: 'Allow international seeds',
                type: EPropertyType.Options,
                description: 'Cần ít nhất 100 thành viên seed audience từ một quốc gia. Nếu không đạt tối thiểu này, Allow international seeds = true có nghĩa là Facebook tìm số lượng tối thiểu này ở quốc gia khác. Mặc định false.',
                options: [
                    { name: 'Yes', value: 'Yes' },
                    { name: 'No', value: 'No' },
                    { name: 'Empty', value: 'Empty' }
                ],
                default: 'Empty'
            },
            {
                name: 'starting_ratio',
                displayName: 'Starting ratio',
                type: EPropertyType.Number,
                description: 'Tỷ lệ phần trăm bắt đầu cho lookalike. Ví dụ: Starting ratio 0.01 và ratio 0.02 tạo lookalike từ 1% đến 2% của segment lookalike. Starting ratio phải nhỏ hơn ratio.',
                minValue: 0.01,
                maxValue: 0.20
            },
            {
                name: 'ratio',
                displayName: 'Ratio',
                type: EPropertyType.Number,
                required: true,
                description: 'Phạm vi 0.01-0.20. Top x% của audience gốc trong quốc gia được chọn.',
                minValue: 0.01,
                maxValue: 0.20
            }
        ],
        displayOptions: {
            show: {
                operation: [
                    EFacebookCustomAudiencesOperation.CREATE_CAMPAIGN_CONVERSION_LOOKALIKES
                ]
            }
        }
    },

    // Data (Required for Populate Seed Audience)
    {
        name: 'data',
        displayName: 'Data',
        type: EPropertyType.Array,
        required: true,
        description: 'Danh sách dữ liệu seed audience cần populate',
        default: {},
        properties: [
            {
                name: 'lookalike_value',
                displayName: 'Lookalike value',
                type: EPropertyType.String,
                required: true,
                description: 'Giá trị lookalike (bắt buộc)'
            },
            {
                name: 'email',
                displayName: 'Email',
                type: EPropertyType.String,
                description: 'Địa chỉ email'
            },
            {
                name: 'phone',
                displayName: 'Phone',
                type: EPropertyType.String,
                description: 'Số điện thoại. Loại bỏ ký hiệu, chữ cái và số 0 đầu. Bạn nên thêm mã quốc gia nếu không có trường COUNTRY.'
            },
            {
                name: 'gender',
                displayName: 'Gender',
                type: EPropertyType.String,
                description: 'Giới tính'
            },
            {
                name: 'birth_year',
                displayName: 'Birth year',
                type: EPropertyType.String,
                description: 'Năm sinh. Sử dụng định dạng YYYY từ 1900 đến năm hiện tại.'
            },
            {
                name: 'birth_month',
                displayName: 'Birth month',
                type: EPropertyType.String,
                description: 'Tháng sinh. Sử dụng định dạng MM: 01 đến 12.'
            },
            {
                name: 'birth_day',
                displayName: 'Birth day',
                type: EPropertyType.String,
                description: 'Ngày sinh. Sử dụng định dạng DD: 01 đến 31.'
            },
            {
                name: 'first_name',
                displayName: 'First name',
                type: EPropertyType.String,
                description: 'Tên. Chỉ sử dụng a-z. Không dấu câu. Ký tự đặc biệt theo định dạng UTF8.'
            },
            {
                name: 'last_name',
                displayName: 'Last name',
                type: EPropertyType.String,
                description: 'Họ. Chỉ sử dụng a-z. Không dấu câu. Ký tự đặc biệt theo định dạng UTF8.'
            },
            {
                name: 'first_name_initial',
                displayName: 'First name initial',
                type: EPropertyType.String,
                description: 'Chữ cái đầu tên. Chỉ sử dụng a-z. Ký tự đặc biệt theo định dạng UTF8.'
            },
            {
                name: 'state',
                displayName: 'State',
                type: EPropertyType.String,
                description: 'Bang/Tỉnh. Sử dụng mã viết tắt ANSI 2 ký tự. Chuẩn hóa các bang ngoài Mỹ, không dấu câu, không ký tự đặc biệt.'
            },
            {
                name: 'city',
                displayName: 'City',
                type: EPropertyType.String,
                description: 'Thành phố. Chỉ sử dụng a-z. Không dấu câu, không ký tự đặc biệt.'
            },
            {
                name: 'zip',
                displayName: 'ZIP',
                type: EPropertyType.String,
                description: 'Mã bưu điện. Chỉ sử dụng 5 số đầu cho Mỹ. Sử dụng định dạng Area/District/Sector cho Anh.'
            },
            {
                name: 'country',
                displayName: 'Country',
                type: EPropertyType.String,
                description: 'Quốc gia. Sử dụng mã quốc gia 2 chữ cái theo ISO 3166-1 alpha-2.'
            },
            {
                name: 'mobile_advertiser_id',
                displayName: 'Mobile advertiser ID',
                type: EPropertyType.String,
                description: 'ID quảng cáo di động. Sử dụng tất cả chữ thường, giữ dấu gạch ngang.'
            },
            {
                name: 'external_id',
                displayName: 'External ID',
                type: EPropertyType.String,
                description: 'ID bên ngoài'
            }
        ],
        displayOptions: {
            show: {
                operation: [
                    EFacebookCustomAudiencesOperation.POPULATE_SEED_AUDIENCE
                ]
            }
        }
    },

    // Value-Based Lookalike specification (Required for Create Value-Based Lookalike)
    {
        name: 'lookalike_specification',
        displayName: 'Lookalike specification',
        type: EPropertyType.Collection,
        required: true,
        description: 'Thông số cho value-based lookalike audience',
        default: {},
        properties: [
            {
                name: 'ratio',
                displayName: 'Ratio',
                type: EPropertyType.Number,
                required: true,
                description: '0.01-0.20 incremented by 0.01. Top x% của audience gốc trong quốc gia được chọn.',
                minValue: 0.01,
                maxValue: 0.20
            },
            {
                name: 'country',
                displayName: 'Country',
                type: EPropertyType.String,
                required: true,
                description: 'Quốc gia cho value-based lookalike audience'
            }
        ],
        displayOptions: {
            show: {
                operation: [
                    EFacebookCustomAudiencesOperation.CREATE_VALUE_BASED_LOOKALIKE
                ]
            }
        }
    }
];
