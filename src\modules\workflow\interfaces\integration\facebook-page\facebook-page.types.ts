/**
 * @file Facebook Page Types & Enums
 * 
 * Định nghĩa các enums và types cho Facebook Page integration
 * Theo patterns từ Make.com chuẩn
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

// =================================================================
// SECTION 1: FACEBOOK PAGE ENUMS
// =================================================================

/**
 * Facebook Page specific operations (theo Make.com chuẩn)
 */
export enum EFacebookPageOperation {
    // === POSTS OPERATIONS - Các thao tác với bài đăng ===
    /** Watch Posts (Trigger) - Triggers when a new post is added */
    WATCH_POSTS = 'watchPosts',
    /** Watch Posts (public page) (Trigger) - Triggers when a new post is added to selected public page */
    WATCH_POSTS_PUBLIC = 'watchPostsPublic',
    /** List Posts (Action) - Returns posts */
    LIST_POSTS = 'listPosts',
    /** Get a Post (Action) - Returns info about a post */
    GET_POST = 'getPost',
    /** Get Post Reactions (Action) - Returns number of reactions for a post */
    GET_POST_REACTIONS = 'getPostReactions',
    /** Create a Post (Action) - Creates a post */
    CREATE_POST = 'createPost',
    /** Create a Post with Photos (Action) - Creates a multi-photo post */
    CREATE_POST_WITH_PHOTOS = 'createPostWithPhotos',
    /** Update a Post (Action) - Updates a post */
    UPDATE_POST = 'updatePost',
    /** Delete a Post (Action) - Deletes a post */
    DELETE_POST = 'deletePost',
    /** Like a Post (Action) - Likes a post */
    LIKE_POST = 'likePost',
    /** Unlike a Post (Action) - Unlikes a post */
    UNLIKE_POST = 'unlikePost',

    // === VIDEOS OPERATIONS - Các thao tác với video ===
    /** Watch Videos (Trigger) - Triggers when a new video is added */
    WATCH_VIDEOS = 'watchVideos',
    /** List Videos (Action) - Returns videos */
    LIST_VIDEOS = 'listVideos',
    /** Get a Video (Action) - Returns info about a video */
    GET_VIDEO = 'getVideo',
    /** Upload a Video (Action) - Uploads a video */
    UPLOAD_VIDEO = 'uploadVideo',
    /** Update a Video (Action) - Updates a video */
    UPDATE_VIDEO = 'updateVideo',
    /** Delete a Video (Action) - Deletes a video */
    DELETE_VIDEO = 'deleteVideo',

    // === PHOTOS OPERATIONS - Các thao tác với ảnh ===
    /** Watch Photos (Trigger) - Triggers when a new photo is added */
    WATCH_PHOTOS = 'watchPhotos',
    /** List Photos (Action) - Returns photos */
    LIST_PHOTOS = 'listPhotos',
    /** Get a Photo (Action) - Returns info about a photo */
    GET_PHOTO = 'getPhoto',
    /** Upload a Photo (Action) - Uploads a photo */
    UPLOAD_PHOTO = 'uploadPhoto',
    /** Delete a Photo (Action) - Deletes a photo */
    DELETE_PHOTO = 'deletePhoto',

    // === COMMENTS OPERATIONS - Các thao tác với bình luận ===
    /** Watch Comments (Trigger) - Triggers when a new comment is added */
    WATCH_COMMENTS = 'watchComments',
    /** List Comments (Action) - Returns comments for a post */
    LIST_COMMENTS = 'listComments',
    /** Get a Comment (Action) - Returns info about a comment */
    GET_COMMENT = 'getComment',
    /** Create a Comment (Action) - Creates a comment */
    CREATE_COMMENT = 'createComment',
    /** Update a Comment (Action) - Updates a comment */
    UPDATE_COMMENT = 'updateComment',
    /** Delete a Comment (Action) - Deletes a comment */
    DELETE_COMMENT = 'deleteComment',

    // === PAGE OPERATIONS - Các thao tác với trang ===
    /** Get a Page (Action) - Returns details about a page */
    GET_PAGE = 'getPage',
    /** Update a Page (Action) - Update details on a page, such as phone or email */
    UPDATE_PAGE = 'updatePage',

    // === OTHER OPERATIONS - Các thao tác khác ===
    /** Publish a Reel (Action) - Uploads a reel */
    PUBLISH_REEL = 'publishReel'
}

/**
 * Facebook post types - Loại bài đăng Facebook
 */
export enum EFacebookPostType {
    /** Text only post - Bài đăng chỉ có văn bản */
    TEXT = 'text',
    /** Photo post - Bài đăng có ảnh */
    PHOTO = 'photo',
    /** Video post - Bài đăng có video */
    VIDEO = 'video',
    /** Link post - Bài đăng có liên kết */
    LINK = 'link',
    /** Event post - Bài đăng sự kiện */
    EVENT = 'event'
}

/**
 * Facebook post privacy settings - Cài đặt quyền riêng tư bài đăng
 */
export enum EFacebookPostPrivacy {
    /** Public post - Bài đăng công khai */
    PUBLIC = 'EVERYONE',
    /** Friends only - Chỉ bạn bè */
    FRIENDS = 'ALL_FRIENDS',
    /** Only me - Chỉ mình tôi */
    SELF = 'SELF'
}

/**
 * Facebook page fields - Các trường thông tin trang Facebook
 */
export enum EFacebookPageField {
    /** Page ID */
    ID = 'id',
    /** Page name */
    NAME = 'name',
    /** Page category */
    CATEGORY = 'category',
    /** Page about */
    ABOUT = 'about',
    /** Page phone */
    PHONE = 'phone',
    /** Page email */
    EMAIL = 'email',
    /** Page website */
    WEBSITE = 'website',
    /** Page picture */
    PICTURE = 'picture',
    /** Page cover */
    COVER = 'cover',
    /** Page access token */
    ACCESS_TOKEN = 'access_token',
    /** Page fan count */
    FAN_COUNT = 'fan_count'
}

/**
 * Facebook post fields - Các trường thông tin bài đăng Facebook
 */
export enum EFacebookPostField {
    /** Post ID */
    ID = 'id',
    /** Post message */
    MESSAGE = 'message',
    /** Post story */
    STORY = 'story',
    /** Post created time */
    CREATED_TIME = 'created_time',
    /** Post updated time */
    UPDATED_TIME = 'updated_time',
    /** Post type */
    TYPE = 'type',
    /** Post status type */
    STATUS_TYPE = 'status_type',
    /** Post permalink URL */
    PERMALINK_URL = 'permalink_url',
    /** Post full picture */
    FULL_PICTURE = 'full_picture',
    /** Post attachments */
    ATTACHMENTS = 'attachments',
    /** Post reactions */
    REACTIONS = 'reactions',
    /** Post comments */
    COMMENTS = 'comments',
    /** Post shares */
    SHARES = 'shares'
}

/**
 * Facebook comment fields - Các trường thông tin bình luận Facebook
 */
export enum EFacebookCommentField {
    /** Comment ID */
    ID = 'id',
    /** Comment message */
    MESSAGE = 'message',
    /** Comment created time */
    CREATED_TIME = 'created_time',
    /** Comment from user */
    FROM = 'from',
    /** Comment like count */
    LIKE_COUNT = 'like_count',
    /** Comment parent */
    PARENT = 'parent'
}

/**
 * Facebook video fields - Các trường thông tin video Facebook
 */
export enum EFacebookVideoField {
    /** Video ID */
    ID = 'id',
    /** Video title */
    TITLE = 'title',
    /** Video description */
    DESCRIPTION = 'description',
    /** Video created time */
    CREATED_TIME = 'created_time',
    /** Video updated time */
    UPDATED_TIME = 'updated_time',
    /** Video source */
    SOURCE = 'source',
    /** Video thumbnail */
    THUMBNAIL = 'thumbnail',
    /** Video length */
    LENGTH = 'length'
}

/**
 * Facebook photo fields - Các trường thông tin ảnh Facebook
 */
export enum EFacebookPhotoField {
    /** Photo ID */
    ID = 'id',
    /** Photo name */
    NAME = 'name',
    /** Photo created time */
    CREATED_TIME = 'created_time',
    /** Photo updated time */
    UPDATED_TIME = 'updated_time',
    /** Photo source */
    SOURCE = 'source',
    /** Photo width */
    WIDTH = 'width',
    /** Photo height */
    HEIGHT = 'height'
}

/**
 * Facebook reaction types - Các loại reaction Facebook
 */
export enum EFacebookReactionType {
    /** Like reaction */
    LIKE = 'LIKE',
    /** Love reaction */
    LOVE = 'LOVE',
    /** Haha reaction */
    HAHA = 'HAHA',
    /** Wow reaction */
    WOW = 'WOW',
    /** Sad reaction */
    SAD = 'SAD',
    /** Angry reaction */
    ANGRY = 'ANGRY',
    /** Care reaction */
    CARE = 'CARE',
    /** Pride reaction */
    PRIDE = 'PRIDE'
}

/**
 * Facebook photo upload action - Hành động upload ảnh
 */
export enum EFacebookPhotoAction {
    /** Upload a photo */
    UPLOAD = 'upload a photo',
    /** Download photo from URL */
    DOWNLOAD_FROM_URL = 'download photo from URL'
}
