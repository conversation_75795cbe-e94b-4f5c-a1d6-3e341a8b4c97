/**
 * @file Schedule Detection Service
 * 
 * Service để detect schedule nodes thông qua nodeDefinitionId join
 * Check xem node có phải là schedule type không
 * 
 */

import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Node } from '../../entities/node.entity';
import { NodeDefinition } from '../../entities/node-definition.entity';
import { ENodeType } from '../../interfaces/node-manager.interface';

/**
 * Interface cho schedule node info
 */
export interface IScheduleNodeInfo {
    nodeId: string;
    nodeName: string;
    workflowId: string;
    nodeDefinitionId: string;
    typeName: string;
    parameters: any;
    isScheduleNode: boolean;
}

@Injectable()
export class ScheduleDetectionService {
    private readonly logger = new Logger(ScheduleDetectionService.name);
    
    constructor(
        @InjectRepository(Node)
        private readonly nodeRepository: Repository<Node>,
        
        @InjectRepository(NodeDefinition)
        private readonly nodeDefinitionRepository: Repository<NodeDefinition>
    ) {}
    
    /**
     * Check xem node có phải là schedule type không
     */
    async isScheduleNode(nodeId: string): Promise<boolean> {
        try {
            const nodeInfo = await this.getNodeWithDefinition(nodeId);
            return nodeInfo?.isScheduleNode || false;
        } catch (error) {
            this.logger.error(`Failed to check if node is schedule type: ${nodeId}`, error);
            return false;
        }
    }
    
    /**
     * Get node info với node definition
     */
    async getNodeWithDefinition(nodeId: string): Promise<IScheduleNodeInfo | null> {
        try {
            // Query với join để get node definition
            const result = await this.nodeRepository
                .createQueryBuilder('node')
                .leftJoinAndSelect('node_definitions', 'def', 'node.nodeDefinitionId = def.id')
                .select([
                    'node.id as nodeId',
                    'node.name as nodeName', 
                    'node.workflowId as workflowId',
                    'node.nodeDefinitionId as nodeDefinitionId',
                    'node.parameters as parameters',
                    'def.typeName as typeName'
                ])
                .where('node.id = :nodeId', { nodeId })
                .getRawOne();
            
            if (!result) {
                this.logger.warn(`Node not found: ${nodeId}`);
                return null;
            }
            
            const isScheduleNode = result.typeName === ENodeType.SCHEDULE;
            
            return {
                nodeId: result.nodeId,
                nodeName: result.nodeName,
                workflowId: result.workflowId,
                nodeDefinitionId: result.nodeDefinitionId,
                typeName: result.typeName,
                parameters: result.parameters,
                isScheduleNode
            };
            
        } catch (error) {
            this.logger.error(`Failed to get node with definition: ${nodeId}`, error);
            return null;
        }
    }
    
    /**
     * Get tất cả schedule nodes trong một workflow
     */
    async getScheduleNodesInWorkflow(workflowId: string): Promise<IScheduleNodeInfo[]> {
        try {
            // Query tất cả nodes trong workflow với schedule type
            const results = await this.nodeRepository
                .createQueryBuilder('node')
                .leftJoinAndSelect('node_definitions', 'def', 'node.nodeDefinitionId = def.id')
                .select([
                    'node.id as nodeId',
                    'node.name as nodeName',
                    'node.workflowId as workflowId', 
                    'node.nodeDefinitionId as nodeDefinitionId',
                    'node.parameters as parameters',
                    'def.typeName as typeName'
                ])
                .where('node.workflowId = :workflowId', { workflowId })
                .andWhere('def.typeName = :typeName', { typeName: ENodeType.SCHEDULE })
                .getRawMany();
            
            return results.map(result => ({
                nodeId: result.nodeId,
                nodeName: result.nodeName,
                workflowId: result.workflowId,
                nodeDefinitionId: result.nodeDefinitionId,
                typeName: result.typeName,
                parameters: result.parameters,
                isScheduleNode: true
            }));
            
        } catch (error) {
            this.logger.error(`Failed to get schedule nodes in workflow: ${workflowId}`, error);
            return [];
        }
    }
    
    /**
     * Get tất cả active schedule nodes across all workflows
     */
    async getAllActiveScheduleNodes(): Promise<IScheduleNodeInfo[]> {
        try {
            const results = await this.nodeRepository
                .createQueryBuilder('node')
                .leftJoinAndSelect('node_definitions', 'def', 'node.nodeDefinitionId = def.id')
                .leftJoinAndSelect('workflows', 'workflow', 'node.workflowId = workflow.id')
                .select([
                    'node.id as nodeId',
                    'node.name as nodeName',
                    'node.workflowId as workflowId',
                    'node.nodeDefinitionId as nodeDefinitionId', 
                    'node.parameters as parameters',
                    'def.typeName as typeName'
                ])
                .where('def.typeName = :typeName', { typeName: ENodeType.SCHEDULE })
                .andWhere('workflow.isActive = :isActive', { isActive: true })
                .andWhere('node.disabled = :disabled', { disabled: false })
                .getRawMany();
            
            return results.map(result => ({
                nodeId: result.nodeId,
                nodeName: result.nodeName,
                workflowId: result.workflowId,
                nodeDefinitionId: result.nodeDefinitionId,
                typeName: result.typeName,
                parameters: result.parameters,
                isScheduleNode: true
            }));
            
        } catch (error) {
            this.logger.error('Failed to get all active schedule nodes', error);
            return [];
        }
    }
    
    /**
     * Validate node có thể được update thành schedule không
     */
    async validateNodeForScheduleUpdate(nodeId: string): Promise<{
        isValid: boolean;
        error?: string;
        nodeInfo?: IScheduleNodeInfo;
    }> {
        try {
            const nodeInfo = await this.getNodeWithDefinition(nodeId);
            
            if (!nodeInfo) {
                return {
                    isValid: false,
                    error: `Node not found: ${nodeId}`
                };
            }
            
            if (!nodeInfo.isScheduleNode) {
                return {
                    isValid: false,
                    error: `Node is not a schedule type: ${nodeId} (type: ${nodeInfo.typeName})`
                };
            }
            
            return {
                isValid: true,
                nodeInfo
            };
            
        } catch (error) {
            this.logger.error(`Failed to validate node for schedule update: ${nodeId}`, error);
            return {
                isValid: false,
                error: error.message
            };
        }
    }
    
    /**
     * Get schedule node definition
     */
    async getScheduleNodeDefinition(): Promise<NodeDefinition | null> {
        try {
            return await this.nodeDefinitionRepository.findOne({
                where: { typeName: ENodeType.SCHEDULE }
            });
        } catch (error) {
            this.logger.error('Failed to get schedule node definition', error);
            return null;
        }
    }
    
    /**
     * Check xem có schedule nodes nào trong workflow không
     */
    async hasScheduleNodes(workflowId: string): Promise<boolean> {
        try {
            const count = await this.nodeRepository
                .createQueryBuilder('node')
                .leftJoin('node_definitions', 'def', 'node.nodeDefinitionId = def.id')
                .where('node.workflowId = :workflowId', { workflowId })
                .andWhere('def.typeName = :typeName', { typeName: ENodeType.SCHEDULE })
                .getCount();
            
            return count > 0;
        } catch (error) {
            this.logger.error(`Failed to check if workflow has schedule nodes: ${workflowId}`, error);
            return false;
        }
    }
    
    /**
     * Get statistics về schedule nodes
     */
    async getScheduleNodeStats(): Promise<{
        totalScheduleNodes: number;
        activeScheduleNodes: number;
        workflowsWithSchedules: number;
        schedulesByType: Record<string, number>;
    }> {
        try {
            // Total schedule nodes
            const totalScheduleNodes = await this.nodeRepository
                .createQueryBuilder('node')
                .leftJoin('node_definitions', 'def', 'node.nodeDefinitionId = def.id')
                .where('def.typeName = :typeName', { typeName: ENodeType.SCHEDULE })
                .getCount();
            
            // Active schedule nodes (in active workflows)
            const activeScheduleNodes = await this.nodeRepository
                .createQueryBuilder('node')
                .leftJoin('node_definitions', 'def', 'node.nodeDefinitionId = def.id')
                .leftJoin('workflows', 'workflow', 'node.workflowId = workflow.id')
                .where('def.typeName = :typeName', { typeName: ENodeType.SCHEDULE })
                .andWhere('workflow.isActive = :isActive', { isActive: true })
                .andWhere('node.disabled = :disabled', { disabled: false })
                .getCount();
            
            // Workflows with schedules
            const workflowsWithSchedules = await this.nodeRepository
                .createQueryBuilder('node')
                .leftJoin('node_definitions', 'def', 'node.nodeDefinitionId = def.id')
                .select('DISTINCT node.workflowId')
                .where('def.typeName = :typeName', { typeName: ENodeType.SCHEDULE })
                .getCount();
            
            // Schedules by type (would need to parse parameters)
            const schedulesByType: Record<string, number> = {
                once: 0,
                daily: 0,
                weekly: 0,
                monthly: 0,
                interval: 0,
                cron: 0
            };
            
            return {
                totalScheduleNodes,
                activeScheduleNodes,
                workflowsWithSchedules,
                schedulesByType
            };
            
        } catch (error) {
            this.logger.error('Failed to get schedule node stats', error);
            return {
                totalScheduleNodes: 0,
                activeScheduleNodes: 0,
                workflowsWithSchedules: 0,
                schedulesByType: {}
            };
        }
    }
}
