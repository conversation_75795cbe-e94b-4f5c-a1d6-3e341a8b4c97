# API SSE QR Code Zalo - Hướng dẫn sử dụng

## Tổng quan

API SSE QR Code Zalo cho phép frontend theo dõi trạng thái đăng nhập QR code real-time thông qua Server-Sent Events (SSE). API này hoạt động như một proxy gi<PERSON>a app main và automation-web.

## Endpoints

### 1. Tạo QR Code Session

**POST** `/api/v1/marketing/zalo/qr-code/session`

```bash
curl -X POST "http://localhost:3000/api/v1/marketing/zalo/qr-code/session" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "integrationId": "uuid-integration-id-123"
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "Tạo QR code session thành công",
  "data": {
    "sessionId": "session-uuid-456",
    "qrCodeBase64": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
    "expiresAt": 1640995230,
    "integrationId": "uuid-integration-id-123",
    "message": "Session đã được tạo thành công"
  }
}
```

### 2. Kiểm tra trạng thái Session

**GET** `/api/v1/marketing/zalo/qr-code/session/{sessionId}/status`

```bash
curl -X GET "http://localhost:3000/api/v1/marketing/zalo/qr-code/session/session-uuid-456/status" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**Response:**
```json
{
  "success": true,
  "message": "Lấy trạng thái session thành công",
  "data": {
    "sessionId": "session-uuid-456",
    "status": "pending",
    "integrationId": "uuid-integration-id-123",
    "createdAt": "2024-01-01T10:00:00.000Z",
    "expiresAt": "2024-01-01T10:00:30.000Z"
  }
}
```

### 3. SSE Stream theo dõi trạng thái

**GET** `/api/v1/marketing/zalo/qr-code/session/{sessionId}/stream`

```javascript
// Frontend JavaScript
const sessionId = 'session-uuid-456';
const eventSource = new EventSource(
  `/api/v1/marketing/zalo/qr-code/session/${sessionId}/stream`,
  {
    headers: {
      'Authorization': 'Bearer YOUR_JWT_TOKEN'
    }
  }
);

eventSource.onmessage = (event) => {
  const data = JSON.parse(event.data);
  console.log('Event received:', data);
  
  switch (data.event) {
    case 'connected':
      console.log('✅ Kết nối SSE thành công');
      break;
      
    case 'heartbeat':
      console.log('💓 Heartbeat:', data.data);
      break;
      
    case 'login_success':
      console.log('🎉 Đăng nhập thành công!', data.data);
      eventSource.close();
      // Redirect hoặc update UI
      break;
      
    case 'qr_expired':
      console.log('⏰ QR code đã hết hạn');
      eventSource.close();
      // Tạo QR code mới
      break;
      
    case 'error':
      console.log('❌ Lỗi:', data.data);
      eventSource.close();
      break;
  }
};

eventSource.onerror = (error) => {
  console.error('SSE Error:', error);
  eventSource.close();
};

// Đóng kết nối sau 35 giây (QR code hết hạn sau 30 giây)
setTimeout(() => {
  if (eventSource.readyState !== EventSource.CLOSED) {
    eventSource.close();
  }
}, 35000);
```

## Trạng thái Session

| Status | Mô tả |
|--------|-------|
| `pending` | Đang chờ user scan QR code |
| `success` | Đăng nhập thành công |
| `expired` | QR code đã hết hạn (30 giây) |
| `error` | Có lỗi xảy ra |

## Events SSE

| Event | Mô tả | Khi nào gửi |
|-------|-------|-------------|
| `connected` | Kết nối SSE thành công | Ngay khi kết nối |
| `heartbeat` | Heartbeat với trạng thái hiện tại | Mỗi 2 giây |
| `login_success` | Đăng nhập thành công | Khi user scan QR và đăng nhập |
| `qr_expired` | QR code hết hạn | Sau 30 giây |
| `error` | Có lỗi xảy ra | Khi có lỗi |

## Flow hoàn chỉnh

```javascript
class ZaloQRCodeLogin {
  constructor() {
    this.sessionId = null;
    this.eventSource = null;
  }

  async startLogin(integrationId) {
    try {
      // 1. Tạo QR code session
      const response = await fetch('/api/v1/marketing/zalo/qr-code/session', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.getToken()}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ integrationId })
      });

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.message);
      }

      this.sessionId = result.data.sessionId;
      
      // 2. Hiển thị QR code
      this.displayQRCode(result.data.qrCodeBase64);
      
      // 3. Bắt đầu SSE stream
      this.startSSEStream();
      
    } catch (error) {
      console.error('Error starting login:', error);
    }
  }

  startSSEStream() {
    if (!this.sessionId) return;

    this.eventSource = new EventSource(
      `/api/v1/marketing/zalo/qr-code/session/${this.sessionId}/stream`
    );

    this.eventSource.onmessage = (event) => {
      const data = JSON.parse(event.data);
      this.handleSSEEvent(data);
    };

    this.eventSource.onerror = (error) => {
      console.error('SSE Error:', error);
      this.cleanup();
    };
  }

  handleSSEEvent(data) {
    switch (data.event) {
      case 'connected':
        console.log('SSE connected');
        break;
        
      case 'heartbeat':
        console.log('Heartbeat:', data.data.status);
        break;
        
      case 'login_success':
        console.log('Login successful!');
        this.onLoginSuccess(data.data);
        this.cleanup();
        break;
        
      case 'qr_expired':
        console.log('QR expired');
        this.onQRExpired();
        this.cleanup();
        break;
        
      case 'error':
        console.log('Error:', data.data.message);
        this.onError(data.data);
        this.cleanup();
        break;
    }
  }

  displayQRCode(base64Image) {
    const img = document.getElementById('qr-code-image');
    img.src = base64Image;
    img.style.display = 'block';
  }

  onLoginSuccess(data) {
    alert('Đăng nhập thành công!');
    // Redirect hoặc update UI
  }

  onQRExpired() {
    alert('QR code đã hết hạn. Vui lòng thử lại.');
    // Tạo QR code mới
  }

  onError(data) {
    alert(`Lỗi: ${data.message}`);
  }

  cleanup() {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
  }

  getToken() {
    return localStorage.getItem('jwt_token');
  }
}

// Sử dụng
const qrLogin = new ZaloQRCodeLogin();
qrLogin.startLogin('your-integration-id');
```

## Lưu ý

1. **Authentication**: Tất cả endpoints đều yêu cầu JWT token
2. **Timeout**: QR code hết hạn sau 30 giây
3. **Polling**: SSE sử dụng polling mỗi 2 giây để kiểm tra trạng thái
4. **Auto cleanup**: SSE tự động đóng khi có terminal events (success, expired, error)
5. **Error handling**: Luôn handle lỗi và cleanup resources

## Cấu hình Environment

Thêm vào `.env`:
```env
AUTOMATION_WEB_API_URL=http://localhost:8002
AUTOMATION_WEB_API_KEY=
AUTOMATION_WEB_TIMEOUT=30000
```
