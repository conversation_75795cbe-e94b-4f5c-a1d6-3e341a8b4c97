import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsEnum, IsUrl } from 'class-validator';
import { ConversationThreadsAttachmentType } from '@/modules/business/entities/external-conversation-message-attachment.entity';

/**
 * External Message Attachment Response DTO
 */
export class ExternalMessageAttachmentDto {
  @ApiProperty({
    description: 'Unique identifier of the attachment',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @IsString()
  attachmentId: string;

  @ApiProperty({
    description: 'Type of attachment',
    enum: ConversationThreadsAttachmentType,
    example: ConversationThreadsAttachmentType.IMAGE,
  })
  @IsEnum(ConversationThreadsAttachmentType)
  attachmentType: ConversationThreadsAttachmentType;

  @ApiProperty({
    description: 'CDN URL for viewing the attachment',
    example: 'https://cdn.example.com/attachments/image.jpg?expires=1234567890',
  })
  @IsUrl()
  viewUrl: string;

  @ApiProperty({
    description: 'Display name of the attachment',
    example: 'screenshot.png',
  })
  @IsString()
  name: string;

  constructor(data: {
    attachmentId: string;
    attachmentType: ConversationThreadsAttachmentType;
    viewUrl: string;
    name: string;
  }) {
    this.attachmentId = data.attachmentId;
    this.attachmentType = data.attachmentType;
    this.viewUrl = data.viewUrl;
    this.name = data.name;
  }
}