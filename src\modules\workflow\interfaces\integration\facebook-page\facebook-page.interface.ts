/**
 * @file Facebook Page Integration Node Interface
 *
 * Đ<PERSON><PERSON> nghĩa type-safe interface cho Facebook Page node operations
 * Theo patterns từ Make.com và n8n industry standards
 *
 * @version 2.0.0
 * <AUTHOR> Assistant
 */

import {
    IBaseIntegrationParameters,
    ITriggerParameters,
    IActionParameters,
    ISearchParameters,
    IBaseIntegrationInput,
    IBaseIntegrationOutput,
    EIntegrationOperationType,
    EIntegrationErrorHandling,
    IBaseIntegrationCredential
} from '../base/base-integration.interface';

import {
    ECredentialName,
    ENodeAuthType,
    EPropertyType,
    ELoadOptionsResource,
    ELoadOptionsMethod,
    INodeProperty
} from '../../node-manager.interface';

import {
    ITypedNodeExecution
} from '../../execute.interface';

import {
    EFacebookPageOperation,
    EFacebookPostType,
    EFacebookPostPrivacy,
    EFacebookPageField,
    EFacebookPostField,
    EFacebookCommentField,
    EFacebookVideoField,
    EFacebookPhotoField,
    EFacebookReactionType,
    EFacebookPhotoAction
} from './facebook-page.types';

// =================================================================
// SECTION 1: FACEBOOK PAGE PARAMETERS
// =================================================================

// =================================================================
// SECTION 2: FACEBOOK PAGE PARAMETERS
// =================================================================

/**
 * List Posts parameters - Tham số liệt kê bài đăng
 */
export interface IListPostsParameters extends IActionParameters {
    operation: EFacebookPageOperation.LIST_POSTS;

    /** Page ID - ID trang Facebook (required) */
    page_id: string;

    /** Include hidden posts - Bao gồm bài đăng ẩn */
    include_hidden?: boolean;

    /** Limit - Số lượng kết quả tối đa (required, default: 2, max: 500) */
    limit: number;

    /** Fields to retrieve - Các trường thông tin cần lấy */
    fields?: EFacebookPostField[];
}

/**
 * Get a Post parameters - Tham số lấy thông tin bài đăng
 */
export interface IGetPostParameters extends IActionParameters {
    operation: EFacebookPageOperation.GET_POST;

    /** Page ID - ID trang Facebook (required) */
    page_id: string;

    /** Post ID - ID bài đăng cần lấy thông tin (required) */
    post_id: string;

    /** Fields to retrieve - Các trường thông tin cần lấy */
    fields?: EFacebookPostField[];
}

/**
 * Get Post Reactions parameters - Tham số lấy reactions của bài đăng
 */
export interface IGetPostReactionsParameters extends IActionParameters {
    operation: EFacebookPageOperation.GET_POST_REACTIONS;

    /** Page ID - ID trang Facebook (required) */
    page_id: string;

    /** Post ID - ID bài đăng cần lấy reactions (required) */
    post_id: string;

    /** Type - Loại reaction cần lấy (optional) */
    type?: EFacebookReactionType;

    /** Limit - Số lượng reactions tối đa (default: 25, max: 100) */
    limit?: number;
}

/**
 * Create a Post parameters - Tham số tạo bài đăng
 */
export interface ICreatePostParameters extends IActionParameters {
    operation: EFacebookPageOperation.CREATE_POST;

    /** Page ID - ID trang Facebook (required) */
    page_id: string;

    /** Message - Nội dung bài đăng */
    message?: string;

    /** Link - Liên kết đính kèm */
    link?: string;

    /** Published - Trạng thái xuất bản (default: true) */
    published?: boolean;

    /** Scheduled publish time - Thời gian xuất bản theo lịch (ISO string) */
    scheduled_publish_time?: string;

    /** Privacy - Cài đặt quyền riêng tư */
    privacy?: EFacebookPostPrivacy;
}

/**
 * Photo item for Create a Post with Photos - Item ảnh cho tạo bài đăng với ảnh
 */
export interface IFacebookPhotoItem {
    /** I want to - Hành động với ảnh (required) */
    action: EFacebookPhotoAction;

    /** File - File ảnh cần upload (for upload action) */
    file?: {
        /** File name - Tên file (required) */
        filename: string;
        /** Data - Dữ liệu file (required) */
        data: string | Buffer;
    };

    /** URL - URL ảnh cần download (for download from URL action) */
    url?: string;

    /** Caption - Caption cho ảnh */
    caption?: string;
}

/**
 * Create a Post with Photos parameters - Tham số tạo bài đăng với ảnh
 */
export interface ICreatePostWithPhotosParameters extends IActionParameters {
    operation: EFacebookPageOperation.CREATE_POST_WITH_PHOTOS;

    /** Page ID - ID trang Facebook (required) */
    page_id: string;

    /** Photos - Danh sách ảnh (required, min: 1) */
    photos: IFacebookPhotoItem[];

    /** Message - Nội dung bài đăng */
    message?: string;

    /** Date - Thời gian xuất bản trong tương lai (ISO string) */
    date?: string;

    /** Published - Trạng thái xuất bản (default: true) */
    published?: boolean;

    /** Scheduled publish time - Thời gian xuất bản theo lịch (ISO string) */
    scheduled_publish_time?: string;

    /** Privacy - Cài đặt quyền riêng tư */
    privacy?: EFacebookPostPrivacy;
}

/**
 * Update a Post parameters - Tham số cập nhật bài đăng
 */
export interface IUpdatePostParameters extends IActionParameters {
    operation: EFacebookPageOperation.UPDATE_POST;

    /** Page ID - ID trang Facebook (required) */
    page_id: string;

    /** Post ID - ID bài đăng cần cập nhật (required) */
    post_id: string;

    /** Message - Nội dung bài đăng mới */
    message?: string;
}

/**
 * Delete a Post parameters - Tham số xóa bài đăng
 */
export interface IDeletePostParameters extends IActionParameters {
    operation: EFacebookPageOperation.DELETE_POST;

    /** Page ID - ID trang Facebook (required) */
    page_id: string;

    /** Post ID - ID bài đăng cần xóa (required) */
    post_id: string;
}

/**
 * Like a Post parameters - Tham số like bài đăng
 */
export interface ILikePostParameters extends IActionParameters {
    operation: EFacebookPageOperation.LIKE_POST;

    /** Page ID - ID trang Facebook (required) */
    page_id: string;

    /** Post ID - ID bài đăng cần like (required) */
    post_id: string;
}

/**
 * Unlike a Post parameters - Tham số unlike bài đăng
 */
export interface IUnlikePostParameters extends IActionParameters {
    operation: EFacebookPageOperation.UNLIKE_POST;

    /** Page ID - ID trang Facebook (required) */
    page_id: string;

    /** Post ID - ID bài đăng cần unlike (required) */
    post_id: string;
}

/**
 * List Videos parameters - Tham số liệt kê video
 */
export interface IListVideosParameters extends IActionParameters {
    operation: EFacebookPageOperation.LIST_VIDEOS;

    /** Page ID - ID trang Facebook (required) */
    page_id: string;

    /** Type - Loại video (required) */
    type: string;

    /** Limit - Số lượng kết quả tối đa (required, default: 2, max: 500) */
    limit: number;

    /** Fields to retrieve - Các trường thông tin cần lấy */
    fields?: EFacebookVideoField[];
}

/**
 * Get a Video parameters - Tham số lấy thông tin video
 */
export interface IGetVideoParameters extends IActionParameters {
    operation: EFacebookPageOperation.GET_VIDEO;

    /** Page ID - ID trang Facebook (required) */
    page_id: string;

    /** Video ID - ID video cần lấy thông tin (required) */
    video_id: string;

    /** Fields to retrieve - Các trường thông tin cần lấy */
    fields?: EFacebookVideoField[];
}

/**
 * Upload a Video parameters - Tham số upload video
 */
export interface IUploadVideoParameters extends IActionParameters {
    operation: EFacebookPageOperation.UPLOAD_VIDEO;

    /** Page ID - ID trang Facebook (required) */
    page_id: string;

    /** Type - Loại thao tác (required) */
    type: string;

    /** File - File video cần upload (required) */
    file: {
        /** File name - Tên file (required) */
        filename: string;
        /** Data - Dữ liệu file (required) */
        data: string | Buffer;
    };

    /** Description - Mô tả video */
    description?: string;
}

/**
 * Update a Video parameters - Tham số cập nhật video
 */
export interface IUpdateVideoParameters extends IActionParameters {
    operation: EFacebookPageOperation.UPDATE_VIDEO;

    /** Page ID - ID trang Facebook (required) */
    page_id: string;

    /** Video ID - ID video cần cập nhật (required) */
    video_id: string;

    /** Title - Tiêu đề video */
    title?: string;

    /** Description - Mô tả video */
    description?: string;
}

/**
 * Delete a Video parameters - Tham số xóa video
 */
export interface IDeleteVideoParameters extends IActionParameters {
    operation: EFacebookPageOperation.DELETE_VIDEO;

    /** Page ID - ID trang Facebook (required) */
    page_id: string;

    /** Video ID - ID video cần xóa (required) */
    video_id: string;
}

/**
 * List Photos parameters - Tham số liệt kê ảnh
 */
export interface IListPhotosParameters extends IActionParameters {
    operation: EFacebookPageOperation.LIST_PHOTOS;

    /** Page ID - ID trang Facebook (required) */
    page_id: string;

    /** Limit - Số lượng kết quả tối đa (required, default: 2, max: 500) */
    limit: number;

    /** Fields to retrieve - Các trường thông tin cần lấy */
    fields?: EFacebookPhotoField[];
}

/**
 * Get a Photo parameters - Tham số lấy thông tin ảnh
 */
export interface IGetPhotoParameters extends IActionParameters {
    operation: EFacebookPageOperation.GET_PHOTO;

    /** Page ID - ID trang Facebook (required) */
    page_id: string;

    /** Photo ID - ID ảnh cần lấy thông tin (required) */
    photo_id: string;

    /** Fields to retrieve - Các trường thông tin cần lấy */
    fields?: EFacebookPhotoField[];
}

/**
 * Upload a Photo parameters - Tham số upload ảnh
 */
export interface IUploadPhotoParameters extends IActionParameters {
    operation: EFacebookPageOperation.UPLOAD_PHOTO;

    /** Page ID - ID trang Facebook (required) */
    page_id: string;

    /** File - File ảnh cần upload (required) */
    file: {
        /** File name - Tên file (required) */
        filename: string;
        /** Data - Dữ liệu file (required) */
        data: string | Buffer;
    };

    /** Message - Nội dung/caption cho ảnh */
    message?: string;
}

/**
 * Delete a Photo parameters - Tham số xóa ảnh
 */
export interface IDeletePhotoParameters extends IActionParameters {
    operation: EFacebookPageOperation.DELETE_PHOTO;

    /** Page ID - ID trang Facebook (required) */
    page_id: string;

    /** Photo ID - ID ảnh cần xóa (required) */
    photo_id: string;
}

/**
 * List Comments parameters - Tham số liệt kê comment
 */
export interface IListCommentsParameters extends IActionParameters {
    operation: EFacebookPageOperation.LIST_COMMENTS;

    /** Page ID - ID trang Facebook (required) */
    page_id: string;

    /** Filter - Bộ lọc comment (required) */
    filter: string;

    /** Order - Thứ tự sắp xếp */
    order?: string;

    /** Limit - Số lượng kết quả tối đa (required, default: 2, max: 500) */
    limit: number;

    /** Fields to retrieve - Các trường thông tin cần lấy */
    fields?: EFacebookCommentField[];
}

/**
 * Get a Comment parameters - Tham số lấy thông tin comment
 */
export interface IGetCommentParameters extends IActionParameters {
    operation: EFacebookPageOperation.GET_COMMENT;

    /** Page ID - ID trang Facebook (required) */
    page_id: string;

    /** Comment ID - ID comment cần lấy thông tin (required) */
    comment_id: string;

    /** Fields to retrieve - Các trường thông tin cần lấy */
    fields?: EFacebookCommentField[];
}

/**
 * Create a Comment parameters - Tham số tạo comment
 */
export interface ICreateCommentParameters extends IActionParameters {
    operation: EFacebookPageOperation.CREATE_COMMENT;

    /** Page ID - ID trang Facebook (required) */
    page_id: string;

    /** Message - Nội dung comment */
    message?: string;

    /** Attachment type - Loại đính kèm */
    attachment_type?: string;
}

/**
 * Update a Comment parameters - Tham số cập nhật comment
 */
export interface IUpdateCommentParameters extends IActionParameters {
    operation: EFacebookPageOperation.UPDATE_COMMENT;

    /** Page ID - ID trang Facebook (required) */
    page_id: string;

    /** Comment ID - ID comment cần cập nhật (required) */
    comment_id: string;

    /** Is hidden - Ẩn comment hay không */
    is_hidden?: boolean;

    /** Message - Nội dung comment */
    message?: string;

    /** Attachment type - Loại đính kèm */
    attachment_type?: string;
}

/**
 * Delete a Comment parameters - Tham số xóa comment
 */
export interface IDeleteCommentParameters extends IActionParameters {
    operation: EFacebookPageOperation.DELETE_COMMENT;

    /** Page ID - ID trang Facebook (required) */
    page_id: string;

    /** Comment ID - ID comment cần xóa (required) */
    comment_id: string;
}

/**
 * Get a Page parameters - Tham số lấy thông tin trang
 */
export interface IGetPageParameters extends IActionParameters {
    operation: EFacebookPageOperation.GET_PAGE;

    /** Page ID - ID trang Facebook (required) */
    page_id: string;

    /** Fields to retrieve - Các trường thông tin cần lấy */
    fields?: EFacebookPageField[];
}

/**
 * Update a Page parameters - Tham số cập nhật trang
 */
export interface IUpdatePageParameters extends IActionParameters {
    operation: EFacebookPageOperation.UPDATE_PAGE;

    /** Page ID - ID trang Facebook (required) */
    page_id: string;

    /** About - Thông tin về trang */
    about?: string;

    /** Bio - Tiểu sử */
    bio?: string;

    /** Company overview - Tổng quan công ty */
    company_overview?: string;

    /** Description - Mô tả */
    description?: string;

    /** Emails - Danh sách email */
    emails?: string[];

    /** General info - Thông tin chung */
    general_info?: string;

    /** Impressum - Thông tin pháp lý */
    impressum?: string;

    /** Is always open - Luôn mở cửa */
    is_always_open?: boolean;

    /** Is permanently closed - Đóng cửa vĩnh viễn */
    is_permanently_closed?: boolean;

    /** Mission - Sứ mệnh */
    mission?: string;

    /** Phone - Số điện thoại */
    phone?: string;

    /** Price range - Khoảng giá */
    price_range?: string;

    /** Service details - Chi tiết dịch vụ */
    service_details?: string;

    /** Website - Website */
    website?: string;
}

/**
 * Publish a Reel parameters - Tham số publish reel
 */
export interface IPublishReelParameters extends IActionParameters {
    operation: EFacebookPageOperation.PUBLISH_REEL;

    /** Page ID - ID trang Facebook (required) */
    page_id: string;

    /** Upload method - Phương thức upload (required) */
    upload_method: string;

    /** File - File video (for file upload method) */
    file?: {
        /** File name - Tên file (required) */
        filename: string;
        /** Data - Dữ liệu file (required) */
        data: string | Buffer;
    };

    /** URL - URL video (for URL upload method) */
    url?: string;

    /** Description - Mô tả cho video (hỗ trợ hashtags) */
    description?: string;

    /** Title - Tiêu đề cho video */
    title?: string;
}

/**
 * Watch Posts trigger parameters - Tham số theo dõi bài đăng
 */
export interface IWatchPostsParameters extends ITriggerParameters {
    operation: EFacebookPageOperation.WATCH_POSTS;

    /** Page ID to watch */
    page_id: string;

    /** Post types to watch */
    post_types?: EFacebookPostType[];

    /** Include comments in response */
    include_comments?: boolean;

    /** Maximum posts per poll */
    limit?: number;
}

/**
 * Watch Comments trigger parameters
 */
export interface IWatchCommentsParameters extends ITriggerParameters {
    operation: EFacebookPageOperation.WATCH_COMMENTS;

    /** Page ID to watch */
    page_id: string;

    /** Specific post ID (optional) */
    post_id?: string;

    /** Include replies to comments */
    include_replies?: boolean;

    /** Maximum comments per poll */
    limit?: number;
}

/**
 * Union type cho tất cả Facebook Page parameters
 */
export type IFacebookPageParameters =
    | IListPostsParameters
    | IGetPostParameters
    | IGetPostReactionsParameters
    | ICreatePostParameters
    | ICreatePostWithPhotosParameters
    | IUpdatePostParameters
    | IDeletePostParameters
    | ILikePostParameters
    | IUnlikePostParameters
    | IListVideosParameters
    | IGetVideoParameters
    | IUploadVideoParameters
    | IUpdateVideoParameters
    | IDeleteVideoParameters
    | IListPhotosParameters
    | IGetPhotoParameters
    | IUploadPhotoParameters
    | IDeletePhotoParameters
    | IListCommentsParameters
    | IGetCommentParameters
    | ICreateCommentParameters
    | IUpdateCommentParameters
    | IDeleteCommentParameters
    | IGetPageParameters
    | IUpdatePageParameters
    | IPublishReelParameters
    | IWatchPostsParameters
    | IWatchCommentsParameters;

// =================================================================
// SECTION 3: INPUT/OUTPUT INTERFACES
// =================================================================

/**
 * Facebook Page input interface
 */
export interface IFacebookPageInput extends IBaseIntegrationInput {
    /** Facebook page data */
    page?: {
        id: string;
        name: string;
        access_token: string;
    };

    /** Post data for creation */
    post?: {
        message?: string;
        photo?: any;
        link?: string;
    };
}

/**
 * Facebook Page output interface
 */
export interface IFacebookPageOutput extends IBaseIntegrationOutput {
    /** Facebook specific data */
    facebook?: {
        page_id?: string;
        post_id?: string;
        comment_id?: string;
        engagement?: {
            likes: number;
            comments: number;
            shares: number;
        };
    };
}



// =================================================================
// SECTION 5: CREDENTIAL DEFINITION
// =================================================================

/**
 * Facebook Page credential definition
 */
export const FACEBOOK_PAGE_CREDENTIAL: IBaseIntegrationCredential = {
    provider: 'facebook',
    name: ECredentialName.FACEBOOK_OAUTH,
    displayName: 'Facebook OAuth2',
    description: 'OAuth2 authentication for Facebook Pages',
    required: true,
    authType: ENodeAuthType.OAUTH2,
    testable: true,
    testUrl: '/api/integrations/test-connection'
};

// =================================================================
// SECTION 6: VALIDATION FUNCTIONS
// =================================================================

/**
 * Validate Facebook Page parameters
 */
export function validateFacebookPageParameters(
    params: Partial<IFacebookPageParameters>
): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!params.page_id) {
        errors.push('Page ID is required');
    }

    // Operation specific validation
    if (params.operation === EFacebookPageOperation.CREATE_POST ||
        params.operation === EFacebookPageOperation.CREATE_POST_WITH_PHOTOS) {
        if (!(params as ICreatePostParameters).message) {
            errors.push('Message is required for post creation');
        }
    }

    if (params.operation === EFacebookPageOperation.CREATE_POST_WITH_PHOTOS) {
        if (!(params as ICreatePostWithPhotosParameters).photos) {
            errors.push('Photos are required for photo post creation');
        }
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}

/**
 * Type guard cho Facebook Page parameters
 */
export function isFacebookPageParameters(params: any): params is IFacebookPageParameters {
    return params && Object.values(EFacebookPageOperation).includes(params.operation);
}

// =================================================================
// SECTION 4: OUTPUT INTERFACES
// =================================================================

/**
 * Facebook Page output interface
 */
export interface IFacebookPageOutput extends IBaseIntegrationOutput {
    /** Operation result data - Dữ liệu kết quả thao tác */
    data: {
        /** Posts data - Dữ liệu bài đăng */
        posts?: any[];
        /** Single post data - Dữ liệu bài đăng đơn */
        post?: any;
        /** Videos data - Dữ liệu video */
        videos?: any[];
        /** Single video data - Dữ liệu video đơn */
        video?: any;
        /** Photos data - Dữ liệu ảnh */
        photos?: any[];
        /** Single photo data - Dữ liệu ảnh đơn */
        photo?: any;
        /** Comments data - Dữ liệu bình luận */
        comments?: any[];
        /** Single comment data - Dữ liệu bình luận đơn */
        comment?: any;
        /** Page data - Dữ liệu trang */
        page?: any;
        /** Operation result - Kết quả thao tác */
        result?: {
            success: boolean;
            message?: string;
            id?: string;
            count?: number;
        };
    };
}



// =================================================================
// SECTION 6: NODE EXECUTION TYPE
// =================================================================

/**
 * Type-safe node execution cho Facebook Page
 */
export type IFacebookPageNodeExecution = ITypedNodeExecution<
    IFacebookPageInput,
    IFacebookPageOutput,
    IFacebookPageParameters
>;
