/**
 * @file Facebook Custom Audiences Module Exports
 * 
 * Export tất cả interfaces, types, và functions cho Facebook Custom Audiences integration
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

// Export Types & Enums
export {
    EFacebookCustomAudiencesOperation,
    EFacebookCustomAudienceType,
    EFacebookCustomAudienceSubtype,
    EFacebookLookalikeSpec,
    EFacebookAudienceMatchKey,
    EFacebookAudienceOperationType,
    EFacebookLookalikeAudienceType,
    EFacebookAudienceStatus
} from './facebook-custom-audiences.types';

// Export Interfaces
export {
    IFacebookCustomAudiencesInput,
    IFacebookCustomAudiencesOutput,
    ICreateCustomAudienceParameters,
    IAddEmailsToCustomAudienceParameters,
    IAddUsersToCustomAudienceParameters,
    IRemoveAudienceMembersParameters,
    ICreateLookalikeAudienceParameters,
    ICreatePageFanLookalikeAudienceParameters,
    ICreateCampaignConversionLookalikesParameters,
    ICreateValueBasedCustomAudienceParameters,
    IPopulateSeedAudienceParameters,
    ICreateValueBasedLookalikeParameters,
    ISeedAudienceData,
    ILookalikeSpecification,
    IPageFanLookalikeSpecification,
    ICampaignConversionLookalikeSpecification,
    IValueBasedLookalikeSpecification,
    ISourceSpecification,
    ILocationSpecification,
    IGeoLocationsSpecification,
    IUserData,
    IFacebookCustomAudiencesParameters
} from './facebook-custom-audiences.interface';

// Export Node Properties
export {
    FACEBOOK_CUSTOM_AUDIENCES_PROPERTIES
} from './facebook-custom-audiences.properties';

// Export Validation & Credentials
export {
    FACEBOOK_CUSTOM_AUDIENCES_CREDENTIAL,
    validateFacebookCustomAudiencesParameters,
    isFacebookCustomAudiencesParameters,
    IFacebookCustomAudiencesNodeExecution
} from './facebook-custom-audiences.validation';
