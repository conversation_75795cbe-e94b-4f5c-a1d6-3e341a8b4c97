/**
 * @file Workflow Schedule Module
 * 
 * Module tích hợp tất cả các services cho Schedule Trigger system
 * Cấ<PERSON> hình <PERSON> queue, dependencies và exports
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';

// Entities
import { Workflow } from '../entities/workflow.entity';
import { Node } from '../entities/node.entity';

// Services
// Old schedule services - temporarily disabled
// import { InMemorySchedulerService } from '../services/schedule/in-memory-scheduler.service';
// import { WorkflowQueueService } from '../services/schedule/workflow-queue.service';
// import { WorkflowLifecycleService } from '../services/schedule/workflow-lifecycle.service';
// import { WorkflowExecutionProcessor } from '../services/schedule/workflow-execution.processor';
// import { ScheduleBootstrapService } from '../services/schedule/schedule-bootstrap.service';

// Dependencies
import { WorkflowExecutionUserService } from '../user/services/workflow-execution-user.service';

@Module({
    imports: [
        // TypeORM entities
        TypeOrmModule.forFeature([
            Workflow,
            Node
        ]),
        
        // Bull Queue configuration
        BullModule.forRootAsync({
            imports: [ConfigModule],
            useFactory: async (configService: ConfigService) => ({
                redis: {
                    host: configService.get('REDIS_HOST', 'localhost'),
                    port: configService.get('REDIS_PORT', 6379),
                    password: configService.get('REDIS_PASSWORD'),
                    db: configService.get('REDIS_DB', 0),
                    maxRetriesPerRequest: 3,
                    retryDelayOnFailover: 100,
                    enableReadyCheck: false,
                },
                defaultJobOptions: {
                    removeOnComplete: 100,
                    removeOnFail: 50,
                    attempts: 3,
                    backoff: {
                        type: 'exponential',
                        delay: 2000,
                    },
                },
                settings: {
                    stalledInterval: 30 * 1000, // 30 seconds
                    maxStalledCount: 1,
                }
            }),
            inject: [ConfigService],
        }),
        
        // Register workflow execution queue
        BullModule.registerQueueAsync({
            name: 'workflow-execution',
            imports: [ConfigModule],
            useFactory: async (configService: ConfigService) => ({
                redis: {
                    host: configService.get('REDIS_HOST', 'localhost'),
                    port: configService.get('REDIS_PORT', 6379),
                    password: configService.get('REDIS_PASSWORD'),
                    db: configService.get('REDIS_DB', 0),
                },
                defaultJobOptions: {
                    removeOnComplete: configService.get('QUEUE_REMOVE_ON_COMPLETE', 100),
                    removeOnFail: configService.get('QUEUE_REMOVE_ON_FAIL', 50),
                    attempts: configService.get('QUEUE_MAX_ATTEMPTS', 3),
                    backoff: {
                        type: 'exponential',
                        delay: configService.get('QUEUE_RETRY_DELAY', 60000),
                    },
                },
                // processors configuration removed - will be handled by @Processor decorator
            }),
            inject: [ConfigService],
        }),
    ],
    
    providers: [
        // Old scheduler services - temporarily disabled
        // InMemorySchedulerService,
        // WorkflowQueueService,
        // WorkflowLifecycleService,
        // WorkflowExecutionProcessor,
        // ScheduleBootstrapService,

        // Dependencies
        WorkflowExecutionUserService,
    ],
    
    exports: [
        // Old services - temporarily disabled
        // InMemorySchedulerService,
        // WorkflowQueueService,
        // WorkflowLifecycleService,
        // ScheduleBootstrapService,

        // Export Bull queue for external monitoring/management
        BullModule,
    ],
})
export class WorkflowScheduleModule {}

/**
 * Configuration interface cho module
 */
export interface IWorkflowScheduleConfig {
    redis: {
        host: string;
        port: number;
        password?: string;
        db?: number;
    };
    queue: {
        concurrency: number;
        maxAttempts: number;
        retryDelay: number;
        removeOnComplete: number;
        removeOnFail: number;
    };
    scheduler: {
        checkInterval: number;
        maxConcurrentExecutions: number;
        defaultExecutionTimeout: number;
    };
}

/**
 * Default configuration
 */
export const DEFAULT_SCHEDULE_CONFIG: IWorkflowScheduleConfig = {
    redis: {
        host: 'localhost',
        port: 6379,
        db: 0,
    },
    queue: {
        concurrency: 5,
        maxAttempts: 3,
        retryDelay: 60000, // 1 minute
        removeOnComplete: 100,
        removeOnFail: 50,
    },
    scheduler: {
        checkInterval: 1000, // 1 second
        maxConcurrentExecutions: 10,
        defaultExecutionTimeout: 300000, // 5 minutes
    },
};

/**
 * Factory function để tạo module với custom config
 * TEMPORARILY DISABLED - Using new ScheduleModule instead
 */
/*
export class WorkflowScheduleModuleFactory {
    static forRoot(config?: Partial<IWorkflowScheduleConfig>) {
        const finalConfig = { ...DEFAULT_SCHEDULE_CONFIG, ...config };
        
        return {
            module: WorkflowScheduleModule,
            imports: [
                TypeOrmModule.forFeature([Workflow, Node]),
                
                BullModule.forRoot({
                    redis: finalConfig.redis,
                    defaultJobOptions: {
                        removeOnComplete: finalConfig.queue.removeOnComplete,
                        removeOnFail: finalConfig.queue.removeOnFail,
                        attempts: finalConfig.queue.maxAttempts,
                        backoff: {
                            type: 'exponential',
                            delay: finalConfig.queue.retryDelay,
                        },
                    },
                }),
                
                BullModule.registerQueue({
                    name: 'workflow-execution'
                }),
            ],
            providers: [
                InMemorySchedulerService,
                WorkflowQueueService,
                WorkflowLifecycleService,
                WorkflowExecutionProcessor,
                ScheduleBootstrapService,
                {
                    provide: 'SCHEDULE_CONFIG',
                    useValue: finalConfig,
                },
            ],
            exports: [
                InMemorySchedulerService,
                WorkflowQueueService,
                WorkflowLifecycleService,
                ScheduleBootstrapService,
                'SCHEDULE_CONFIG',
            ],
        };
    }
    
    static forRootAsync(options: {
        imports?: any[];
        useFactory?: (...args: any[]) => Promise<IWorkflowScheduleConfig> | IWorkflowScheduleConfig;
        inject?: any[];
    }) {
        return {
            module: WorkflowScheduleModule,
            imports: [
                TypeOrmModule.forFeature([Workflow, Node]),
                
                BullModule.forRootAsync({
                    imports: options.imports,
                    useFactory: async (...args: any[]) => {
                        const config = options.useFactory ? await options.useFactory(...args) : DEFAULT_SCHEDULE_CONFIG;
                        return {
                            redis: config.redis,
                            defaultJobOptions: {
                                removeOnComplete: config.queue.removeOnComplete,
                                removeOnFail: config.queue.removeOnFail,
                                attempts: config.queue.maxAttempts,
                                backoff: {
                                    type: 'exponential',
                                    delay: config.queue.retryDelay,
                                },
                            },
                        };
                    },
                    inject: options.inject,
                }),
                
                BullModule.registerQueueAsync({
                    name: 'workflow-execution',
                    imports: options.imports,
                    useFactory: async (...args: any[]) => {
                        // Return empty config - processors handled by @Processor decorator
                        return {};
                    },
                    inject: options.inject,
                }),
                
                ...(options.imports || []),
            ],
            providers: [
                InMemorySchedulerService,
                WorkflowQueueService,
                WorkflowLifecycleService,
                WorkflowExecutionProcessor,
                ScheduleBootstrapService,
                {
                    provide: 'SCHEDULE_CONFIG',
                    useFactory: options.useFactory || (() => DEFAULT_SCHEDULE_CONFIG),
                    inject: options.inject || [],
                },
            ],
            exports: [
                InMemorySchedulerService,
                WorkflowQueueService,
                WorkflowLifecycleService,
                // ScheduleBootstrapService, // Temporarily disabled
                'SCHEDULE_CONFIG',
            ],
        };
    }
}
*/

/**
 * Environment variables cần thiết
 */
export const REQUIRED_ENV_VARS = [
    'REDIS_HOST',
    'REDIS_PORT',
] as const;

export const OPTIONAL_ENV_VARS = [
    'REDIS_PASSWORD',
    'REDIS_DB',
    'QUEUE_CONCURRENCY',
    'QUEUE_MAX_ATTEMPTS',
    'QUEUE_RETRY_DELAY',
    'QUEUE_REMOVE_ON_COMPLETE',
    'QUEUE_REMOVE_ON_FAIL',
    'SCHEDULER_CHECK_INTERVAL',
    'SCHEDULER_MAX_CONCURRENT_EXECUTIONS',
    'SCHEDULER_DEFAULT_EXECUTION_TIMEOUT',
] as const;
