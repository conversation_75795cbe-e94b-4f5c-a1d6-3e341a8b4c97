import { AppException, ErrorCode } from '@/common';
import { <PERSON>ginatedResult } from '@/common/response/api-response-dto';
import { ConfigService, ConfigType, LivechatConfig } from '@/config';
import { CdnService } from '@/shared/services/cdn.service';
import { S3Service } from '@/shared/services/s3.service';
import { FileSizeEnum, ImageType, TimeIntervalEnum } from '@/shared/utils';
import { CategoryFolderEnum, generateS3Key } from '@/shared/utils/generators/s3-key-generator.util';
import { Injectable, Logger } from '@nestjs/common';
import axios from 'axios';
import { Transactional } from 'typeorm-transactional';
import { ProviderEnum } from '../../constants/provider.enum';
import { DisplayMode, SideMode } from '../../entities/box-chat-config.entity';
import { INTEGRATION_ERROR_CODES } from '../../exceptions/integration-error.code';
import { WebsiteMetadata } from '../../interfaces/metadata.interface';
import { BoxChatConfigRepository } from '../../repositories';
import { BoxChatConfigMediaRepository } from '../../repositories/box-chat-config-media.repository';
import { IntegrationWebsiteRepository } from '../../repositories/integration-website.repository';
import { EncryptionWebsiteService } from '../../../../shared/services/encryption';
import { MediaRepository } from '@/modules/data/media/repositories/media.repository';
import { Media } from '@/modules/data/media/entities/media.entity';
import { MediaStatusEnum } from '@/modules/data/media/enums/media-status.enum';
import { OwnerTypeEnum } from '@/modules/data/media/enums/owner-type.enum';
import { MediaTypeEnum } from '@/modules/data/media/enums/media-type.enum';
import { BoxChatConfigResponseDto, CreateWebsiteDto, CreateWebsiteResponseDto, DeleteWebsitesDto, ToggleWebsiteActiveResponseDto, UpdateAvatarResponseDto, UpdateBoxChatConfigDto, WebsiteQueryDto, WebsiteResponseDto } from '../dto';
import { IntegrationProviderRepository } from './../../repositories/integration-provider.repository';

@Injectable()
export class UserWebsiteUserService {
  private readonly logger = new Logger(UserWebsiteUserService.name);
  private livechatSRCUrl;

  constructor(
    private readonly integrationWebsiteRepository: IntegrationWebsiteRepository,
    private readonly integrationProviderRepository: IntegrationProviderRepository,
    private readonly s3Service: S3Service,
    private readonly cdnService: CdnService,
    private readonly boxChatConfigRepository: BoxChatConfigRepository,
    private readonly boxChatConfigMediaRepository: BoxChatConfigMediaRepository,
    private readonly encryptionWebsiteService: EncryptionWebsiteService,
    private readonly configService: ConfigService,
    private readonly mediaRepository: MediaRepository,
  ) {
    this.livechatSRCUrl = this.configService.getConfig<LivechatConfig>(
      ConfigType.Livechat,
    ).livechatSRCUrl;
  }

  /**
   * Lấy danh sách website của người dùng
   * @param queryDto Tham số truy vấn
   * @param userId ID của người dùng
   * @returns Danh sách website với phân trang
   */
  async findAll(
    queryDto: WebsiteQueryDto,
    userId: number,
  ): Promise<PaginatedResult<WebsiteResponseDto>> {
    try {
      const { page = 1, limit = 10, search, isConnectAgent } = queryDto;

      // Sử dụng repository method mới để lấy dữ liệu với join agent
      const result = await this.integrationWebsiteRepository.findWithPagination(
        userId,
        page,
        limit,
        search,
        isConnectAgent,
      );
      // Chuyển đổi và format dữ liệu thủ công
      const websiteResponseDtos = result.items.map((item) => {
        const key = this.encryptionWebsiteService.encryptLiveChatPayload({
          websiteId: item.id,
          userId: userId,
        });

        // Tạo script HTML với format ref="redai_{userId}"
        const script = `<script src="${this.livechatSRCUrl}" key="redai_${key}"></script>`;

        // Lấy metadata từ Integration entity
        const metadata = item.metadata as WebsiteMetadata;

        // Lấy agent data từ join (đã được xử lý trong repository)
        const agent = item.agent;

        // Manual mapping để đảm bảo tất cả trường được bao gồm
        const dto: WebsiteResponseDto = {
          id: item.id,
          host: metadata?.host || '',
          agentId: agent?.id || null,
          agentName: agent?.name || null,
          logo: metadata?.logo ? this.cdnService.generateUrlView(metadata.logo, TimeIntervalEnum.FIVE_MINUTES) : null,
          createdAt: item.createdAt,
          active: metadata?.active || false,
          script,
          click: metadata?.click || 0,
          websiteName: item.integrationName,
        };

        return dto;
      });

      return {
        items: websiteResponseDtos,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(
        `Error getting websites for user ${userId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Tạo mới website
   * @param createWebsiteDto Thông tin website cần tạo
   * @param userId ID của người dùng
   * @returns Thông tin website đã tạo với presigned URL cho logo (nếu có)
   */
  async create(
    createWebsiteDto: CreateWebsiteDto,
    userId: number,
  ): Promise<CreateWebsiteResponseDto> {
    try {
      // Kiểm tra xem host đã tồn tại chưa
      const existingWebsite = await this.integrationWebsiteRepository.findOne({
        where: { metadata: { host: createWebsiteDto.host }, userId },
      });

      if (existingWebsite) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          `Website với host ${createWebsiteDto.host} đã tồn tại`,
        );
      }

      // Kiểm tra xem website có tồn tại không
      await this.validateWebsite(createWebsiteDto.host);

      // Tạo presigned URL cho logo nếu có logoMime
      let logoUploadUrl: string | undefined;
      let logoKey: string | undefined = undefined;

      if (createWebsiteDto.logoMime) {
        // Tạo S3 key cho logo
        const logoS3Key = generateS3Key({
          baseFolder: userId.toString(),
          categoryFolder: CategoryFolderEnum.IMAGE,
          useTimeFolder: true,
        });

        // Tạo presigned URL
        const expirationTime = TimeIntervalEnum.FIFTEEN_MINUTES;
        logoUploadUrl = await this.s3Service.createPresignedWithID(
          logoS3Key,
          expirationTime,
          ImageType.getType(createWebsiteDto.logoMime),
          FileSizeEnum.FIVE_MB, // 5MB max size
        );

        // Lưu S3 key vào website
        logoKey = logoS3Key;
      }

      const provider = await this.integrationProviderRepository.findByType(
        ProviderEnum.WEBSITE,
      );

      if (!provider) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy provider WEBSITE'
        );
      }

      // Tạo mới website
      const newWebsite = this.integrationWebsiteRepository.create({
        integrationName: createWebsiteDto.websiteName,
        userId,
        typeId: provider.id,
        secretKey: '',
        metadata: {
          host: createWebsiteDto.host,
          active: true,
          logo: logoKey,
          click: 0,
        } as WebsiteMetadata,
      });

      // Lưu vào database
      await this.integrationWebsiteRepository.save(newWebsite);

      const boxChatConfig = this.boxChatConfigRepository.create({
        integrationId: newWebsite.id,
        welcomeText: '',
        avatar: logoKey,
        components: undefined,
        quickMessages: [],
        placeholderMessage: undefined,
        displayMode: DisplayMode.CENTER,
        sideMode: SideMode.FLOATING,
        colorPrimary: '#ff0000ff',
        icon: null,
      });

      await this.boxChatConfigRepository.save(boxChatConfig);

      // Tạo response DTO - chỉ trả về logoUploadUrl
      const response: CreateWebsiteResponseDto = {
        id: newWebsite.id,
        logoUploadUrl,
      };

      return response;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error creating website for user ${userId}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        `Không thể tạo website: ${error.message}`,
      );
    }
  }

  /**
   * Kiểm tra xem website có tồn tại không
   * @param host Host của website cần kiểm tra
   * @returns true nếu website tồn tại, false nếu không tồn tại
   */
  private async validateWebsite(host: string): Promise<boolean> {
    try {
      // Chuẩn hóa host
      let normalizedHost = host;
      if (
        !normalizedHost.startsWith('http://') &&
        !normalizedHost.startsWith('https://')
      ) {
        normalizedHost = `https://${normalizedHost}`;
      }

      // Kiểm tra xem website có tồn tại không
      const response = await axios.head(normalizedHost, {
        timeout: 5000, // Timeout 5 giây
        validateStatus: (status) => status < 500, // Chấp nhận status code < 500
      });

      this.logger.log(`Website status: ${response.status}`);

      // Nếu status code là 2xx hoặc 3xx, website tồn tại
      if (response.status >= 200 && response.status < 400) {
        return true;
      }

      // Nếu status code là 4xx, website không tồn tại hoặc không thể truy cập
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        `Website không tồn tại hoặc không thể truy cập (status code: ${response.status})`,
      );
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      // Xử lý lỗi từ axios
      if (axios.isAxiosError(error)) {
        if (error.code === 'ENOTFOUND') {
          throw new AppException(
            ErrorCode.VALIDATION_ERROR,
            'Không thể kết nối đến website. Vui lòng kiểm tra lại tên miền.',
          );
        }
        if (error.code === 'ECONNREFUSED') {
          throw new AppException(
            ErrorCode.VALIDATION_ERROR,
            'Kết nối bị từ chối. Website có thể không hoạt động.',
          );
        }
        if (error.code === 'ETIMEDOUT') {
          throw new AppException(
            ErrorCode.VALIDATION_ERROR,
            'Kết nối bị timeout. Website phản hồi quá chậm hoặc không phản hồi.',
          );
        }
      }

      // Lỗi khác
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        `Không thể xác minh website: ${error.message}`,
      );
    }
  }

  /**
   * Xóa một website
   * @param websiteId ID của website cần xóa
   * @param userId ID của người dùng
   */
  @Transactional()
  async deleteWebsite(websiteId: string, userId: number): Promise<void> {
    try {
      // Tìm website thuộc về người dùng
      const website = await this.integrationWebsiteRepository.findByIdAndUserIdForDelete(websiteId, userId);

      if (!website) {
        throw new AppException(INTEGRATION_ERROR_CODES.WEBSITE_NOT_FOUND);
      }

      // Xóa website integration
      await this.integrationWebsiteRepository.remove(website);

      this.logger.log(`Đã xóa website ${website.integrationName} (ID: ${websiteId}) của user ${userId}`);

      // Kiểm tra xem user còn website nào không
      const remainingWebsiteCount = await this.integrationWebsiteRepository.countByUserId(userId);

      this.logger.log(`User ${userId} còn lại ${remainingWebsiteCount} website(s)`);

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Lỗi khi xóa website: ${error.message}`, error.stack);
      throw new AppException(INTEGRATION_ERROR_CODES.WEBSITE_DELETE_FAILED);
    }
  }

  /**
   * Xóa nhiều website cùng lúc
   * @param deleteDto DTO chứa danh sách ID website cần xóa
   * @param userId ID của người dùng
   * @returns Thông tin về số lượng website đã xóa và danh sách website lỗi
   */
  @Transactional()
  async deleteMultipleWebsites(
    deleteDto: DeleteWebsitesDto,
    userId: number,
  ): Promise<{ deletedCount: number; errorWebsites?: string[] }> {
    try {
      const { websiteIds } = deleteDto;

      if (!websiteIds || websiteIds.length === 0) {
        return { deletedCount: 0 };
      }

      // Tìm tất cả website thuộc về người dùng
      const websites = await this.integrationWebsiteRepository.findByIdsAndUserId(websiteIds, userId);

      if (websites.length === 0) {
        return { deletedCount: 0 };
      }

      this.logger.log(
        `Tìm thấy ${websites.length} website cần xóa cho user ${userId}`,
      );

      const errorWebsites: string[] = [];
      let deletedCount = 0;

      // Xóa từng website
      for (const website of websites) {
        try {
          await this.integrationWebsiteRepository.remove(website);
          deletedCount++;
          this.logger.log(`Đã xóa website ${website.integrationName} (ID: ${website.id})`);
        } catch (error) {
          this.logger.error(`Lỗi khi xóa website ${website.integrationName}: ${error.message}`, error.stack);
          errorWebsites.push(website.integrationName);
        }
      }

      return {
        deletedCount,
        errorWebsites: errorWebsites.length > 0 ? errorWebsites : undefined,
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi xóa nhiều website: ${error.message}`,
        error.stack,
      );
      throw new AppException(INTEGRATION_ERROR_CODES.WEBSITE_DELETE_FAILED);
    }
  }

  /**
   * Cập nhật logo S3 key cho website sau khi upload thành công
   * @param websiteId ID của website
   * @param userId ID của người dùng
   * @param logoS3Key S3 key của logo đã upload
   */
  @Transactional()
  async updateWebsiteLogo(
    websiteId: string,
    userId: number,
    logoMime: string,
  ): Promise<{ id: string, logoUrlUpload: string }> {
    try {
      // Tìm website thuộc về người dùng
      const website = await this.integrationWebsiteRepository.findOne({
        where: { id: websiteId, userId }
      });

      if (!website) {
        throw new AppException(INTEGRATION_ERROR_CODES.WEBSITE_NOT_FOUND);
      }

      const metadata = website.metadata as WebsiteMetadata;

      let logoUrlUpload: string;
      if (metadata.logo) {
        logoUrlUpload = await this.s3Service.createPresignedWithID(
          metadata.logo,
          TimeIntervalEnum.FIVE_MINUTES,
          ImageType.getType(logoMime),
          FileSizeEnum.ONE_MB,
        );
      } else {
        // Tạo S3 key cho logo
        const logoS3Key = generateS3Key({
          baseFolder: userId.toString(),
          categoryFolder: CategoryFolderEnum.CHAT,
          useTimeFolder: true,
          prefix: `user_${userId}`,
        });

        // Tạo presigned URL
        logoUrlUpload = await this.s3Service.createPresignedWithID(
          logoS3Key,
          TimeIntervalEnum.FIVE_MINUTES,
          ImageType.getType(logoMime),
          FileSizeEnum.ONE_MB,
        );

        metadata.logo = logoS3Key;
        await this.integrationWebsiteRepository.update(websiteId, { metadata });
      }

      return {
        id: websiteId,
        logoUrlUpload,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(
        `Lỗi khi cập nhật logo website: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi cập nhật logo website',
      );
    }
  }

  /**
   * Lấy cấu hình box chat theo website ID
   * @param websiteId ID của website
   * @param userId ID của người dùng
   * @returns Cấu hình box chat
   */
  async getBoxChatConfigByWebsiteId(
    websiteId: string,
    userId: number,
  ): Promise<BoxChatConfigResponseDto> {
    try {
      // Kiểm tra website có thuộc về user không
      const website = await this.integrationWebsiteRepository.findOne({ where: { id: websiteId, userId } });
      if (!website) {
        throw new AppException(INTEGRATION_ERROR_CODES.WEBSITE_NOT_FOUND);
      }

      // Tìm cấu hình box chat
      const boxChatConfig =
        await this.boxChatConfigRepository.findByWebsiteId(websiteId);
      if (!boxChatConfig) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.BOX_CHAT_CONFIG_NOT_FOUND,
        );
      }

      // Lấy media banners từ bảng junction
      const mediaList = await this.boxChatConfigMediaRepository.findMediaByBoxChatId(boxChatConfig.id);

      // Chuyển đổi entity thành DTO
      const response: BoxChatConfigResponseDto = {
        id: boxChatConfig.id,
        integrationId: boxChatConfig.integrationId,
        welcomeText: boxChatConfig.welcomeText,
        displayMode: boxChatConfig.displayMode,
        sideMode: boxChatConfig.sideMode,
        colorPrimary: boxChatConfig.colorPrimary,
        avatar: boxChatConfig.avatar ? this.cdnService.generateUrlView(boxChatConfig.avatar, TimeIntervalEnum.ONE_DAY) : null,
        icon: boxChatConfig.icon ? this.cdnService.generateUrlView(boxChatConfig.icon, TimeIntervalEnum.ONE_DAY) : null,
        media: mediaList.map(item => ({
          id: item.media.id,
          name: item.media.name,
          storageKey: item.media.storageKey,
          size: item.media.size,
          mediaType: item.media.mediaType,
          status: item.media.status,
        })),
        components: boxChatConfig.components,
        quickMessages: boxChatConfig.quickMessages || [],
        placeholderMessage: boxChatConfig.placeholderMessage,
      };

      return response;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error('Lỗi khi lấy cấu hình box chat:', error);
      throw new AppException(INTEGRATION_ERROR_CODES.BOX_CHAT_CONFIG_NOT_FOUND);
    }
  }

  /**
   * Cập nhật cấu hình box chat
   * @param websiteId ID của website
   * @param userId ID của người dùng
   * @param updateDto Dữ liệu cập nhật
   * @returns Cấu hình box chat đã cập nhật
   */
  @Transactional()
  async updateBoxChatConfig(
    websiteId: string,
    userId: number,
    updateDto: UpdateBoxChatConfigDto,
  ): Promise<{ id: number, avatarUrlUpload: string | null, iconUrlUpload: string | null, bannerUrlUploads?: string[] }> {
    try {
      // Kiểm tra website có thuộc về user không
      const website = await this.integrationWebsiteRepository.findOne({ where: { id: websiteId, userId } });
      if (!website) {
        throw new AppException(INTEGRATION_ERROR_CODES.WEBSITE_NOT_FOUND);
      }

      // Tìm cấu hình box chat
      const boxChatConfig =
        await this.boxChatConfigRepository.findByWebsiteId(websiteId);
      if (!boxChatConfig) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.BOX_CHAT_CONFIG_NOT_FOUND,
        );
      }

      let avatarUrlUpload: string | null = null;
      let iconUrlUpload: string | null = null;
      let bannerUrlUploads: string[] = [];

      // Cập nhật các trường cơ bản
      if (updateDto.welcomeText !== undefined) {
        boxChatConfig.welcomeText = updateDto.welcomeText;
      }
      if (updateDto.displayMode !== undefined) {
        boxChatConfig.displayMode = updateDto.displayMode;
      }
      if (updateDto.sideMode !== undefined) {
        boxChatConfig.sideMode = updateDto.sideMode;
      }
      if (updateDto.colorPrimary !== undefined) {
        boxChatConfig.colorPrimary = updateDto.colorPrimary;
      }
      if (updateDto.components !== undefined) {
        boxChatConfig.components = updateDto.components;
      }
      if (updateDto.quickMessages !== undefined) {
        boxChatConfig.quickMessages = updateDto.quickMessages;
      }
      if (updateDto.placeholderMessage !== undefined) {
        boxChatConfig.placeholderMessage = updateDto.placeholderMessage;
      }
      if (updateDto.iconMime !== undefined) {
        if (boxChatConfig.icon) {
          // Nếu đã có icon, tạo presigned URL cho icon hiện tại
          iconUrlUpload = await this.s3Service.createPresignedWithID(
            boxChatConfig.icon,
            TimeIntervalEnum.FIVE_MINUTES,
            ImageType.getType(updateDto.iconMime),
            FileSizeEnum.FIVE_MB,
          );
        } else {
          // Nếu chưa có icon, tạo S3 key mới và presigned URL
          const iconKey = generateS3Key({
            baseFolder: userId.toString(),
            categoryFolder: CategoryFolderEnum.CHAT,
            useTimeFolder: true,
            prefix: `user_${userId}`,
          });

          iconUrlUpload = await this.s3Service.createPresignedWithID(
            iconKey,
            TimeIntervalEnum.FIVE_MINUTES,
            ImageType.getType(updateDto.iconMime),
            FileSizeEnum.FIVE_MB,
          );

          boxChatConfig.icon = iconKey;
        }
      }
      if (updateDto.avatarMime !== undefined) {
        if (boxChatConfig.avatar) {
          // Nếu đã có avatar, tạo presigned URL cho avatar hiện tại
          avatarUrlUpload = await this.s3Service.createPresignedWithID(
            boxChatConfig.avatar,
            TimeIntervalEnum.FIVE_MINUTES,
            ImageType.getType(updateDto.avatarMime),
            FileSizeEnum.FIVE_MB,
          );
        } else {
          // Nếu chưa có avatar, tạo S3 key mới và presigned URL
          const avatarKey = generateS3Key({
            baseFolder: userId.toString(),
            categoryFolder: CategoryFolderEnum.CHAT,
            useTimeFolder: true,
            prefix: `user_${userId}`,
          });

          avatarUrlUpload = await this.s3Service.createPresignedWithID(
            avatarKey,
            TimeIntervalEnum.FIVE_MINUTES,
            ImageType.getType(updateDto.avatarMime),
            FileSizeEnum.FIVE_MB,
          );

          boxChatConfig.avatar = avatarKey;
        }
      }

      // Xử lý cập nhật media banners
      if (updateDto.bannerMediaIds !== undefined) {
        // Xóa tất cả media cũ
        await this.boxChatConfigMediaRepository.removeAllMediaFromBoxChatConfig(boxChatConfig.id);

        // Thêm media mới nếu có
        if (updateDto.bannerMediaIds.length > 0) {
          for (const mediaId of updateDto.bannerMediaIds) {
            await this.boxChatConfigMediaRepository.addMediaToBoxChatConfig(boxChatConfig.id, mediaId);
          }
        }
      }

      // Xử lý tạo media mới từ bannerFiles
      if (updateDto.bannerFiles && updateDto.bannerFiles.length > 0) {
        bannerUrlUploads = await this.createBannerMediaFromFiles(
          updateDto.bannerFiles,
          userId,
          boxChatConfig.id
        );
      }

      await this.boxChatConfigRepository.update(
        boxChatConfig.id,
        boxChatConfig,
      );

      return {
        id: boxChatConfig.id,
        avatarUrlUpload,
        iconUrlUpload,
        ...(bannerUrlUploads.length > 0 && { bannerUrlUploads })
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error('Lỗi khi cập nhật cấu hình box chat:', error);
      throw new AppException(
        INTEGRATION_ERROR_CODES.BOX_CHAT_CONFIG_UPDATE_FAILED,
      );
    }
  }

  /**
   * Cập nhật avatar cho box chat config
   * @param websiteId ID của website
   * @param userId ID của người dùng
   * @param updateDto Dữ liệu cập nhật avatar
   * @returns Thông tin avatar đã cập nhật và presigned URL (nếu có)
   */
  @Transactional()
  async updateBoxChatAvatar(
    websiteId: string,
    userId: number,
    updateDto: UpdateBoxChatConfigDto,
  ): Promise<UpdateAvatarResponseDto> {
    try {
      // Kiểm tra website có thuộc về user không
      const website = await this.integrationWebsiteRepository.findOne({ where: { id: websiteId, userId } });
      if (!website) {
        throw new AppException(INTEGRATION_ERROR_CODES.WEBSITE_NOT_FOUND);
      }

      // Tìm cấu hình box chat
      const boxChatConfig =
        await this.boxChatConfigRepository.findByWebsiteId(websiteId);
      if (!boxChatConfig) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.BOX_CHAT_CONFIG_NOT_FOUND,
        );
      }

      let avatarUpload: string | null = null;

      // Logic cập nhật avatar theo yêu cầu
      if (updateDto.avatarMime !== undefined) {
        if (boxChatConfig.avatar) {
          // Nếu đã có avatar, tạo presigned URL cho avatar hiện tại
          avatarUpload = await this.s3Service.createPresignedWithID(
            boxChatConfig.avatar,
            TimeIntervalEnum.FIVE_MINUTES,
            ImageType.getType(updateDto.avatarMime),
            FileSizeEnum.FIVE_MB,
          );
        } else {
          // Nếu chưa có avatar, tạo S3 key mới và presigned URL
          const avatarKey = generateS3Key({
            baseFolder: userId.toString(),
            categoryFolder: CategoryFolderEnum.IMAGE,
          });

          avatarUpload = await this.s3Service.createPresignedWithID(
            avatarKey,
            TimeIntervalEnum.FIVE_MINUTES,
            ImageType.getType(updateDto.avatarMime),
            FileSizeEnum.FIVE_MB,
          );

          // Cập nhật avatar key vào database
          boxChatConfig.avatar = avatarKey;
        }
      }

      // Lưu cập nhật vào database
      await this.boxChatConfigRepository.save(boxChatConfig);

      const response: UpdateAvatarResponseDto = {
        id: boxChatConfig.id,
        avatar: boxChatConfig.avatar,
        avatarUploadUrl: avatarUpload,
        message: 'Cập nhật avatar thành công',
      };

      this.logger.log(
        `Avatar updated for box chat config ${boxChatConfig.id} by user ${userId}`,
      );
      return response;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error('Lỗi khi cập nhật avatar box chat:', error);
      throw new AppException(
        INTEGRATION_ERROR_CODES.BOX_CHAT_CONFIG_UPDATE_FAILED,
      );
    }
  }

  /**
   * Bật/tắt trạng thái website (đảo ngược giá trị hiện tại)
   * @param websiteId ID của website
   * @param userId ID của người dùng
   * @returns Thông tin website với trạng thái mới
   */
  @Transactional()
  async toggleWebsiteActive(
    websiteId: string,
    userId: number,
  ): Promise<ToggleWebsiteActiveResponseDto> {
    try {
      // Kiểm tra website có thuộc về user không
      const website = await this.integrationWebsiteRepository.findOne({ where: { id: websiteId, userId } });
      if (!website) {
        throw new AppException(INTEGRATION_ERROR_CODES.WEBSITE_NOT_FOUND);
      }

      // Đảo ngược trạng thái active
      const metadata = website.metadata as WebsiteMetadata;

      website.metadata = {
        ...metadata,
        active: !metadata?.active || false,
      };

      // Cập nhật trạng thái trong database
      await this.integrationWebsiteRepository.update(
        website.id,
        website,
      );

      const response: ToggleWebsiteActiveResponseDto = {
        id: websiteId,
        active: !metadata?.active || false,
        message: !metadata?.active
          ? 'Website đã được bật thành công'
          : 'Website đã được tắt thành công',
      };

      return response;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error('Lỗi khi bật/tắt website:', error);
      throw new AppException(INTEGRATION_ERROR_CODES.WEBSITE_UPDATE_FAILED);
    }
  }

  /**
   * Thêm media vào box chat config
   * @param websiteId ID của website
   * @param userId ID của người dùng
   * @param mediaIds Danh sách ID của media cần thêm
   * @returns Cấu hình box chat đã cập nhật
   */
  @Transactional()
  async addMediaToBoxChatConfig(
    websiteId: string,
    userId: number,
    mediaIds: string[],
  ): Promise<BoxChatConfigResponseDto> {
    try {
      // Kiểm tra website có thuộc về user không
      const website = await this.integrationWebsiteRepository.findOne({ where: { id: websiteId, userId } });
      if (!website) {
        throw new AppException(INTEGRATION_ERROR_CODES.WEBSITE_NOT_FOUND);
      }

      // Tìm cấu hình box chat
      const boxChatConfig = await this.boxChatConfigRepository.findByWebsiteId(websiteId);
      if (!boxChatConfig) {
        throw new AppException(INTEGRATION_ERROR_CODES.BOX_CHAT_CONFIG_NOT_FOUND);
      }

      // Thêm media vào box chat config
      for (const mediaId of mediaIds) {
        await this.boxChatConfigMediaRepository.addMediaToBoxChatConfig(boxChatConfig.id, mediaId);
      }

      // Trả về cấu hình đã cập nhật
      return this.getBoxChatConfigByWebsiteId(websiteId, userId);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error('Lỗi khi thêm media vào box chat config:', error);
      throw new AppException(INTEGRATION_ERROR_CODES.BOX_CHAT_CONFIG_UPDATE_FAILED);
    }
  }

  /**
   * Xóa media khỏi box chat config
   * @param websiteId ID của website
   * @param userId ID của người dùng
   * @param mediaIds Danh sách ID của media cần xóa
   * @returns Cấu hình box chat đã cập nhật
   */
  @Transactional()
  async removeMediaFromBoxChatConfig(
    websiteId: string,
    userId: number,
    mediaIds: string[],
  ): Promise<BoxChatConfigResponseDto> {
    try {
      // Kiểm tra website có thuộc về user không
      const website = await this.integrationWebsiteRepository.findOne({ where: { id: websiteId, userId } });
      if (!website) {
        throw new AppException(INTEGRATION_ERROR_CODES.WEBSITE_NOT_FOUND);
      }

      // Tìm cấu hình box chat
      const boxChatConfig = await this.boxChatConfigRepository.findByWebsiteId(websiteId);
      if (!boxChatConfig) {
        throw new AppException(INTEGRATION_ERROR_CODES.BOX_CHAT_CONFIG_NOT_FOUND);
      }

      // Xóa media khỏi box chat config
      for (const mediaId of mediaIds) {
        await this.boxChatConfigMediaRepository.removeMediaFromBoxChatConfig(boxChatConfig.id, mediaId);
      }

      // Trả về cấu hình đã cập nhật
      return this.getBoxChatConfigByWebsiteId(websiteId, userId);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error('Lỗi khi xóa media khỏi box chat config:', error);
      throw new AppException(INTEGRATION_ERROR_CODES.BOX_CHAT_CONFIG_UPDATE_FAILED);
    }
  }

  /**
   * Tạo media mới từ banner files và trả về presigned URLs
   * @param bannerFiles Danh sách thông tin banner files
   * @param userId ID của user
   * @param boxChatConfigId ID của box chat config
   * @returns Danh sách presigned URLs
   */
  private async createBannerMediaFromFiles(
    bannerFiles: Array<{ mimeType: string; size: number; fileName: string }>,
    userId: number,
    boxChatConfigId: number
  ): Promise<string[]> {
    const uploadUrls: string[] = [];

    for (let i = 0; i < bannerFiles.length; i++) {
      const bannerFile = bannerFiles[i];
      const { mimeType, size, fileName } = bannerFile;

      // Validate MIME type
      if (!['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/webp'].includes(mimeType)) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.BOX_CHAT_CONFIG_UPDATE_FAILED,
          `MIME type không hợp lệ: ${mimeType}`
        );
      }

      // Validate size
      if (size <= 0 || size > 5242880) { // 5MB
        throw new AppException(
          INTEGRATION_ERROR_CODES.BOX_CHAT_CONFIG_UPDATE_FAILED,
          `Kích thước file không hợp lệ: ${size} bytes`
        );
      }

      // Xử lý tên file
      const sanitizedFileName = this.sanitizeFileName(fileName);
      const extension = mimeType.split('/')[1];
      const finalFileName = sanitizedFileName || `banner_${Date.now()}_${i}.${extension}`;

      // Tạo S3 key
      const storageKey = generateS3Key({
        baseFolder: 'box-chat',
        categoryFolder: CategoryFolderEnum.IMAGE,
        fileName: finalFileName,
        prefix: `user_${userId}`,
        useTimeFolder: true,
      });

      // Tạo media entity
      const mediaEntity: Partial<Media> = {
        name: sanitizedFileName || `Box Chat Banner ${i + 1}`,
        description: `Banner cho box chat config ${boxChatConfigId}`,
        size: size, // Sử dụng size từ client
        ownedBy: userId,
        ownerType: OwnerTypeEnum.USER,
        status: MediaStatusEnum.PENDING,
        storageKey,
        mediaType: MediaTypeEnum.IMAGE,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      };

      // Lưu media entity
      const savedMedia = await this.mediaRepository.save(mediaEntity);

      // Tạo presigned URL
      const imageType = ImageType.getType(mimeType);
      const uploadUrl = await this.s3Service.createPresignedWithID(
        storageKey,
        TimeIntervalEnum.ONE_HOUR,
        imageType,
        FileSizeEnum.FIVE_MB,
      );

      uploadUrls.push(uploadUrl);

      // Thêm media vào box chat config
      await this.boxChatConfigMediaRepository.addMediaToBoxChatConfig(boxChatConfigId, savedMedia.id);
    }

    return uploadUrls;
  }

  /**
   * Làm sạch tên file để đảm bảo an toàn
   * @param fileName Tên file gốc
   * @returns Tên file đã được làm sạch
   */
  private sanitizeFileName(fileName: string): string {
    if (!fileName) return '';

    // Loại bỏ các ký tự không an toàn
    return fileName
      .replace(/[^a-zA-Z0-9._-]/g, '_') // Thay thế ký tự đặc biệt bằng _
      .replace(/_{2,}/g, '_') // Thay thế nhiều _ liên tiếp bằng 1 _
      .replace(/^_+|_+$/g, '') // Loại bỏ _ ở đầu và cuối
      .substring(0, 200); // Giới hạn độ dài
  }
}
