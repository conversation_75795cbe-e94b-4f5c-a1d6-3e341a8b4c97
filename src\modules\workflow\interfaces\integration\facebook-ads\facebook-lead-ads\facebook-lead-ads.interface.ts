/**
 * @file Facebook Lead Ads Integration Node Interface
 * 
 * <PERSON><PERSON><PERSON> nghĩa type-safe interface cho Facebook Lead Ads node operations
 * Theo patterns từ Make.com và n8n industry standards
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

import {
    IBaseIntegrationParameters,
    ITriggerParameters,
    IActionParameters,
    ISearchParameters,
    IBaseIntegrationInput,
    IBaseIntegrationOutput,
    EIntegrationOperationType,
    EIntegrationErrorHandling,
    IBaseIntegrationCredential
} from '../../base/base-integration.interface';

import {
    ECredentialName,
    ENodeAuthType,
    EPropertyType,
    ELoadOptionsResource,
    ELoadOptionsMethod,
    INodeProperty
} from '../../../node-manager.interface';

import {
    ITypedNodeExecution
} from '../../../execute.interface';

// =================================================================
// SECTION 1: FACEBOOK LEAD ADS ENUMS
// =================================================================

/**
 * Facebook Lead Ads specific operations
 */
export enum EFacebookLeadAdsOperation {
    /** Watch for new leads (webhook trigger) */
    NEW_LEAD = 'newLead',
    /** Get lead details by ID */
    GET_LEAD_DETAIL = 'getLeadDetail',
    /** Get form information */
    GET_FORM = 'getForm',
    /** Unsubscribe a webhook */
    UNSUBSCRIBE_WEBHOOK = 'unsubscribeWebhook',
    /** List leads with filters */
    LIST_LEADS = 'listLeads'
}

/**
 * Facebook Lead Form output fields
 */
export enum EFacebookLeadFormField {
    /** Allow organic lead */
    ALLOW_ORGANIC_LEAD = 'allow_organic_lead',
    /** Block display for non-targeted viewer */
    BLOCK_DISPLAY_FOR_NON_TARGETED_VIEWER = 'block_display_for_non_targeted_viewer',
    /** Context card */
    CONTEXT_CARD = 'context_card',
    /** Created time */
    CREATED_TIME = 'created_time',
    /** Expired leads count */
    EXPIRED_LEADS_COUNT = 'expired_leads_count',
    /** Follow-up action text */
    FOLLOWUP_ACTION_TEXT = 'followup_action_text',
    /** Follow-up action URL */
    FOLLOWUP_ACTION_URL = 'followup_action_url',
    /** Is optimized for quality */
    IS_OPTIMIZED_FOR_QUALITY = 'is_optimized_for_quality',
    /** Leads count */
    LEADS_COUNT = 'leads_count',
    /** Legal content */
    LEGAL_CONTENT = 'legal_content',
    /** Locale */
    LOCALE = 'locale',
    /** Name */
    NAME = 'name',
    /** Organic leads count */
    ORGANIC_LEADS_COUNT = 'organic_leads_count',
    /** Page */
    PAGE = 'page',
    /** Page ID */
    PAGE_ID = 'page_id',
    /** Privacy policy URL */
    PRIVACY_POLICY_URL = 'privacy_policy_url',
    /** Question page custom headline */
    QUESTION_PAGE_CUSTOM_HEADLINE = 'question_page_custom_headline',
    /** Questions */
    QUESTIONS = 'questions',
    /** Status */
    STATUS = 'status',
    /** Tracking parameters */
    TRACKING_PARAMETERS = 'tracking_parameters',
    /** Thank you page */
    THANK_YOU_PAGE = 'thank_you_page'
}

// =================================================================
// SECTION 2: FACEBOOK LEAD ADS PARAMETERS
// =================================================================

/**
 * New Lead webhook trigger parameters
 */
export interface INewLeadParameters extends ITriggerParameters {
    operation: EFacebookLeadAdsOperation.NEW_LEAD;

    /** Webhook name */
    webhook_name: string;

    /** Facebook Page ID */
    page_id: string;
}

/**
 * Get Lead Detail parameters
 */
export interface IGetLeadDetailParameters extends IActionParameters {
    operation: EFacebookLeadAdsOperation.GET_LEAD_DETAIL;

    /** Facebook Page ID */
    page_id: string;

    /** Lead ID */
    lead_id: string;
}

/**
 * Get Form parameters
 */
export interface IGetFormParameters extends IActionParameters {
    operation: EFacebookLeadAdsOperation.GET_FORM;

    /** Facebook Page ID */
    page_id: string;

    /** Form ID */
    form_id: string;

    /** Output fields selection */
    output_fields: 'all' | EFacebookLeadFormField[];
}

/**
 * Unsubscribe Webhook parameters
 */
export interface IUnsubscribeWebhookParameters extends IActionParameters {
    operation: EFacebookLeadAdsOperation.UNSUBSCRIBE_WEBHOOK;

    /** Facebook Page ID */
    page_id: string;

    /** Webhook ID to unsubscribe */
    webhook_id?: string;
}

/**
 * List Leads parameters
 */
export interface IListLeadsParameters extends ISearchParameters {
    operation: EFacebookLeadAdsOperation.LIST_LEADS;

    /** Facebook Page ID */
    page_id: string;

    /** Maximum number of leads to return */
    limit: number;

    /** Advanced settings */
    advanced_settings?: {
        /** Filter leads older than this date */
        older_than?: string;
    };
}

/**
 * Union type cho tất cả Facebook Lead Ads parameters
 */
export type IFacebookLeadAdsParameters = 
    | INewLeadParameters
    | IGetLeadDetailParameters
    | IGetFormParameters
    | IUnsubscribeWebhookParameters
    | IListLeadsParameters;

// =================================================================
// SECTION 3: INPUT/OUTPUT INTERFACES
// =================================================================

/**
 * Facebook Lead Ads input interface
 */
export interface IFacebookLeadAdsInput extends IBaseIntegrationInput {
    /** Facebook page data */
    page?: {
        id: string;
        name: string;
        access_token?: string;
    };

    /** Lead data for webhook triggers */
    lead?: {
        id?: string;
        form_id?: string;
        field_data?: Array<{
            name: string;
            values: string[];
        }>;
        created_time?: string;
    };

    /** Webhook data */
    webhook?: {
        id?: string;
        name?: string;
        callback_url?: string;
    };
}

/**
 * Facebook Lead Ads output interface
 */
export interface IFacebookLeadAdsOutput extends IBaseIntegrationOutput {
    /** Facebook Lead Ads specific data */
    facebook_lead_ads?: {
        lead_id?: string;
        form_id?: string;
        page_id?: string;
        webhook_id?: string;
        lead_data?: {
            field_data?: Array<{
                name: string;
                values: string[];
            }>;
            created_time?: string;
            id?: string;
        };
        form_data?: {
            id?: string;
            name?: string;
            status?: string;
            leads_count?: number;
            questions?: any[];
            [key: string]: any;
        };
        leads?: Array<{
            id: string;
            created_time: string;
            field_data: Array<{
                name: string;
                values: string[];
            }>;
        }>;
    };
}

// =================================================================
// SECTION 4: NODE PROPERTIES DEFINITION
// =================================================================

/**
 * Facebook Lead Ads node properties
 */
export const FACEBOOK_LEAD_ADS_PROPERTIES: INodeProperty[] = [
    {
        name: 'operation',
        displayName: 'Operation',
        type: EPropertyType.Options,
        required: true,
        options: [
            { name: 'New Lead (Webhook)', value: EFacebookLeadAdsOperation.NEW_LEAD },
            { name: 'Get Lead Detail', value: EFacebookLeadAdsOperation.GET_LEAD_DETAIL },
            { name: 'Get a Form', value: EFacebookLeadAdsOperation.GET_FORM },
            { name: 'Unsubscribe a Webhook', value: EFacebookLeadAdsOperation.UNSUBSCRIBE_WEBHOOK },
            { name: 'List Leads', value: EFacebookLeadAdsOperation.LIST_LEADS }
        ]
    },
    {
        name: 'webhook_name',
        displayName: 'Webhook Name',
        type: EPropertyType.String,
        required: true,
        displayOptions: {
            show: {
                operation: [EFacebookLeadAdsOperation.NEW_LEAD]
            }
        }
    },

    {
        name: 'page_id',
        displayName: 'Page',
        type: EPropertyType.Options,
        required: true,
        loadOptions: {
            resource: ELoadOptionsResource.INTEGRATIONS,
            method: ELoadOptionsMethod.GET_CONNECTED,
            dependsOn: ['integration_id']
        }
    },
    {
        name: 'lead_id',
        displayName: 'Lead ID',
        type: EPropertyType.String,
        required: true,
        displayOptions: {
            show: {
                operation: [EFacebookLeadAdsOperation.GET_LEAD_DETAIL]
            }
        }
    },
    {
        name: 'form_id',
        displayName: 'Form ID',
        type: EPropertyType.String,
        required: true,
        displayOptions: {
            show: {
                operation: [EFacebookLeadAdsOperation.GET_FORM]
            }
        }
    },
    {
        name: 'output_fields',
        displayName: 'Output Fields',
        type: EPropertyType.Options,
        required: false,
        default: 'all',
        options: [
            { name: 'Select All', value: 'all' },
            { name: 'Allow Organic Lead', value: EFacebookLeadFormField.ALLOW_ORGANIC_LEAD },
            { name: 'Block Display for Non-targeted Viewer', value: EFacebookLeadFormField.BLOCK_DISPLAY_FOR_NON_TARGETED_VIEWER },
            { name: 'Context Card', value: EFacebookLeadFormField.CONTEXT_CARD },
            { name: 'Created Time', value: EFacebookLeadFormField.CREATED_TIME },
            { name: 'Expired Leads Count', value: EFacebookLeadFormField.EXPIRED_LEADS_COUNT },
            { name: 'Follow-up Action Text', value: EFacebookLeadFormField.FOLLOWUP_ACTION_TEXT },
            { name: 'Follow-up Action URL', value: EFacebookLeadFormField.FOLLOWUP_ACTION_URL },
            { name: 'Is Optimized for Quality', value: EFacebookLeadFormField.IS_OPTIMIZED_FOR_QUALITY },
            { name: 'Leads Count', value: EFacebookLeadFormField.LEADS_COUNT },
            { name: 'Legal Content', value: EFacebookLeadFormField.LEGAL_CONTENT },
            { name: 'Locale', value: EFacebookLeadFormField.LOCALE },
            { name: 'Name', value: EFacebookLeadFormField.NAME },
            { name: 'Organic Leads Count', value: EFacebookLeadFormField.ORGANIC_LEADS_COUNT },
            { name: 'Page', value: EFacebookLeadFormField.PAGE },
            { name: 'Page ID', value: EFacebookLeadFormField.PAGE_ID },
            { name: 'Privacy Policy URL', value: EFacebookLeadFormField.PRIVACY_POLICY_URL },
            { name: 'Question Page Custom Headline', value: EFacebookLeadFormField.QUESTION_PAGE_CUSTOM_HEADLINE },
            { name: 'Questions', value: EFacebookLeadFormField.QUESTIONS },
            { name: 'Status', value: EFacebookLeadFormField.STATUS },
            { name: 'Tracking Parameters', value: EFacebookLeadFormField.TRACKING_PARAMETERS },
            { name: 'Thank You Page', value: EFacebookLeadFormField.THANK_YOU_PAGE }
        ],
        multipleValues: true,
        displayOptions: {
            show: {
                operation: [EFacebookLeadAdsOperation.GET_FORM]
            }
        }
    },
    {
        name: 'webhook_id',
        displayName: 'Webhook ID',
        type: EPropertyType.String,
        required: false,
        displayOptions: {
            show: {
                operation: [EFacebookLeadAdsOperation.UNSUBSCRIBE_WEBHOOK]
            }
        }
    },
    {
        name: 'limit',
        displayName: 'Limit',
        type: EPropertyType.Number,
        required: true,
        default: 25,
        minValue: 1,
        maxValue: 100,
        displayOptions: {
            show: {
                operation: [EFacebookLeadAdsOperation.LIST_LEADS]
            }
        }
    },
    {
        name: 'older_than',
        displayName: 'Older Than',
        type: EPropertyType.DateTime,
        required: false,
        description: 'Filter leads older than this date',
        displayOptions: {
            show: {
                operation: [EFacebookLeadAdsOperation.LIST_LEADS]
            }
        }
    }
];

// =================================================================
// SECTION 5: CREDENTIAL DEFINITION
// =================================================================

/**
 * Facebook Lead Ads credential definition
 */
export const FACEBOOK_LEAD_ADS_CREDENTIAL: IBaseIntegrationCredential = {
    provider: 'facebook',
    name: ECredentialName.FACEBOOK_OAUTH,
    displayName: 'Facebook Lead Ads OAuth2',
    description: 'OAuth2 authentication for Facebook Lead Ads',
    required: true,
    authType: ENodeAuthType.OAUTH2,
    testable: true,
    testUrl: '/api/integrations/test-connection'
};

// =================================================================
// SECTION 6: VALIDATION FUNCTIONS
// =================================================================

/**
 * Validate Facebook Lead Ads parameters
 */
export function validateFacebookLeadAdsParameters(
    params: Partial<IFacebookLeadAdsParameters>
): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check required fields
    if (!params.operation) {
        errors.push('Operation is required');
    }

    if (!params.page_id) {
        errors.push('Page is required');
    }

    // Operation specific validation
    switch (params.operation) {
        case EFacebookLeadAdsOperation.NEW_LEAD:
            const newLeadParams = params as INewLeadParameters;
            if (!newLeadParams.webhook_name) {
                errors.push('Webhook name is required for New Lead operation');
            }
            break;

        case EFacebookLeadAdsOperation.GET_LEAD_DETAIL:
            const getLeadParams = params as IGetLeadDetailParameters;
            if (!getLeadParams.lead_id) {
                errors.push('Lead ID is required for Get Lead Detail operation');
            }
            break;

        case EFacebookLeadAdsOperation.GET_FORM:
            const getFormParams = params as IGetFormParameters;
            if (!getFormParams.form_id) {
                errors.push('Form ID is required for Get Form operation');
            }
            break;

        case EFacebookLeadAdsOperation.LIST_LEADS:
            const listLeadsParams = params as IListLeadsParameters;
            if (!listLeadsParams.limit) {
                errors.push('Limit is required for List Leads operation');
            }
            if (listLeadsParams.limit && (listLeadsParams.limit < 1 || listLeadsParams.limit > 100)) {
                errors.push('Limit must be between 1 and 100');
            }
            break;
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}

/**
 * Type guard cho Facebook Lead Ads parameters
 */
export function isFacebookLeadAdsParameters(params: any): params is IFacebookLeadAdsParameters {
    return params && Object.values(EFacebookLeadAdsOperation).includes(params.operation);
}

/**
 * Type-safe node execution cho Facebook Lead Ads
 */
export type IFacebookLeadAdsNodeExecution = ITypedNodeExecution<
    IFacebookLeadAdsInput,
    IFacebookLeadAdsOutput,
    IFacebookLeadAdsParameters
>;
