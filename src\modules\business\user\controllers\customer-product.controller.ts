import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { CustomerProductService } from '../services/product/customer-product.service';
import { CompletePhysicalProductService } from '../services/product/complete-physical-product.service';
import { CustomerProductImportService } from '../services/product/customer-product-import.service';
import { CompleteDigitalProductService } from '../services/product/complete-digital-product.service';
import { CompleteServiceProductService } from '../services/product/complete-service-product.service';
import { CompleteEventProductService } from '../services/product/complete-event-product.service';
import { CompleteComboProductService } from '../services/product/complete-combo-product.service';
import {
  CreateCustomerProductDto,
  UpdateCustomerProductDto,
  QueryCustomerProductDto,
  CustomerProductResponseDto,
  BulkDeleteCustomerProductDto,
  BulkDeleteCustomerProductResponseDto,
  BulkCreateCustomerProductDto,
  BulkCreateCustomerProductResponseDto,
  ImportCustomerProductsDto,
  ImportCustomerProductsResponseDto,
  GetCustomerProductsByIdsDto,
} from '../dto/customer-product';

import { CompletePhysicalProductResponseDto } from '../dto/physical-product/complete-physical-product-response.dto';
import { CompleteDigitalProductResponseDto } from '../dto/digital-product/complete-digital-product-response.dto';
import { CompleteServiceProductResponseDto } from '../dto/service-product/complete-service-product-response.dto';
import { CompleteEventProductResponseDto } from '../dto/event-product/complete-event-product-response.dto';
import { CompleteComboProductResponseDto } from '../dto/combo-product/complete-combo-product-response.dto';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { ProductTypeEnum } from '@modules/business/enums';
import { AppException } from '@/common/exceptions';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';

/**
 * Controller xử lý các request liên quan đến sản phẩm khách hàng
 */
@ApiTags(SWAGGER_API_TAGS.USER_BUSINESS_CUSTOMER_PRODUCT)
@Controller('user/customer-products')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(
  ApiResponseDto,
  PaginatedResult,
  CustomerProductResponseDto,
  CreateCustomerProductDto,
  UpdateCustomerProductDto,
  QueryCustomerProductDto,
  BulkDeleteCustomerProductDto,
  BulkDeleteCustomerProductResponseDto,
  BulkCreateCustomerProductDto,
  BulkCreateCustomerProductResponseDto,
  ImportCustomerProductsResponseDto,
  CompletePhysicalProductResponseDto,
  CompleteDigitalProductResponseDto,
  CompleteServiceProductResponseDto,
  CompleteEventProductResponseDto,
  CompleteComboProductResponseDto,
)
export class CustomerProductController {
  constructor(
    private readonly customerProductService: CustomerProductService,
    private readonly completePhysicalProductService: CompletePhysicalProductService,
    private readonly completeDigitalProductService: CompleteDigitalProductService,
    private readonly completeServiceProductService: CompleteServiceProductService,
    private readonly completeEventProductService: CompleteEventProductService,
    private readonly completeComboProductService: CompleteComboProductService,
    private readonly customerProductImportService: CustomerProductImportService,
  ) {}

  /**
   * Tạo sản phẩm khách hàng mới
   * @param createDto DTO chứa thông tin sản phẩm mới
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin sản phẩm đã tạo
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Tạo sản phẩm khách hàng mới',
    description:
      'Tạo sản phẩm khách hàng mới với thông tin cơ bản. Sản phẩm sẽ có trạng thái PENDING (chờ duyệt) sau khi tạo.',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Sản phẩm đã được tạo thành công',
    type: () => ApiResponseDto.getSchema(CustomerProductResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  async create(
    @Body() createDto: CreateCustomerProductDto,
    @CurrentUser('id') userId: number,
  ) {
    const product = await this.customerProductService.create(createDto, userId);
    return ApiResponseDto.created<CustomerProductResponseDto>(
      product,
      'Tạo sản phẩm khách hàng thành công',
    );
  }

  /**
   * Lấy danh sách sản phẩm khách hàng
   * @param queryDto DTO chứa các tham số truy vấn
   * @param userId ID của người dùng hiện tại
   * @returns Danh sách sản phẩm với phân trang
   */
  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Lấy danh sách sản phẩm khách hàng',
    description:
      'Lấy danh sách sản phẩm khách hàng của người dùng hiện tại với phân trang và filter. Tự động tính quantity cho từng loại sản phẩm: PHYSICAL (từ variants), DIGITAL (từ versions), SERVICE (từ service packages), EVENT (từ tickets), COMBO (từ inventory). Có thể loại bỏ sản phẩm COMBO bằng tham số excludeCombo=true. Hỗ trợ filter theo danh sách ID sản phẩm cụ thể.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách sản phẩm khách hàng với thông tin tồn kho',
    type: () => ApiResponseDto.getPaginatedSchema(CustomerProductResponseDto),
    examples: {
      'Danh sách sản phẩm': {
        summary: 'Danh sách sản phẩm khách hàng với quantity',
        value: {
          code: 200,
          message: 'Lấy danh sách sản phẩm khách hàng thành công',
          result: {
            items: [
              {
                id: 51,
                userId: 1,
                name: 'Quần tây',
                description: 'Quần tây',
                productType: 'PHYSICAL',
                typePrice: 'HAS_PRICE',
                price: {
                  currency: 'VND',
                  listPrice: 9999,
                  salePrice: 2000,
                },
                status: 'APPROVED',
                createdAt: 1751539791681,
                updatedAt: 1751540310497,
                quantity: 150,
                customFields: [],
              },
              {
                id: 52,
                userId: 1,
                name: 'Áo thun nam đa màu',
                description: 'Áo thun nam có nhiều variant',
                productType: 'PHYSICAL',
                typePrice: 'HAS_PRICE',
                price: {
                  currency: 'VND',
                  listPrice: 500000,
                  salePrice: 450000,
                },
                status: 'APPROVED',
                createdAt: 1751539791681,
                updatedAt: 1751540310497,
                quantity: 80,
                customFields: [],
              },
              {
                id: 53,
                userId: 1,
                name: 'Dịch vụ chăm sóc da',
                description: 'Dịch vụ chăm sóc da chuyên nghiệp',
                productType: 'SERVICE',
                typePrice: 'HAS_PRICE',
                price: {
                  currency: 'VND',
                  listPrice: 1000000,
                  salePrice: 900000,
                },
                status: 'APPROVED',
                createdAt: 1751539791681,
                updatedAt: 1751540310497,
                quantity: 25,
                customFields: [],
              },
            ],
            meta: {
              total: 3,
              page: 1,
              limit: 10,
              totalPages: 1,
            },
          },
        },
      },
    },
  })
  async findAll(
    @Query() queryDto: QueryCustomerProductDto,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<PaginatedResult<CustomerProductResponseDto>>> {
    const products = await this.customerProductService.findAll(
      queryDto,
      userId,
    );
    return ApiResponseDto.success<PaginatedResult<CustomerProductResponseDto>>(
      products,
      'Lấy danh sách sản phẩm khách hàng thành công',
    );
  }

  /**
   * Lấy chi tiết sản phẩm khách hàng theo ID
   * @param id ID của sản phẩm
   * @param userId ID của người dùng hiện tại
   * @returns Chi tiết sản phẩm
   */
  @Get(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Lấy chi tiết sản phẩm khách hàng',
    description:
      'Lấy thông tin chi tiết đầy đủ của một sản phẩm khách hàng theo ID, bao gồm thông tin tồn kho từ product_inventory. Sản phẩm vật lý sử dụng variantId, sản phẩm số sử dụng versionId.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của sản phẩm khách hàng',
    type: Number,
    example: 123,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description:
      'Chi tiết đầy đủ sản phẩm khách hàng - Response sẽ khác nhau tùy theo productType. Sản phẩm vật lý và sản phẩm số đều bao gồm thông tin tồn kho chi tiết từ product_inventory với variantId/versionId tương ứng.',
    schema: {
      oneOf: [
        { $ref: '#/components/schemas/CompletePhysicalProductResponseDto' },
        { $ref: '#/components/schemas/CompleteDigitalProductResponseDto' },
        { $ref: '#/components/schemas/CompleteServiceProductResponseDto' },
        { $ref: '#/components/schemas/CompleteEventProductResponseDto' },
        { $ref: '#/components/schemas/CompleteComboProductResponseDto' },
      ],
    },
    examples: {
      'Sản phẩm vật lý có variant': {
        summary:
          'Response cho sản phẩm vật lý có variant - Bao gồm variants và thông tin tồn kho chi tiết',
        value: {
          code: 200,
          message: 'Lấy chi tiết sản phẩm khách hàng thành công',
          result: {
            id: 123,
            userId: 456,
            name: 'Áo thun nam đa màu sắc',
            description:
              'Áo thun nam chất liệu cotton 100%, có nhiều màu và size',
            productType: 'PHYSICAL',
            typePrice: 'HAS_PRICE',
            price: {
              listPrice: 500000,
              salePrice: 450000,
              currency: 'VND',
            },
            tags: ['thời trang', 'nam', 'cotton', 'đa màu'],
            status: 'ACTIVE',
            createdAt: '2024-01-15T08:00:00.000Z',
            updatedAt: '2024-01-15T10:30:00.000Z',
            stockQuantity: 105,
            quantity: 80,
            sku: 'SHIRT-MULTI-001',
            barcode: '1234567890456',
            shipmentConfig: {
              widthCm: 25,
              heightCm: 5,
              lengthCm: 30,
              weightGram: 200,
            },
            customFields: [
              {
                customFieldId: 1,
                label: 'Màu sắc',
                value: 'Đa màu',
                configJson: {
                  type: 'select',
                  options: ['Đỏ', 'Xanh', 'Vàng'],
                },
              },
            ],
            images: [
              {
                id: 789,
                key: 'products/shirt-multi-001.jpg',
                url: 'https://cdn.redai.vn/products/shirt-multi-001.jpg',
                position: 1,
              },
            ],
            variants: [
              {
                id: 1,
                name: 'Áo thun đỏ size M',
                description: 'Áo thun màu đỏ, size M',
                sku: 'SHIRT-RED-M-001',
                barcode: '1234567890001',
                attributes: {
                  color: 'Đỏ',
                  size: 'M',
                },
                price: {
                  listPrice: 300000,
                  salePrice: 250000,
                  currency: 'VND',
                },
                shipmentConfig: {
                  widthCm: 20,
                  heightCm: 3,
                  lengthCm: 25,
                  weightGram: 150,
                },
                images: [
                  {
                    id: 790,
                    key: 'variants/shirt-red-m.jpg',
                    url: 'https://cdn.redai.vn/variants/shirt-red-m.jpg',
                    position: 1,
                  },
                ],
                createdAt: '2024-01-15T08:00:00.000Z',
                updatedAt: '2024-01-15T10:30:00.000Z',
              },
              {
                id: 2,
                name: 'Áo thun xanh size L',
                description: 'Áo thun màu xanh, size L',
                sku: 'SHIRT-BLUE-L-001',
                barcode: '1234567890002',
                attributes: {
                  color: 'Xanh',
                  size: 'L',
                },
                price: {
                  listPrice: 320000,
                  salePrice: 270000,
                  currency: 'VND',
                },
                shipmentConfig: {
                  widthCm: 22,
                  heightCm: 3,
                  lengthCm: 27,
                  weightGram: 160,
                },
                images: [
                  {
                    id: 791,
                    key: 'variants/shirt-blue-l.jpg',
                    url: 'https://cdn.redai.vn/variants/shirt-blue-l.jpg',
                    position: 1,
                  },
                ],
                createdAt: '2024-01-15T08:00:00.000Z',
                updatedAt: '2024-01-15T10:30:00.000Z',
              },
            ],
            inventories: [
              {
                id: 123,
                productId: 123,
                variantId: 1,
                quantity: 105,
                variantQuantity: 30,
                updatedAt: '2024-01-15T10:30:00.000Z',
              },
              {
                id: 124,
                productId: 123,
                variantId: 2,
                quantity: 105,
                variantQuantity: 50,
                updatedAt: '2024-01-15T10:30:00.000Z',
              },
            ],
          },
        },
      },
      'Sản phẩm vật lý không có variant': {
        summary:
          'Response cho sản phẩm vật lý không có variant - Sản phẩm đơn giản với tồn kho tổng',
        value: {
          code: 200,
          message: 'Lấy chi tiết sản phẩm khách hàng thành công',
          result: {
            id: 456,
            userId: 789,
            name: 'Áo thun nam đơn giản',
            description: 'Áo thun nam chất liệu cotton, màu trắng',
            productType: 'PHYSICAL',
            typePrice: 'HAS_PRICE',
            price: {
              listPrice: 200000,
              salePrice: 180000,
              currency: 'VND',
            },
            tags: ['thời trang', 'nam', 'cotton'],
            status: 'ACTIVE',
            createdAt: '2024-01-15T08:00:00.000Z',
            updatedAt: '2024-01-15T10:30:00.000Z',
            stockQuantity: 100,
            quantity: 100,
            sku: 'SHIRT-SIMPLE-001',
            barcode: '1234567890123',
            shipmentConfig: {
              widthCm: 25,
              heightCm: 5,
              lengthCm: 30,
              weightGram: 200,
            },
            customFields: [],
            images: [
              {
                id: 792,
                key: 'products/shirt-simple-001.jpg',
                url: 'https://cdn.redai.vn/products/shirt-simple-001.jpg',
                position: 1,
              },
            ],
            variants: [],
            inventories: [
              {
                id: 125,
                productId: 456,
                variantId: null,
                quantity: 100,
                variantQuantity: 0,
                updatedAt: '2024-01-15T10:30:00.000Z',
              },
            ],
          },
        },
      },
      'Sản phẩm số với versions': {
        summary:
          'Response cho sản phẩm số với versions - Bao gồm thông tin tồn kho theo versionId từ product_inventory',
        value: {
          code: 200,
          message: 'Lấy chi tiết sản phẩm khách hàng thành công',
          result: {
            id: 789,
            userId: 123,
            name: 'Khóa học lập trình Python cơ bản',
            description:
              'Khóa học lập trình Python từ cơ bản đến nâng cao với nhiều bài tập thực hành',
            productType: 'DIGITAL',
            typePrice: 'HAS_PRICE',
            price: {
              listPrice: 500000,
              salePrice: 450000,
              currency: 'VND',
            },
            tags: ['khóa học', 'lập trình', 'python', 'online'],
            status: 'ACTIVE',
            createdAt: '2024-01-15T08:00:00.000Z',
            updatedAt: '2024-01-15T10:30:00.000Z',
            quantity: 800,
            digitalInfo: {
              deliveryMethod: 'Tải xuống trực tiếp',
              deliveryTime: 'Ngay lập tức sau khi thanh toán',
              waitingTime: 'Xử lý trong 24h',
              digitalProductType: 'ACCESS_CODE',
              accessLink: 'https://course.example.com/activate',
              usageInstruction: 'Sử dụng mã code để kích hoạt khóa học',
            },
            customFields: [
              {
                customFieldId: 1,
                label: 'Ngôn ngữ',
                value: 'Tiếng Việt',
              },
              {
                customFieldId: 2,
                label: 'Định dạng',
                value: 'PDF',
              },
            ],
            images: [
              {
                id: 793,
                key: 'products/python-course-001.jpg',
                url: 'https://cdn.redai.vn/products/python-course-001.jpg',
                position: 1,
              },
            ],
            versions: [
              {
                id: 101,
                digitalProductId: 789,
                versionName: 'Bản tiêu chuẩn',
                description: 'Phiên bản cơ bản với đầy đủ tính năng chính',
                customFields: [
                  {
                    customFieldId: 1,
                    label: 'Ngôn ngữ',
                    value: 'Tiếng Việt',
                  },
                ],
                contentLink: 'https://example.com/download/course-v1.pdf',
                sku: 'COURSE-STD-V1',
                minQuantity: 1,
                maxQuantity: 1,
                quantity: 300,
                price: {
                  listPrice: 300000,
                  salePrice: 250000,
                  currency: 'VND',
                },
                images: [
                  {
                    id: 794,
                    key: 'versions/course-std-v1.jpg',
                    url: 'https://cdn.redai.vn/versions/course-std-v1.jpg',
                    position: 1,
                  },
                ],
              },
              {
                id: 102,
                digitalProductId: 789,
                versionName: 'Bản nâng cao',
                description: 'Phiên bản nâng cao với nhiều tính năng bổ sung',
                customFields: [
                  {
                    customFieldId: 1,
                    label: 'Ngôn ngữ',
                    value: 'Tiếng Việt',
                  },
                ],
                contentLink: 'https://example.com/download/course-v2.pdf',
                sku: 'COURSE-ADV-V2',
                minQuantity: 1,
                maxQuantity: 1,
                quantity: 500,
                price: {
                  listPrice: 500000,
                  salePrice: 450000,
                  currency: 'VND',
                },
                images: [
                  {
                    id: 795,
                    key: 'versions/course-adv-v2.jpg',
                    url: 'https://cdn.redai.vn/versions/course-adv-v2.jpg',
                    position: 1,
                  },
                ],
              },
            ],
            inventories: [
              {
                id: 201,
                variantId: null,
                versionId: 101,
                quantity: 300,
                variantQuantity: 0,
                updatedAt: 1705312200000,
              },
              {
                id: 202,
                variantId: null,
                versionId: 102,
                quantity: 500,
                variantQuantity: 0,
                updatedAt: 1705312200000,
              },
            ],
          },
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy sản phẩm',
  })
  async findById(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number,
  ) {
    // Lấy thông tin cơ bản để xác định productType
    const basicProduct = await this.customerProductService.findById(id, userId);

    // Route đến service chuyên biệt dựa trên productType
    let product: any;
    switch (basicProduct.productType) {
      case ProductTypeEnum.PHYSICAL:
        product =
          await this.completePhysicalProductService.getCompletePhysicalProduct(
            id,
            userId,
          );
        break;
      case ProductTypeEnum.DIGITAL:
        product =
          await this.completeDigitalProductService.getCompleteDigitalProduct(
            id,
            userId,
          );
        break;
      case ProductTypeEnum.SERVICE:
        product =
          await this.completeServiceProductService.getCompleteServiceProduct(
            id,
            userId,
          );
        break;
      case ProductTypeEnum.EVENT:
        product =
          await this.completeEventProductService.getCompleteEventProduct(
            id,
            userId,
          );
        break;
      case ProductTypeEnum.COMBO:
        product =
          await this.completeComboProductService.getCompleteComboProduct(
            id,
            userId,
          );
        break;
      default:
        // Fallback to old method
        product = await this.customerProductService.findByIdComplete(
          id,
          userId,
        );
    }

    return ApiResponseDto.success(
      product,
      'Lấy chi tiết sản phẩm khách hàng thành công',
    );
  }

  /**
   * Lấy danh sách sản phẩm khách hàng theo nhiều ID
   * @param getByIdsDto DTO chứa danh sách ID sản phẩm
   * @param userId ID của người dùng hiện tại
   * @returns Danh sách sản phẩm khách hàng
   */
  @Get('search/by-ids')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Lấy danh sách sản phẩm khách hàng theo nhiều ID',
    description:
      'Lấy thông tin chi tiết của nhiều sản phẩm khách hàng cùng lúc theo danh sách ID. ' +
      'API được tối ưu để tránh lỗi N+1 query. Chỉ trả về sản phẩm của người dùng hiện tại và có trạng thái APPROVED.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách sản phẩm khách hàng',
    type: [CustomerProductResponseDto],
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  async findByIds(
    @Query() getByIdsDto: GetCustomerProductsByIdsDto,
    @CurrentUser('id') userId: number,
  ) {
    // Parse string IDs thành array numbers
    const ids = getByIdsDto.ids
      .split(',')
      .map((id) => parseInt(id.trim(), 10))
      .filter((id) => !isNaN(id) && id > 0);

    // Validation
    if (ids.length === 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVALID_INPUT,
        'Danh sách ID không được rỗng hoặc không hợp lệ',
      );
    }

    if (ids.length > 100) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVALID_INPUT,
        'Không thể lấy quá 100 sản phẩm cùng lúc',
      );
    }

    // Lấy thông tin cơ bản để xác định productType
    const basicProducts = await this.customerProductService.findByIds(
      ids,
      userId,
    );

    // Lấy chi tiết đầy đủ cho từng sản phẩm dựa trên productType
    const detailedProducts: any[] = [];

    for (const basicProduct of basicProducts) {
      let product: any;

      switch (basicProduct.productType) {
        case ProductTypeEnum.PHYSICAL:
          product =
            await this.completePhysicalProductService.getCompletePhysicalProduct(
              basicProduct.id,
              userId,
            );
          break;
        case ProductTypeEnum.DIGITAL:
          product =
            await this.completeDigitalProductService.getCompleteDigitalProduct(
              basicProduct.id,
              userId,
            );
          break;
        case ProductTypeEnum.SERVICE:
          product =
            await this.completeServiceProductService.getCompleteServiceProduct(
              basicProduct.id,
              userId,
            );
          break;
        case ProductTypeEnum.EVENT:
          product =
            await this.completeEventProductService.getCompleteEventProduct(
              basicProduct.id,
              userId,
            );
          break;
        case ProductTypeEnum.COMBO:
          product =
            await this.completeComboProductService.getCompleteComboProduct(
              basicProduct.id,
              userId,
            );
          break;
        default:
          // Fallback to old method
          product = await this.customerProductService.findByIdComplete(
            basicProduct.id,
            userId,
          );
      }

      detailedProducts.push(product);
    }

    return ApiResponseDto.success(
      detailedProducts,
      `Lấy thành công ${detailedProducts.length}/${ids.length} sản phẩm khách hàng`,
    );
  }

  /**
   * Cập nhật sản phẩm khách hàng
   * @param id ID của sản phẩm cần cập nhật
   * @param updateDto DTO chứa thông tin cập nhật
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin sản phẩm đã cập nhật
   */
  @Put(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Cập nhật sản phẩm khách hàng',
    description:
      'Cập nhật thông tin sản phẩm khách hàng. Chỉ có thể cập nhật sản phẩm của chính mình.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của sản phẩm khách hàng',
    type: Number,
    example: 123,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Sản phẩm đã được cập nhật thành công',
    type: () => ApiResponseDto.getSchema(CustomerProductResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy sản phẩm',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateCustomerProductDto,
    @CurrentUser('id') userId: number,
  ) {
    const product = await this.customerProductService.update(
      id,
      updateDto,
      userId,
    );
    return ApiResponseDto.success<CustomerProductResponseDto>(
      product,
      'Cập nhật sản phẩm khách hàng thành công',
    );
  }

  /**
   * Xóa sản phẩm khách hàng (soft delete)
   * @param id ID của sản phẩm cần xóa
   * @param userId ID của người dùng hiện tại
   * @returns Thông báo xóa thành công
   */
  @Delete(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Xóa sản phẩm khách hàng',
    description:
      'Xóa mềm sản phẩm khách hàng (chuyển trạng thái thành DELETED). Chỉ có thể xóa sản phẩm của chính mình.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của sản phẩm khách hàng',
    type: Number,
    example: 123,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Sản phẩm đã được xóa thành công',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy sản phẩm',
  })
  async delete(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number,
  ) {
    await this.customerProductService.delete(id, userId);
    return ApiResponseDto.success(null, 'Xóa sản phẩm khách hàng thành công');
  }

  /**
   * Tạo nhiều sản phẩm khách hàng cùng lúc
   * @param bulkCreateDto DTO chứa danh sách sản phẩm cần tạo
   * @param userId ID của người dùng hiện tại
   * @returns Kết quả tạo nhiều sản phẩm (job ID để tracking)
   */
  @Post('bulk')
  @HttpCode(HttpStatus.ACCEPTED)
  @ApiOperation({
    summary: 'Tạo nhiều sản phẩm khách hàng cùng lúc',
    description:
      'Tạo nhiều sản phẩm khách hàng cùng lúc. Tối đa 200 sản phẩm mỗi lần. Xử lý qua queue để tối ưu performance.',
  })
  @ApiResponse({
    status: HttpStatus.ACCEPTED,
    description: 'Job tạo sản phẩm đã được thêm vào queue thành công',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: {
          type: 'string',
          example: 'Job tạo sản phẩm đã được thêm vào queue',
        },
        result: {
          type: 'object',
          properties: {
            jobId: {
              type: 'string',
              example: 'job_1704067200_bulk_create_products_user_1',
              description: 'ID của job trong queue system để tracking',
            },
            message: {
              type: 'string',
              example: 'Job tạo sản phẩm đã được thêm vào queue',
              description: 'Thông báo xác nhận',
            },
            totalRequested: {
              type: 'number',
              example: 150,
              description: 'Tổng số sản phẩm được yêu cầu tạo',
            },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 400 },
        message: { type: 'string', example: 'Validation failed' },
        errors: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              field: { type: 'string', example: 'products' },
              message: {
                type: 'string',
                example: 'Tối đa 200 sản phẩm mỗi request',
              },
            },
          },
          example: [
            {
              field: 'products',
              message: 'Tối đa 200 sản phẩm mỗi request',
            },
            {
              field: 'products[0].name',
              message: 'Tên sản phẩm không được để trống',
            },
            {
              field: 'products',
              message:
                'Tất cả sản phẩm phải cùng loại. Tìm thấy: PHYSICAL, DIGITAL',
            },
          ],
        },
      },
    },
  })
  async bulkCreate(
    @Body() bulkCreateDto: BulkCreateCustomerProductDto,
    @CurrentUser('id') userId: number,
  ) {
    const result = await this.customerProductService.bulkCreate(
      bulkCreateDto,
      userId,
    );
    return ApiResponseDto.success(
      result,
      'Job tạo sản phẩm đã được thêm vào queue',
    );
  }

  /**
   * Import sản phẩm khách hàng từ file Excel/CSV/Word
   * @param file File upload chứa danh sách sản phẩm
   * @param userId ID của người dùng hiện tại
   * @param productType Loại sản phẩm (optional)
   * @returns Kết quả import với danh sách success/failed
   */
  @Post('import')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Import sản phẩm khách hàng từ file',
    description: `
    Import nhiều sản phẩm khách hàng từ file Excel/CSV/Word.
    - Hỗ trợ định dạng: .xlsx, .xls, .csv, .docx, .doc
    - Kích thước file tối đa: 10MB
    - Xử lý ngay lập tức, không qua queue
    - Trả về kết quả chi tiết cho từng sản phẩm

    Cấu trúc file Excel/CSV:
    - Header row: name, description, product_type, price_type, list_price, sale_price, currency, tags
    - Các trường custom: custom_fieldname hoặc cf_fieldname
    - Loại sản phẩm: PHYSICAL, DIGITAL, EVENT, SERVICE, COMBO
    - Kiểu giá: HAS_PRICE, STRING_PRICE, NO_PRICE
    `,
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'File import và thông tin bổ sung',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'File Excel/CSV/Word chứa danh sách sản phẩm',
        },
        productType: {
          type: 'string',
          enum: ['PHYSICAL', 'DIGITAL', 'EVENT', 'SERVICE', 'COMBO'],
          description: 'Loại sản phẩm (tùy chọn, nếu không có sẽ lấy từ file)',
        },
      },
      required: ['file'],
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Import sản phẩm thành công',
    schema: ApiResponseDto.getSchema(ImportCustomerProductsResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'File không hợp lệ hoặc dữ liệu không đúng định dạng',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 400 },
        message: {
          type: 'string',
          example: 'File quá lớn. Kích thước tối đa là 10MB',
        },
        timestamp: { type: 'string', example: '2025-01-08T10:30:00.000Z' },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Lỗi server khi xử lý file',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 500 },
        message: {
          type: 'string',
          example: 'Lỗi khi import sản phẩm: Không thể đọc file Excel',
        },
        timestamp: { type: 'string', example: '2025-01-08T10:30:00.000Z' },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file'))
  async importCustomerProducts(
    @UploadedFile() file: Express.Multer.File,
    @CurrentUser('id') userId: number,
    @Body('productType') productType?: string,
  ): Promise<ApiResponseDto<ImportCustomerProductsResponseDto>> {
    const result =
      await this.customerProductImportService.importCustomerProducts(
        file,
        userId,
        productType as any,
      );

    const message =
      result.successCount > 0
        ? `Import thành công ${result.successCount}/${result.totalProducts} sản phẩm${result.failedCount > 0 ? `, ${result.failedCount} sản phẩm thất bại` : ''}`
        : 'Không có sản phẩm nào được tạo thành công';

    return ApiResponseDto.success(result, message);
  }

  /**
   * Xóa nhiều sản phẩm khách hàng cùng lúc (soft delete)
   * @param bulkDeleteDto DTO chứa danh sách ID sản phẩm cần xóa
   * @param userId ID của người dùng hiện tại
   * @returns Kết quả xóa nhiều sản phẩm
   */
  @Delete('bulk')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Xóa nhiều sản phẩm khách hàng',
    description:
      'Xóa mềm nhiều sản phẩm khách hàng cùng lúc. Tối đa 50 sản phẩm mỗi lần.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xóa nhiều sản phẩm thành công',
    type: () => ApiResponseDto.getSchema(BulkDeleteCustomerProductResponseDto),
  })
  @ApiResponse({
    status: 207,
    description: 'Một số sản phẩm không thể xóa',
    type: () => ApiResponseDto.getSchema(BulkDeleteCustomerProductResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  async bulkDelete(
    @Body() bulkDeleteDto: BulkDeleteCustomerProductDto,
    @CurrentUser('id') userId: number,
  ) {
    const result = await this.customerProductService.bulkDelete(
      bulkDeleteDto,
      userId,
    );
    return ApiResponseDto.success(result, 'Xóa sản phẩm khách hàng thành công');
  }
}
