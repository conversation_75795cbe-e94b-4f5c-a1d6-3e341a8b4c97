import {
  Injectable,
  CanActivate,
  ExecutionContext,
  Logger,
  ForbiddenException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Request } from 'express';
import { JwtUtilService } from '../../auth/guards/jwt.util';
import { AddonCacheService } from '../services/addon-cache.service';
import { AddonHandlerFactory } from '../services/addon-usage-handlers/addon-handler.factory';
import { AddonType } from '../enums/addon-type.enum';
import { AppException } from '../../../common/exceptions';
import { USAGE_CONSUMPTION_ERROR_CODES } from '../exceptions/usage-consumption.exception';
import {
  UsageSecurityCheckRequest,
  UsageSecurityCheckResult,
  CachedAddonData,
} from '../interfaces/track-usage.interface';
import { UsageOperationParams } from '../services/usage-consumption.service';
import {
  SUBSCRIPTION_CHECK_METADATA_KEY,
  SubscriptionCheckOptions,
} from '../decorators';
import { SubscriptionRedirectException } from '../exceptions/subscription-redirect.exception';

@Injectable()
export class SubscriptionGuard implements CanActivate {
  private readonly logger = new Logger(SubscriptionGuard.name);

  constructor(
    private readonly reflector: Reflector,
    private readonly jwtUtilService: JwtUtilService,
    private readonly addonCacheService: AddonCacheService,
    private readonly addonHandlerFactory: AddonHandlerFactory,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();

    console.log('Subscription Guard - checking:', request.method, request.url);

    // Kiểm tra decorator metadata từ method trước, sau đó controller
    const methodOptions = this.reflector.get<SubscriptionCheckOptions>(
      SUBSCRIPTION_CHECK_METADATA_KEY,
      context.getHandler(),
    ) as SubscriptionCheckOptions | undefined;

    const controllerOptions = this.reflector.get<SubscriptionCheckOptions>(
      SUBSCRIPTION_CHECK_METADATA_KEY,
      context.getClass(),
    ) as SubscriptionCheckOptions | undefined;

    // Method decorator có ưu tiên cao hơn controller decorator
    const subscriptionOptions = methodOptions || controllerOptions;

    // Log thông tin về decorator được sử dụng
    if (methodOptions) {
      this.logger.debug(
        `Using method-level decorator for: ${request.method} ${request.url}`,
      );
    } else if (controllerOptions) {
      this.logger.debug(
        `Using controller-level decorator for: ${request.method} ${request.url}`,
      );
    }

    // Nếu có decorator và skipCheck = true, bỏ qua kiểm tra
    if (subscriptionOptions?.skipCheck) {
      const decoratorLevel = methodOptions ? 'method' : 'controller';
      this.logger.debug(
        `Skipping subscription check due to ${decoratorLevel}-level decorator: ${request.method} ${request.url}`,
      );
      return true;
    }

    // Xác định addon type cần kiểm tra
    let addonTypeToCheck: AddonType | undefined;
    let requiredAmount = 1;

    // Ưu tiên 1: Sử dụng addonType từ decorator nếu có
    if (subscriptionOptions?.addonType) {
      addonTypeToCheck = subscriptionOptions.addonType;
      requiredAmount = subscriptionOptions.amount || 1;
      const decoratorLevel = methodOptions ? 'method' : 'controller';
      this.logger.debug(
        `Using ${decoratorLevel}-level decorator: addonType=${addonTypeToCheck}, amount=${requiredAmount}`,
      );
    }
    // Ưu tiên 2: Kiểm tra SYSTEM_BASE cho non-public paths
    else if (this.requiresSystemBaseAddon(request.url)) {
      addonTypeToCheck = AddonType.SYSTEM_BASE;
      requiredAmount = 1;
      this.logger.debug(
        `Non-public URL requires SYSTEM_BASE addon: ${request.url}`,
      );
    }
    // Không có decorator và là public path - không cần kiểm tra
    else {
      this.logger.debug(
        `Public path without decorator - no subscription check needed: ${request.method} ${request.url}`,
      );
      return true; // Cho phép đi qua
    }

    // Nếu không có addon type nào được xác định, cho phép đi qua
    if (!addonTypeToCheck) {
      this.logger.debug('No addon type detected, allowing request to proceed');
      return true;
    }

    try {
      // Lấy user từ token nếu chưa có trong request
      await this.extractUserFromToken(request);

      // Validate subscription của user với addon type cụ thể
      await this.validateUserSubscriptionForAddon(
        request,
        addonTypeToCheck,
        requiredAmount,
      );

      this.logger.debug(
        `Subscription check passed for: ${request.method} ${request.url}, addonType=${addonTypeToCheck}`,
      );

      return true; // Cho phép đi qua
    } catch (error) {
      this.logger.error(
        `Subscription check failed for: ${request.method} ${request.url}, addonType=${addonTypeToCheck}`,
        error.message,
      );

      // Xử lý các loại lỗi khác nhau
      if (error instanceof AppException) {
        const errorCode = error.getErrorCode();
        if (errorCode === USAGE_CONSUMPTION_ERROR_CODES.INSUFFICIENT_USAGE) {
          throw new SubscriptionRedirectException(
            'Bạn không có quyền truy cập tính năng này. Vui lòng nâng cấp gói dịch vụ.',
          );
        } else if (
          errorCode === USAGE_CONSUMPTION_ERROR_CODES.USAGE_LIMIT_EXCEEDED
        ) {
          throw new SubscriptionRedirectException(
            'Bạn đã vượt quá giới hạn sử dụng. Vui lòng nâng cấp gói dịch vụ hoặc chờ đến kỳ thanh toán tiếp theo.',
          );
        }
      }

      // Lỗi mặc định
      throw new SubscriptionRedirectException(
        'Không thể truy cập tính năng này. Vui lòng kiểm tra gói dịch vụ của bạn.',
      );
    }
  }

  /**
   * Lấy user từ token trong authentication header
   * @param request Request object
   */
  private async extractUserFromToken(request: Request): Promise<void> {
    try {
      // Nếu đã có user hoặc employee trong request thì không cần lấy lại
      if ((request as any).user?.id || (request as any).employee?.id) {
        return;
      }

      // Lấy token từ header
      const token = this.extractTokenFromHeader(request);
      if (!token) {
        this.logger.debug('No token found in request header');
        return;
      }

      // Verify token và lấy payload - thử cả user và employee token
      let payload: any;
      let isEmployee = false;

      try {
        // Thử verify như employee token trước
        payload = this.jwtUtilService.verifyEmployeeAccessToken(token);
        isEmployee = true;
      } catch (employeeError) {
        try {
          // Nếu không phải employee token, thử verify như user token
          payload = this.jwtUtilService.verifyTokenUser(token);
          isEmployee = false;
        } catch (userError) {
          // Nếu cả hai đều thất bại, throw lỗi user token
          throw userError;
        }
      }

      // Gắn user hoặc employee vào request
      if (isEmployee) {
        (request as any).employee = {
          id: payload.id || payload.sub,
          ...payload,
        };
      } else {
        (request as any).user = {
          id: payload.id || payload.sub,
          ...payload,
        };
      }

      this.logger.debug(
        `Extracted user from token: userId=${payload.id || payload.sub}`,
      );

      // Lưu ý: Subscription validation sẽ được thực hiện trong canActivate()
      // Không cần validate ở đây để tránh xung đột
    } catch (error) {
      this.logger.warn(`Failed to extract user from token: ${error.message}`);
      // Không throw error ở đây vì đây là exception filter
    }
  }

  /**
   * Lấy token từ header Authorization
   * @param request Request object
   * @returns Token string hoặc undefined
   */
  private extractTokenFromHeader(request: Request): string | undefined {
    const authHeader = request.headers.authorization;
    if (!authHeader) {
      return undefined;
    }

    const [type, token] = authHeader.split(' ');
    return type === 'Bearer' ? token : undefined;
  }

  /**
   * Validate subscription của user cho addon type cụ thể (dùng với decorator)
   * @param request Request object
   * @param addonType Loại addon cần kiểm tra
   * @param requiredAmount Số lượng usage cần thiết
   */
  private async validateUserSubscriptionForAddon(
    request: Request,
    addonType: AddonType,
    requiredAmount: number = 1,
  ): Promise<void> {
    try {
      // Hỗ trợ cả user và employee
      const user = (request as any).user || (request as any).employee;
      if (!user?.id) {
        throw new ForbiddenException('User not authenticated');
      }

      // Nếu là employee (admin), bỏ qua kiểm tra subscription
      if ((request as any).employee?.id) {
        this.logger.debug(
          `Employee detected (id=${user.id}), skipping subscription check`,
        );
        return;
      }

      this.logger.debug(
        `Validating subscription for userId=${user.id}, addonType=${addonType}, requiredAmount=${requiredAmount}`,
      );

      // Kiểm tra usage security
      const checkRequest: UsageSecurityCheckRequest = {
        userId: user.id,
        addonType: addonType,
        requiredAmount: requiredAmount,
      };

      const checkResult = await this.checkUsageSecurity(checkRequest);

      // Nếu không thể sử dụng, throw exception
      if (!checkResult.canUse) {
        this.logger.warn(
          `Subscription validation failed for userId=${user.id}, addonType=${addonType}: ${checkResult.errorMessage}`,
        );

        // Throw AppException để có thể handle trong canActivate
        if (!checkResult.hasActiveSubscription) {
          throw new AppException(
            USAGE_CONSUMPTION_ERROR_CODES.INSUFFICIENT_USAGE,
            'Không có subscription active cho addon này',
          );
        } else if (!checkResult.hasRemainingUsage) {
          throw new AppException(
            USAGE_CONSUMPTION_ERROR_CODES.USAGE_LIMIT_EXCEEDED,
            `Đã hết quota cho addon ${addonType}. Còn lại: ${checkResult.remainingUsage}, yêu cầu: ${requiredAmount}`,
          );
        } else {
          throw new AppException(
            USAGE_CONSUMPTION_ERROR_CODES.INSUFFICIENT_USAGE,
            checkResult.errorMessage || 'Không thể sử dụng addon này',
          );
        }
      }

      this.logger.debug(
        `Subscription validation passed for userId=${user.id}, addonType=${addonType}`,
      );
    } catch (error) {
      this.logger.error(
        `Error validating user subscription for addon: ${error.message}`,
        error.stack,
      );

      // Re-throw để có thể handle trong canActivate
      throw error;
    }
  }

  /**
   * Kiểm tra xem API path có cần addon SYSTEM_BASE không
   * Tất cả đường dẫn không phải public đều cần SYSTEM_BASE
   * @param path URL path
   * @returns true nếu cần addon SYSTEM_BASE
   */
  private requiresSystemBaseAddon(path: string): boolean {
    // Nếu là đường dẫn public thì không cần SYSTEM_BASE
    if (this.isPublicPath(path)) {
      return false;
    }

    // Tất cả đường dẫn không phải public đều cần SYSTEM_BASE
    return true;
  }

  /**
   * Kiểm tra usage security cho user và addon type (tương tự UsageSecurityGuard)
   */
  private async checkUsageSecurity(
    request: UsageSecurityCheckRequest,
  ): Promise<UsageSecurityCheckResult> {
    try {
      this.logger.debug(
        `Checking usage security: userId=${request.userId}, addonType=${request.addonType}, requiredAmount=${request.requiredAmount || 1}`,
      );

      // 1. Kiểm tra cache trước
      const cacheResult = await this.addonCacheService.getCachedAddonStatus(
        request.userId,
        request.addonType,
      );

      let cachedData: CachedAddonData;
      if (cacheResult.found && cacheResult.data) {
        cachedData = cacheResult.data;
        this.logger.debug(
          `Using cached data for userId=${request.userId}, addonType=${request.addonType}`,
        );
      } else {
        // 2. Cache miss - lấy từ database và cache lại
        this.logger.debug(
          `Cache miss - fetching from database for userId=${request.userId}, addonType=${request.addonType}`,
        );
        cachedData = await this.addonCacheService.getAndCacheAddonStatus(
          request.userId,
          request.addonType,
        );
      }

      // 3. Gọi addon-specific validation trước khi kiểm tra usage
      await this.validateAddonSpecificRules(request);

      // 4. Kiểm tra usage requirements
      const requiredAmount = request.requiredAmount || 1;
      const hasRemainingUsage = cachedData.remainingUsage >= requiredAmount;
      const canUse = cachedData.canUse && hasRemainingUsage;

      const result: UsageSecurityCheckResult = {
        canUse,
        hasActiveSubscription: cachedData.hasActiveSubscription,
        hasRemainingUsage,
        remainingUsage: cachedData.remainingUsage,
        totalUsageLimit: cachedData.totalLimit,
        errorMessage: canUse
          ? undefined
          : this.buildErrorMessage(cachedData, requiredAmount),
      };

      this.logger.debug(
        `Usage security check result: canUse=${canUse}, remainingUsage=${cachedData.remainingUsage}, requiredAmount=${requiredAmount}`,
      );
      return result;
    } catch (error) {
      this.logger.error('Error checking usage security', error);
      return {
        canUse: false,
        hasActiveSubscription: false,
        hasRemainingUsage: false,
        remainingUsage: 0,
        totalUsageLimit: 0,
        errorMessage: 'Lỗi kiểm tra usage security',
        errorDetails: error,
      };
    }
  }

  /**
   * Tạo error message dựa trên cached data
   */
  private buildErrorMessage(
    cachedData: CachedAddonData,
    requiredAmount: number,
  ): string {
    if (!cachedData.hasActiveSubscription) {
      return 'Không có subscription active';
    }

    if (!cachedData.canUse) {
      if (cachedData.remainingUsage <= 0) {
        return 'Đã hết quota cho addon này';
      }
      if (Date.now() > cachedData.expiryTime) {
        return 'Addon đã hết hạn sử dụng';
      }
      return 'Không thể sử dụng addon này';
    }

    if (cachedData.remainingUsage < requiredAmount) {
      return `Không đủ usage. Còn lại: ${cachedData.remainingUsage}, yêu cầu: ${requiredAmount}`;
    }

    return 'Không thể sử dụng addon này';
  }

  /**
   * Gọi addon-specific validation rules thông qua AddonHandlerFactory
   */
  private async validateAddonSpecificRules(
    request: UsageSecurityCheckRequest,
  ): Promise<void> {
    try {
      this.logger.debug(
        `Validating addon-specific rules for addonType=${request.addonType}, userId=${request.userId}`,
      );

      // 1. Lấy addon handler cho addon type này
      const handler = await this.addonHandlerFactory.getHandlerByAddonType(
        request.addonType,
      );

      if (!handler) {
        this.logger.warn(
          `No handler found for addon type: ${request.addonType}`,
        );
        return; // Không có handler thì skip validation
      }

      // 2. Tạo UsageOperationParams từ request
      const operationParams: UsageOperationParams = {
        userId: request.userId,
        addonId: 0, // Sẽ được resolve trong handler nếu cần
        amount: request.requiredAmount || 1,
        reason: 'security_validation',
        metadata: {
          moduleSource: 'subscription',
          resourceType: 'security_validation',
          additionalInfo: {
            source: 'subscription_exception_filter',
            addonType: request.addonType,
            validationOnly: true,
          },
        },
      };

      // 3. Gọi addon-specific validation
      await handler.validateAddonSpecificRules(operationParams);

      this.logger.debug(
        `Addon-specific validation passed for addonType=${request.addonType}`,
      );
    } catch (error) {
      this.logger.error(
        `Addon-specific validation failed for addonType=${request.addonType}: ${error.message}`,
      );

      // Re-throw error để có thể handle
      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        USAGE_CONSUMPTION_ERROR_CODES.USAGE_SECURITY_CHECK_FAILED,
        `Addon validation failed: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách các pattern đường dẫn public (không cần kiểm tra subscription)
   * @returns Array các pattern đường dẫn public
   */
  private getPublicPathPatterns(): string[] {
    return [
      'admin',
      'employees',
      'auth',
      'public',
      'user/addon-usages',
      'user/',
      'health',
      'docs',
      'employee',
      'swagger',
      'i18n-examples',
      'status',
      'ping',
      'user/help-center',
      'integration',
      'integrations',
      'marketing/zalo',
      'version',
      'user/subscription',
      'user/plan-pricing',
      'users/two-factor-auth',
      'users/settings',
      'users/profile',
      'user/routers',
      'user/affiliate',
      'user/rule-contracts-xstate',
      'users/change-password',
      'webhook/ipn',
      'users/points',
      'user/subscription/payment/webhook',
      'r-point',
      'user/routers-sse',
      'webhook',
      'marketing/zalo/webhook',
      // Search APIs - không cần kiểm tra subscription
      'search/files/search',
      'search/products/search',
      'search/media/search',
      'search/urls/search',
      'search/search/image',
      // Admin Search APIs - không cần kiểm tra subscription
      'admin/search/files/search',
      'admin/search/products/search',
      'admin/search/media/search',
      'admin/search/urls/search',
      'admin/search/search/image',
      // Website chat endpoints - không cần kiểm tra subscription (sử dụng website key authentication)
      'website/chat',
      'website/platform',
      // Thêm các pattern public khác nếu cần
    ];
  }

  /**
   * Lấy danh sách các prefix version API
   * @returns Array các prefix version
   */
  private getApiVersionPrefixes(): string[] {
    return [
      '/api/v1/',
      '/v1/',
      '/api/v2/',
      '/v2/',
      // Thêm các version khác nếu cần
    ];
  }

  /**
   * Kiểm tra xem path có phải là public không
   * @param path URL path
   * @returns true nếu là public path
   */
  private isPublicPath(path: string): boolean {
    const publicPatterns = this.getPublicPathPatterns();
    const versionPrefixes = this.getApiVersionPrefixes();

    // Kiểm tra từng pattern với từng version prefix
    for (const pattern of publicPatterns) {
      // Kiểm tra pattern trực tiếp (không có prefix)
      if (path.startsWith(`/${pattern}`)) {
        return true;
      }

      // Kiểm tra pattern với các version prefix
      for (const prefix of versionPrefixes) {
        if (path.startsWith(`${prefix}${pattern}`)) {
          return true;
        }
      }
    }

    return false;
  }
}
