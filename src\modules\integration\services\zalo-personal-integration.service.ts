import { Injectable, Logger } from '@nestjs/common';
import { AppException, ErrorCode } from '@/common/exceptions';
import { ZaloPersonalIntegrationRepository } from '../repositories/zalo-personal-integration.repository';
import { KeyPairEncryptionService } from '@/shared/services/encryption/key-pair-encryption.service';
import { CreateZaloPersonalIntegrationDto } from '../dto/zalo-personal/create-zalo-personal-integration.dto';
import { UpdateZaloPersonalIntegrationDto } from '../dto/zalo-personal/update-zalo-personal-integration.dto';
import { ZaloPersonalIntegrationResponseDto } from '../dto/zalo-personal/zalo-personal-integration-response.dto';
import { ZaloPersonalMetadata } from '../interfaces/zalo-personal-metadata.interface';
import { ZaloPersonalPayload } from '../interfaces/payload_encryption.interface';
import { Integration } from '../entities/integration.entity';

/**
 * Service xử lý Zalo Personal Integration
 */
@Injectable()
export class ZaloPersonalIntegrationService {
  private readonly logger = new Logger(ZaloPersonalIntegrationService.name);

  constructor(
    private readonly zaloPersonalIntegrationRepository: ZaloPersonalIntegrationRepository,
    private readonly keyPairEncryptionService: KeyPairEncryptionService,
  ) {}

  /**
   * Tạo Zalo Personal Integration mới
   */
  async createZaloPersonalIntegration(
    dto: CreateZaloPersonalIntegrationDto,
    userId?: number,
    employeeId?: number,
  ): Promise<ZaloPersonalIntegrationResponseDto> {
    try {
      this.logger.log(
        `Creating Zalo Personal Integration for userId: ${dto.userId}`,
      );

      // Kiểm tra integration đã tồn tại chưa
      const existingIntegration = userId
        ? await this.zaloPersonalIntegrationRepository.findByUserIdAndZaloUserId(
            userId,
            dto.userId,
          )
        : employeeId
          ? await this.zaloPersonalIntegrationRepository.findByEmployeeIdAndZaloUserId(
              employeeId,
              dto.userId,
            )
          : null;

      if (existingIntegration) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Tài khoản Zalo cá nhân này đã được kết nối trước đó',
        );
      }

      // Lấy provider
      const provider =
        await this.zaloPersonalIntegrationRepository.findZaloPersonalProvider();

      // Tạo payload để mã hóa
      const payload: ZaloPersonalPayload = {
        accessToken: dto.accessToken,
        refreshToken: dto.refreshToken,
        sessionCookies: dto.sessionCookies,
        browserFingerprint: dto.browserFingerprint,
      };

      // Mã hóa dữ liệu nhạy cảm
      const encryptionResult = this.keyPairEncryptionService.encrypt(payload);

      // Tạo metadata
      const now = Date.now();
      const metadata: ZaloPersonalMetadata = {
        zalo_uid: dto.userId,
        displayName: dto.displayName,
        username: dto.username,
        avatarUrl: dto.avatarUrl,
        phoneNumber: dto.phoneNumber,
        email: dto.email,
        expiresAt: dto.expiresAt || now + 24 * 60 * 60 * 1000, // 24 giờ mặc định
        refreshTokenExpiresAt:
          dto.refreshTokenExpiresAt || now + 30 * 24 * 60 * 60 * 1000, // 30 ngày mặc định
        status: dto.status || 'active',
        createdAt: now,
        updatedAt: now,
        browserInfo: {
          userAgent: dto.userAgent,
          sessionCookies: dto.sessionCookies,
          lastLoginAt: now,
          ipAddress: dto.ipAddress,
        },
        settings: {
          autoSendMessage: dto.autoSendMessage || false,
          dailyMessageLimit: dto.dailyMessageLimit || 100,
          messageDelay: dto.messageDelay || 1000,
          useProxy: dto.useProxy || false,
          proxyConfig:
            dto.useProxy && dto.proxyHost && dto.proxyPort
              ? {
                  host: dto.proxyHost,
                  port: dto.proxyPort,
                  username: dto.proxyUsername,
                  password: dto.proxyPassword,
                }
              : undefined,
        },
      };

      // Tạo integration
      const integration =
        await this.zaloPersonalIntegrationRepository.createZaloPersonalIntegration(
          {
            integrationName: dto.integrationName,
            typeId: provider.id,
            userId,
            employeeId,
            encryptedConfig: encryptionResult.encryptedData,
            secretKey: encryptionResult.publicKey,
            metadata,
          },
        );

      this.logger.log(
        `Created Zalo Personal Integration with ID: ${integration.id}`,
      );

      return this.mapToResponseDto(integration);
    } catch (error) {
      this.logger.error(
        `Failed to create Zalo Personal Integration: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Tạo Zalo Personal Integration từ thông tin đăng nhập automation-web
   */
  async createFromAutomationWebLogin(data: {
    userId: number;
    zaloUserId: string;
    displayName: string;
    avatarUrl?: string;
    integrationId: string;
    sessionCookies?: string;
    browserFingerprint?: string;
  }): Promise<ZaloPersonalIntegrationResponseDto> {
    try {
      this.logger.log(
        `Creating Zalo Personal Integration from automation-web login for user ${data.userId}`,
      );

      // Kiểm tra integration đã tồn tại chưa
      const existingIntegration =
        await this.zaloPersonalIntegrationRepository.findByUserIdAndZaloUserId(
          data.userId,
          data.zaloUserId,
        );

      if (existingIntegration) {
        this.logger.log(
          `Zalo Personal Integration already exists, updating metadata`,
        );

        // Cập nhật metadata với thông tin mới
        const currentMetadata =
          existingIntegration.metadata as ZaloPersonalMetadata;
        const updatedMetadata: ZaloPersonalMetadata = {
          ...currentMetadata,
          displayName: data.displayName,
          avatarUrl: data.avatarUrl || currentMetadata.avatarUrl,
          status: 'active',
          updatedAt: Date.now(),
          browserInfo: {
            ...currentMetadata.browserInfo,
            sessionCookies: data.sessionCookies,
            lastLoginAt: Date.now(),
          },
        };

        const updatedIntegration =
          await this.zaloPersonalIntegrationRepository.updateZaloPersonalIntegration(
            existingIntegration.id,
            { metadata: updatedMetadata },
          );

        return this.mapToResponseDto(updatedIntegration);
      }

      // Tạo integration mới
      const now = Date.now();
      const createDto: CreateZaloPersonalIntegrationDto = {
        integrationName: `Zalo Personal - ${data.displayName}`,
        userId: data.zaloUserId, // Đây là zalo_uid, sẽ được map vào metadata.zalo_uid
        displayName: data.displayName,
        avatarUrl: data.avatarUrl,
        accessToken: 'temp_token', // Sẽ được cập nhật sau
        sessionCookies: data.sessionCookies,
        browserFingerprint: data.browserFingerprint,
        status: 'active',
        expiresAt: now + 24 * 60 * 60 * 1000, // 24 giờ mặc định
      };

      return await this.createZaloPersonalIntegration(createDto, data.userId);
    } catch (error) {
      this.logger.error(
        `Failed to create Zalo Personal Integration from automation-web: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Lấy Zalo Personal Integration theo ID
   */
  async getZaloPersonalIntegrationById(
    id: string,
  ): Promise<ZaloPersonalIntegrationResponseDto> {
    const integration =
      await this.zaloPersonalIntegrationRepository.findById(id);

    if (!integration) {
      throw new AppException(
        ErrorCode.NOT_FOUND,
        'Zalo Personal Integration không tồn tại',
      );
    }

    return this.mapToResponseDto(integration);
  }

  /**
   * Lấy danh sách Zalo Personal Integration theo userId
   */
  async getZaloPersonalIntegrationsByUserId(
    userId: number,
  ): Promise<ZaloPersonalIntegrationResponseDto[]> {
    const integrations =
      await this.zaloPersonalIntegrationRepository.findByUserId(userId);
    return integrations.map((integration) =>
      this.mapToResponseDto(integration),
    );
  }

  /**
   * Cập nhật Zalo Personal Integration
   */
  async updateZaloPersonalIntegration(
    id: string,
    dto: UpdateZaloPersonalIntegrationDto,
  ): Promise<ZaloPersonalIntegrationResponseDto> {
    try {
      this.logger.log(`Updating Zalo Personal Integration: ${id}`);

      const existingIntegration =
        await this.zaloPersonalIntegrationRepository.findById(id);
      if (!existingIntegration) {
        throw new AppException(
          ErrorCode.NOT_FOUND,
          'Zalo Personal Integration không tồn tại',
        );
      }

      // Cập nhật payload nếu có thay đổi token
      let encryptedConfig = existingIntegration.encryptedConfig;
      let secretKey = existingIntegration.secretKey;

      if (
        dto.accessToken ||
        dto.refreshToken ||
        dto.sessionCookies ||
        dto.browserFingerprint
      ) {
        const currentMetadata =
          existingIntegration.metadata as ZaloPersonalMetadata;
        const payload: ZaloPersonalPayload = {
          accessToken: dto.accessToken || 'existing_token',
          refreshToken: dto.refreshToken,
          sessionCookies: dto.sessionCookies,
          browserFingerprint: dto.browserFingerprint,
        };

        const encryptionResult = this.keyPairEncryptionService.encrypt(payload);
        encryptedConfig = encryptionResult.encryptedData;
        secretKey = encryptionResult.publicKey;
      }

      // Cập nhật metadata
      const currentMetadata =
        existingIntegration.metadata as ZaloPersonalMetadata;
      const updatedMetadata: ZaloPersonalMetadata = {
        ...currentMetadata,
        displayName: dto.displayName || currentMetadata.displayName,
        username: dto.username || currentMetadata.username,
        avatarUrl: dto.avatarUrl || currentMetadata.avatarUrl,
        phoneNumber: dto.phoneNumber || currentMetadata.phoneNumber,
        email: dto.email || currentMetadata.email,
        status: dto.status || currentMetadata.status,
        updatedAt: Date.now(),
        browserInfo: {
          ...currentMetadata.browserInfo,
          userAgent: dto.userAgent || currentMetadata.browserInfo?.userAgent,
          ipAddress: dto.ipAddress || currentMetadata.browserInfo?.ipAddress,
        },
        settings: {
          ...currentMetadata.settings,
          autoSendMessage:
            dto.autoSendMessage ?? currentMetadata.settings?.autoSendMessage,
          dailyMessageLimit:
            dto.dailyMessageLimit ||
            currentMetadata.settings?.dailyMessageLimit,
          messageDelay:
            dto.messageDelay || currentMetadata.settings?.messageDelay,
          useProxy: dto.useProxy ?? currentMetadata.settings?.useProxy,
          proxyConfig: dto.useProxy
            ? (() => {
                const host =
                  dto.proxyHost || currentMetadata.settings?.proxyConfig?.host;
                const port =
                  dto.proxyPort || currentMetadata.settings?.proxyConfig?.port;

                if (host && port) {
                  return {
                    host,
                    port,
                    username:
                      dto.proxyUsername ||
                      currentMetadata.settings?.proxyConfig?.username,
                    password:
                      dto.proxyPassword ||
                      currentMetadata.settings?.proxyConfig?.password,
                  };
                }
                return undefined;
              })()
            : currentMetadata.settings?.proxyConfig,
        },
      };

      const updatedIntegration =
        await this.zaloPersonalIntegrationRepository.updateZaloPersonalIntegration(
          id,
          {
            integrationName: dto.integrationName,
            encryptedConfig: encryptedConfig || undefined,
            secretKey: secretKey || undefined,
            metadata: updatedMetadata,
          },
        );

      this.logger.log(`Updated Zalo Personal Integration: ${id}`);

      return this.mapToResponseDto(updatedIntegration);
    } catch (error) {
      this.logger.error(
        `Failed to update Zalo Personal Integration: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Xóa Zalo Personal Integration
   */
  async deleteZaloPersonalIntegration(id: string): Promise<void> {
    try {
      this.logger.log(`Deleting Zalo Personal Integration: ${id}`);

      const existingIntegration =
        await this.zaloPersonalIntegrationRepository.findById(id);
      if (!existingIntegration) {
        throw new AppException(
          ErrorCode.NOT_FOUND,
          'Zalo Personal Integration không tồn tại',
        );
      }

      await this.zaloPersonalIntegrationRepository.deleteZaloPersonalIntegration(
        id,
      );

      this.logger.log(`Deleted Zalo Personal Integration: ${id}`);
    } catch (error) {
      this.logger.error(
        `Failed to delete Zalo Personal Integration: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Lấy zalo_uid từ integration
   */
  async getZaloUidFromIntegration(integrationId: string): Promise<string> {
    const integration =
      await this.zaloPersonalIntegrationRepository.findById(integrationId);

    if (!integration) {
      throw new AppException(
        ErrorCode.NOT_FOUND,
        'Zalo Personal Integration không tồn tại',
      );
    }

    const metadata = integration.metadata as ZaloPersonalMetadata;
    return metadata.zalo_uid;
  }

  /**
   * Tìm integration theo zalo_uid
   */
  async findByZaloUid(
    zaloUid: string,
  ): Promise<ZaloPersonalIntegrationResponseDto[]> {
    try {
      const provider =
        await this.zaloPersonalIntegrationRepository.findZaloPersonalProvider();

      const integrations = await this.zaloPersonalIntegrationRepository[
        'integrationRepository'
      ].find({
        where: {
          typeId: provider.id,
          metadata: {
            zalo_uid: zaloUid,
          } as any,
        },
      });

      return integrations.map((integration) =>
        this.mapToResponseDto(integration),
      );
    } catch (error) {
      this.logger.error(
        `Failed to find integration by zalo_uid: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Map Integration entity sang response DTO
   */
  private mapToResponseDto(
    integration: Integration,
  ): ZaloPersonalIntegrationResponseDto {
    const metadata = integration.metadata as ZaloPersonalMetadata;

    return {
      id: integration.id,
      integrationName: integration.integrationName,
      userId: integration.userId || undefined,
      employeeId: integration.employeeId || undefined,
      metadata,
      createdAt: new Date(integration.createdAt),
      hasAccessToken: !!integration.encryptedConfig,
      hasRefreshToken: true, // Assume có refresh token
      hasSessionCookies: !!metadata.browserInfo?.sessionCookies,
      hasBrowserFingerprint: true, // Assume có browser fingerprint
    };
  }
}
