import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Request,
  UseGuards,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards';
import { ApiResponseDto } from '@/common/response';
import { ZaloPersonalIntegrationService } from '../services/zalo-personal-integration.service';
import {
  ZaloPersonalWebhookService,
  ZaloPersonalLoginWebhookData,
} from '../services/zalo-personal-webhook.service';
import { CreateZaloPersonalIntegrationDto } from '../dto/zalo-personal/create-zalo-personal-integration.dto';
import { UpdateZaloPersonalIntegrationDto } from '../dto/zalo-personal/update-zalo-personal-integration.dto';
import { ZaloPersonalIntegrationResponseDto } from '../dto/zalo-personal/zalo-personal-integration-response.dto';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { ApiResponseDto as AppApiResponse } from '@/common/response';

/**
 * Controller cho Zalo Personal Integration
 */
@ApiTags(SWAGGER_API_TAGS.INTEGRATION)
@Controller('integration/zalo-personal')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class ZaloPersonalIntegrationController {
  private readonly logger = new Logger(ZaloPersonalIntegrationController.name);

  constructor(
    private readonly zaloPersonalIntegrationService: ZaloPersonalIntegrationService,
    private readonly zaloPersonalWebhookService: ZaloPersonalWebhookService,
  ) {}

  /**
   * Tạo Zalo Personal Integration mới
   */
  @Post()
  @ApiOperation({ summary: 'Tạo Zalo Personal Integration mới' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Tạo thành công',
    type: ZaloPersonalIntegrationResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu không hợp lệ',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Tài khoản Zalo đã được kết nối',
  })
  async createZaloPersonalIntegration(
    @Body() createDto: CreateZaloPersonalIntegrationDto,
    @Request() req: any,
  ): Promise<ApiResponseDto<ZaloPersonalIntegrationResponseDto>> {
    const result =
      await this.zaloPersonalIntegrationService.createZaloPersonalIntegration(
        createDto,
        req.user.id,
      );

    return ApiResponseDto.success(
      result,
      'Tạo Zalo Personal Integration thành công',
    );
  }

  /**
   * Lấy danh sách Zalo Personal Integration của user
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách Zalo Personal Integration của user' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách thành công',
    type: [ZaloPersonalIntegrationResponseDto],
  })
  async getZaloPersonalIntegrations(
    @Request() req: any,
  ): Promise<AppApiResponse<ZaloPersonalIntegrationResponseDto[]>> {
    const result =
      await this.zaloPersonalIntegrationService.getZaloPersonalIntegrationsByUserId(
        req.user.id,
      );

    return AppApiResponse.success(
      result,
      'Lấy danh sách Zalo Personal Integration thành công',
    );
  }

  /**
   * Lấy thông tin Zalo Personal Integration theo ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin Zalo Personal Integration theo ID' })
  @ApiParam({ name: 'id', description: 'ID của Zalo Personal Integration' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy thông tin thành công',
    type: ZaloPersonalIntegrationResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy integration',
  })
  async getZaloPersonalIntegrationById(
    @Param('id') id: string,
  ): Promise<AppApiResponse<ZaloPersonalIntegrationResponseDto>> {
    const result =
      await this.zaloPersonalIntegrationService.getZaloPersonalIntegrationById(
        id,
      );

    return AppApiResponse.success(
      result,
      'Lấy thông tin Zalo Personal Integration thành công',
    );
  }

  /**
   * Cập nhật Zalo Personal Integration
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật Zalo Personal Integration' })
  @ApiParam({ name: 'id', description: 'ID của Zalo Personal Integration' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Cập nhật thành công',
    type: ZaloPersonalIntegrationResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy integration',
  })
  async updateZaloPersonalIntegration(
    @Param('id') id: string,
    @Body() updateDto: UpdateZaloPersonalIntegrationDto,
  ): Promise<AppApiResponse<ZaloPersonalIntegrationResponseDto>> {
    const result =
      await this.zaloPersonalIntegrationService.updateZaloPersonalIntegration(
        id,
        updateDto,
      );

    return AppApiResponse.success(
      result,
      'Cập nhật Zalo Personal Integration thành công',
    );
  }

  /**
   * Xóa Zalo Personal Integration
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa Zalo Personal Integration' })
  @ApiParam({ name: 'id', description: 'ID của Zalo Personal Integration' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xóa thành công',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy integration',
  })
  async deleteZaloPersonalIntegration(
    @Param('id') id: string,
  ): Promise<AppApiResponse<void>> {
    await this.zaloPersonalIntegrationService.deleteZaloPersonalIntegration(id);

    return AppApiResponse.success(
      undefined,
      'Xóa Zalo Personal Integration thành công',
    );
  }

  /**
   * Lấy zalo_uid từ integration ID
   */
  @Get(':id/zalo-uid')
  @ApiOperation({
    summary: 'Lấy zalo_uid từ integration ID',
    description:
      'Endpoint này trả về zalo_uid để sử dụng với automation-web APIs',
  })
  @ApiParam({ name: 'id', description: 'ID của Zalo Personal Integration' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy zalo_uid thành công',
    schema: {
      type: 'object',
      properties: {
        zalo_uid: { type: 'string', example: '1234567890' },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy integration',
  })
  async getZaloUidFromIntegration(
    @Param('id') id: string,
  ): Promise<AppApiResponse<{ zalo_uid: string }>> {
    const zaloUid =
      await this.zaloPersonalIntegrationService.getZaloUidFromIntegration(id);

    return AppApiResponse.success(
      { zalo_uid: zaloUid },
      'Lấy zalo_uid thành công',
    );
  }

  /**
   * Tìm integration theo zalo_uid
   */
  @Get('by-zalo-uid/:zaloUid')
  @ApiOperation({
    summary: 'Tìm integration theo zalo_uid',
    description: 'Endpoint này tìm tất cả integration có zalo_uid tương ứng',
  })
  @ApiParam({ name: 'zaloUid', description: 'Zalo UID cần tìm' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Tìm integration thành công',
    type: [ZaloPersonalIntegrationResponseDto],
  })
  async findIntegrationByZaloUid(
    @Param('zaloUid') zaloUid: string,
  ): Promise<AppApiResponse<ZaloPersonalIntegrationResponseDto[]>> {
    const integrations =
      await this.zaloPersonalIntegrationService.findByZaloUid(zaloUid);

    return AppApiResponse.success(
      integrations,
      'Tìm integration theo zalo_uid thành công',
    );
  }
}
