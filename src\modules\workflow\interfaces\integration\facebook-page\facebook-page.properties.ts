/**
 * @file Facebook Page Node Properties
 * 
 * Đ<PERSON>nh nghĩa node properties cho Facebook Page integration
 * Theo patterns từ Make.com chuẩn
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

import {
    EPropertyType,
    INodeProperty
} from '../../node-manager.interface';

import {
    EFacebookPageOperation,
    EFacebookPostField,
    EFacebookPageField,
    EFacebookReactionType,
    EFacebookPostPrivacy,
    EFacebookPhotoAction
} from './facebook-page.types';

// =================================================================
// FACEBOOK PAGE NODE PROPERTIES
// =================================================================

/**
 * Facebook Page node properties definition
 */
export const FACEBOOK_PAGE_PROPERTIES: INodeProperty[] = [
    // Operation Selection
    {
        name: 'operation',
        displayName: 'Operation',
        type: EPropertyType.Options,
        required: true,
        default: EFacebookPageOperation.LIST_POSTS,
        description: 'Chọn thao tác cần thực hiện',
        options: [
            // === POSTS OPERATIONS ===
            { name: 'Watch Posts', value: EFacebookPageOperation.WATCH_POSTS },
            { name: 'Watch Posts (public page)', value: EFacebookPageOperation.WATCH_POSTS_PUBLIC },
            { name: 'List Posts', value: EFacebookPageOperation.LIST_POSTS },
            { name: 'Get a Post', value: EFacebookPageOperation.GET_POST },
            { name: 'Get Post Reactions', value: EFacebookPageOperation.GET_POST_REACTIONS },
            { name: 'Create a Post', value: EFacebookPageOperation.CREATE_POST },
            { name: 'Create a Post with Photos', value: EFacebookPageOperation.CREATE_POST_WITH_PHOTOS },
            { name: 'Update a Post', value: EFacebookPageOperation.UPDATE_POST },
            { name: 'Delete a Post', value: EFacebookPageOperation.DELETE_POST },
            { name: 'Like a Post', value: EFacebookPageOperation.LIKE_POST },
            { name: 'Unlike a Post', value: EFacebookPageOperation.UNLIKE_POST },

            // === VIDEOS OPERATIONS ===
            { name: 'Watch Videos', value: EFacebookPageOperation.WATCH_VIDEOS },
            { name: 'List Videos', value: EFacebookPageOperation.LIST_VIDEOS },
            { name: 'Get a Video', value: EFacebookPageOperation.GET_VIDEO },
            { name: 'Upload a Video', value: EFacebookPageOperation.UPLOAD_VIDEO },
            { name: 'Update a Video', value: EFacebookPageOperation.UPDATE_VIDEO },
            { name: 'Delete a Video', value: EFacebookPageOperation.DELETE_VIDEO },

            // === PHOTOS OPERATIONS ===
            { name: 'Watch Photos', value: EFacebookPageOperation.WATCH_PHOTOS },
            { name: 'List Photos', value: EFacebookPageOperation.LIST_PHOTOS },
            { name: 'Get a Photo', value: EFacebookPageOperation.GET_PHOTO },
            { name: 'Upload a Photo', value: EFacebookPageOperation.UPLOAD_PHOTO },
            { name: 'Delete a Photo', value: EFacebookPageOperation.DELETE_PHOTO },

            // === COMMENTS OPERATIONS ===
            { name: 'Watch Comments', value: EFacebookPageOperation.WATCH_COMMENTS },
            { name: 'List Comments', value: EFacebookPageOperation.LIST_COMMENTS },
            { name: 'Get a Comment', value: EFacebookPageOperation.GET_COMMENT },
            { name: 'Create a Comment', value: EFacebookPageOperation.CREATE_COMMENT },
            { name: 'Update a Comment', value: EFacebookPageOperation.UPDATE_COMMENT },
            { name: 'Delete a Comment', value: EFacebookPageOperation.DELETE_COMMENT },

            // === PAGE OPERATIONS ===
            { name: 'Get a Page', value: EFacebookPageOperation.GET_PAGE },
            { name: 'Update a Page', value: EFacebookPageOperation.UPDATE_PAGE },

            // === OTHER OPERATIONS ===
            { name: 'Publish a Reel', value: EFacebookPageOperation.PUBLISH_REEL }
        ]
    },

    // Page ID (Required for most operations)
    {
        name: 'page_id',
        displayName: 'Page',
        type: EPropertyType.String,
        required: true,
        description: 'Chọn trang Facebook',
        displayOptions: {
            show: {
                operation: [
                    EFacebookPageOperation.LIST_POSTS,
                    EFacebookPageOperation.GET_POST,
                    EFacebookPageOperation.GET_POST_REACTIONS,
                    EFacebookPageOperation.CREATE_POST,
                    EFacebookPageOperation.CREATE_POST_WITH_PHOTOS,
                    EFacebookPageOperation.UPDATE_POST,
                    EFacebookPageOperation.DELETE_POST,
                    EFacebookPageOperation.LIKE_POST,
                    EFacebookPageOperation.UNLIKE_POST,
                    EFacebookPageOperation.LIST_VIDEOS,
                    EFacebookPageOperation.GET_VIDEO,
                    EFacebookPageOperation.UPLOAD_VIDEO,
                    EFacebookPageOperation.UPDATE_VIDEO,
                    EFacebookPageOperation.DELETE_VIDEO,
                    EFacebookPageOperation.LIST_PHOTOS,
                    EFacebookPageOperation.GET_PHOTO,
                    EFacebookPageOperation.UPLOAD_PHOTO,
                    EFacebookPageOperation.DELETE_PHOTO,
                    EFacebookPageOperation.LIST_COMMENTS,
                    EFacebookPageOperation.GET_COMMENT,
                    EFacebookPageOperation.CREATE_COMMENT,
                    EFacebookPageOperation.UPDATE_COMMENT,
                    EFacebookPageOperation.DELETE_COMMENT,
                    EFacebookPageOperation.GET_PAGE,
                    EFacebookPageOperation.UPDATE_PAGE,
                    EFacebookPageOperation.PUBLISH_REEL
                ]
            }
        }
    },

    // Include Hidden Posts (for List Posts)
    {
        name: 'include_hidden',
        displayName: 'Include hidden posts',
        type: EPropertyType.Options,
        description: 'Bao gồm bài đăng ẩn',
        default: 'empty',
        options: [
            { name: 'Yes', value: 'yes' },
            { name: 'No', value: 'no' },
            { name: 'Empty', value: 'empty' }
        ],
        displayOptions: {
            show: {
                operation: [
                    EFacebookPageOperation.LIST_POSTS
                ]
            }
        }
    },

    // Limit field (for List Posts)
    {
        name: 'limit',
        displayName: 'Limit',
        type: EPropertyType.Number,
        required: true,
        default: 2,
        minValue: 1,
        maxValue: 500,
        description: 'The maximum number of results to be worked with during one execution cycle',
        displayOptions: {
            show: {
                operation: [
                    EFacebookPageOperation.LIST_POSTS
                ]
            }
        }
    },

    // Post ID field (for Get a Post, Get Post Reactions, Update a Post, Delete a Post, Like a Post, and Unlike a Post)
    {
        name: 'post_id',
        displayName: 'Post ID',
        type: EPropertyType.String,
        required: true,
        description: 'ID của bài đăng',
        displayOptions: {
            show: {
                operation: [
                    EFacebookPageOperation.GET_POST,
                    EFacebookPageOperation.GET_POST_REACTIONS,
                    EFacebookPageOperation.UPDATE_POST,
                    EFacebookPageOperation.DELETE_POST,
                    EFacebookPageOperation.LIKE_POST,
                    EFacebookPageOperation.UNLIKE_POST
                ]
            }
        }
    },

    // Message field (for Create a Post and Update a Post)
    {
        name: 'message',
        displayName: 'Message',
        type: EPropertyType.String,
        description: 'Nội dung bài đăng',
        displayOptions: {
            show: {
                operation: [
                    EFacebookPageOperation.CREATE_POST,
                    EFacebookPageOperation.UPDATE_POST
                ]
            }
        }
    },

    // Link field (for Create a Post)
    {
        name: 'link',
        displayName: 'Link',
        type: EPropertyType.String,
        description: 'Liên kết đính kèm',
        displayOptions: {
            show: {
                operation: [
                    EFacebookPageOperation.CREATE_POST
                ]
            }
        }
    },

    // Published field (for Create a Post)
    {
        name: 'published',
        displayName: 'Published',
        type: EPropertyType.Boolean,
        default: true,
        description: 'Trạng thái xuất bản bài đăng',
        displayOptions: {
            show: {
                operation: [
                    EFacebookPageOperation.CREATE_POST
                ]
            }
        }
    },

    // Scheduled publish time field (for Create a Post)
    {
        name: 'scheduled_publish_time',
        displayName: 'Scheduled publish time',
        type: EPropertyType.DateTime,
        description: 'Thời gian xuất bản theo lịch (ISO string)',
        displayOptions: {
            show: {
                operation: [
                    EFacebookPageOperation.CREATE_POST
                ],
                published: [false]
            }
        }
    },

    // Privacy field (for Create a Post)
    {
        name: 'privacy',
        displayName: 'Privacy',
        type: EPropertyType.Options,
        description: 'Cài đặt quyền riêng tư',
        options: [
            { name: 'Public', value: EFacebookPostPrivacy.PUBLIC },
            { name: 'Friends', value: EFacebookPostPrivacy.FRIENDS },
            { name: 'Only me', value: EFacebookPostPrivacy.SELF }
        ],
        displayOptions: {
            show: {
                operation: [
                    EFacebookPageOperation.CREATE_POST
                ]
            }
        }
    },

    // Type field (for Get Post Reactions)
    {
        name: 'type',
        displayName: 'Type',
        type: EPropertyType.Options,
        description: 'Loại reaction cần lấy',
        options: [
            { name: 'Like', value: EFacebookReactionType.LIKE },
            { name: 'Love', value: EFacebookReactionType.LOVE },
            { name: 'Haha', value: EFacebookReactionType.HAHA },
            { name: 'Wow', value: EFacebookReactionType.WOW },
            { name: 'Sad', value: EFacebookReactionType.SAD },
            { name: 'Angry', value: EFacebookReactionType.ANGRY },
            { name: 'Care', value: EFacebookReactionType.CARE },
            { name: 'Pride', value: EFacebookReactionType.PRIDE }
        ],
        displayOptions: {
            show: {
                operation: [
                    EFacebookPageOperation.GET_POST_REACTIONS
                ]
            }
        }
    },

    // Limit field (for Get Post Reactions)
    {
        name: 'limit_reactions',
        displayName: 'Limit',
        type: EPropertyType.Number,
        default: 25,
        minValue: 1,
        maxValue: 100,
        description: 'The maximum number of reactions to be worked with during one execution cycle',
        displayOptions: {
            show: {
                operation: [
                    EFacebookPageOperation.GET_POST_REACTIONS
                ]
            }
        }
    },

    // Photos field (for Create a Post with Photos)
    {
        name: 'photos',
        displayName: 'Photos',
        type: EPropertyType.Collection,
        required: true,
        description: 'Danh sách ảnh cần đăng (tối thiểu 1 ảnh)',
        properties: [
            {
                name: 'url',
                displayName: 'Photo URL',
                type: EPropertyType.String,
                required: true,
                description: 'URL của ảnh'
            },
            {
                name: 'caption',
                displayName: 'Caption',
                type: EPropertyType.String,
                description: 'Chú thích cho ảnh'
            }
        ],
        displayOptions: {
            show: {
                operation: [
                    EFacebookPageOperation.CREATE_POST_WITH_PHOTOS
                ]
            }
        }
    },

    // Message field (for Create a Post with Photos)
    {
        name: 'message_photos',
        displayName: 'Message',
        type: EPropertyType.String,
        description: 'Nội dung bài đăng với ảnh',
        displayOptions: {
            show: {
                operation: [
                    EFacebookPageOperation.CREATE_POST_WITH_PHOTOS
                ]
            }
        }
    },

    // Type field (for List Videos)
    {
        name: 'type',
        displayName: 'Type',
        type: EPropertyType.Options,
        required: true,
        default: 'uploaded',
        description: 'Loại video',
        options: [
            { name: 'Uploaded', value: 'uploaded' }
        ],
        displayOptions: {
            show: {
                operation: [
                    EFacebookPageOperation.LIST_VIDEOS
                ]
            }
        }
    },

    // Limit field (for List Videos)
    {
        name: 'limit_videos',
        displayName: 'Limit',
        type: EPropertyType.Number,
        required: true,
        default: 2,
        minValue: 1,
        maxValue: 500,
        description: 'The maximum number of results to be worked with during one execution cycle',
        displayOptions: {
            show: {
                operation: [
                    EFacebookPageOperation.LIST_VIDEOS
                ]
            }
        }
    },

    // Video ID field (for Get a Video, Update a Video, and Delete a Video)
    {
        name: 'video_id',
        displayName: 'Video ID',
        type: EPropertyType.String,
        required: true,
        description: 'ID của video',
        displayOptions: {
            show: {
                operation: [
                    EFacebookPageOperation.GET_VIDEO,
                    EFacebookPageOperation.UPDATE_VIDEO,
                    EFacebookPageOperation.DELETE_VIDEO
                ]
            }
        }
    },

    // Type field (for Upload a Video)
    {
        name: 'type_video',
        displayName: 'Type',
        type: EPropertyType.Options,
        required: true,
        default: 'upload a video',
        description: 'Loại thao tác với video',
        options: [
            { name: 'Upload a video', value: 'upload a video' }
        ],
        displayOptions: {
            show: {
                operation: [
                    EFacebookPageOperation.UPLOAD_VIDEO
                ]
            }
        }
    },

    // File field (for Upload a Video)
    {
        name: 'file_video',
        displayName: 'File',
        type: EPropertyType.Collection,
        required: true,
        description: 'File video cần upload',
        properties: [
            {
                name: 'filename',
                displayName: 'File Name',
                type: EPropertyType.String,
                required: true,
                description: 'Tên file video'
            },
            {
                name: 'data',
                displayName: 'File Data',
                type: EPropertyType.String,
                required: true,
                description: 'Dữ liệu file video (base64 hoặc binary)'
            }
        ],
        displayOptions: {
            show: {
                operation: [
                    EFacebookPageOperation.UPLOAD_VIDEO
                ]
            }
        }
    },

    // Description field (for Upload a Video)
    {
        name: 'description_video',
        displayName: 'Description',
        type: EPropertyType.String,
        description: 'Mô tả video',
        displayOptions: {
            show: {
                operation: [
                    EFacebookPageOperation.UPLOAD_VIDEO
                ]
            }
        }
    },

    // Title field (for Update a Video)
    {
        name: 'title_video',
        displayName: 'Title',
        type: EPropertyType.String,
        description: 'Tiêu đề video',
        displayOptions: {
            show: {
                operation: [
                    EFacebookPageOperation.UPDATE_VIDEO
                ]
            }
        }
    },

    // Description field (for Update a Video)
    {
        name: 'description_update_video',
        displayName: 'Description',
        type: EPropertyType.String,
        description: 'Mô tả video',
        displayOptions: {
            show: {
                operation: [
                    EFacebookPageOperation.UPDATE_VIDEO
                ]
            }
        }
    },

    // Limit field (for List Photos)
    {
        name: 'limit_photos',
        displayName: 'Limit',
        type: EPropertyType.Number,
        required: true,
        default: 2,
        minValue: 1,
        maxValue: 500,
        description: 'The maximum number of results to be worked with during one execution cycle',
        displayOptions: {
            show: {
                operation: [
                    EFacebookPageOperation.LIST_PHOTOS
                ]
            }
        }
    },

    // Photo ID field (for Get a Photo and Delete a Photo)
    {
        name: 'photo_id',
        displayName: 'Photo ID',
        type: EPropertyType.String,
        required: true,
        description: 'ID của ảnh',
        displayOptions: {
            show: {
                operation: [
                    EFacebookPageOperation.GET_PHOTO,
                    EFacebookPageOperation.DELETE_PHOTO
                ]
            }
        }
    },

    // File field (for Upload a Photo)
    {
        name: 'file_photo',
        displayName: 'File',
        type: EPropertyType.Collection,
        required: true,
        description: 'File ảnh cần upload',
        properties: [
            {
                name: 'filename',
                displayName: 'File Name',
                type: EPropertyType.String,
                required: true,
                description: 'Tên file ảnh'
            },
            {
                name: 'data',
                displayName: 'File Data',
                type: EPropertyType.String,
                required: true,
                description: 'Dữ liệu file ảnh (base64 hoặc binary)'
            }
        ],
        displayOptions: {
            show: {
                operation: [
                    EFacebookPageOperation.UPLOAD_PHOTO
                ]
            }
        }
    },

    // Message field (for Upload a Photo)
    {
        name: 'message_photo',
        displayName: 'Message',
        type: EPropertyType.String,
        description: 'Nội dung/caption cho ảnh',
        displayOptions: {
            show: {
                operation: [
                    EFacebookPageOperation.UPLOAD_PHOTO
                ]
            }
        }
    },

    // Filter field (for List Comments)
    {
        name: 'filter_comments',
        displayName: 'Filter',
        type: EPropertyType.Options,
        required: true,
        default: 'stream',
        description: 'This determines which comments are returned when comment replies are available',
        options: [
            { name: 'Stream (all comments)', value: 'stream' }
        ],
        displayOptions: {
            show: {
                operation: [
                    EFacebookPageOperation.LIST_COMMENTS
                ]
            }
        }
    },

    // Order field (for List Comments)
    {
        name: 'order_comments',
        displayName: 'Order',
        type: EPropertyType.String,
        description: 'Thứ tự sắp xếp comment',
        displayOptions: {
            show: {
                operation: [
                    EFacebookPageOperation.LIST_COMMENTS
                ]
            }
        }
    },

    // Limit field (for List Comments)
    {
        name: 'limit_comments',
        displayName: 'Limit',
        type: EPropertyType.Number,
        required: true,
        default: 2,
        minValue: 1,
        maxValue: 500,
        description: 'The maximum number of results to be worked with during one execution cycle',
        displayOptions: {
            show: {
                operation: [
                    EFacebookPageOperation.LIST_COMMENTS
                ]
            }
        }
    },

    // Comment ID field (for Get a Comment, Update a Comment, and Delete a Comment)
    {
        name: 'comment_id',
        displayName: 'Comment ID',
        type: EPropertyType.String,
        required: true,
        description: 'ID của comment',
        displayOptions: {
            show: {
                operation: [
                    EFacebookPageOperation.GET_COMMENT,
                    EFacebookPageOperation.UPDATE_COMMENT,
                    EFacebookPageOperation.DELETE_COMMENT
                ]
            }
        }
    },

    // Message field (for Create a Comment)
    {
        name: 'message_comment',
        displayName: 'Message',
        type: EPropertyType.String,
        description: 'Nội dung comment',
        displayOptions: {
            show: {
                operation: [
                    EFacebookPageOperation.CREATE_COMMENT
                ]
            }
        }
    },

    // Attachment type field (for Create a Comment)
    {
        name: 'attachment_type',
        displayName: 'Attachment type',
        type: EPropertyType.String,
        description: 'Loại đính kèm',
        displayOptions: {
            show: {
                operation: [
                    EFacebookPageOperation.CREATE_COMMENT
                ]
            }
        }
    },

    // Is hidden field (for Update a Comment)
    {
        name: 'is_hidden',
        displayName: 'Is hidden',
        type: EPropertyType.Boolean,
        description: 'Ẩn comment hay không',
        displayOptions: {
            show: {
                operation: [
                    EFacebookPageOperation.UPDATE_COMMENT
                ]
            }
        }
    },

    // Message field (for Update a Comment)
    {
        name: 'message_update_comment',
        displayName: 'Message',
        type: EPropertyType.String,
        description: 'Nội dung comment',
        displayOptions: {
            show: {
                operation: [
                    EFacebookPageOperation.UPDATE_COMMENT
                ]
            }
        }
    },

    // Attachment type field (for Update a Comment)
    {
        name: 'attachment_type_update',
        displayName: 'Attachment type',
        type: EPropertyType.String,
        description: 'Loại đính kèm',
        displayOptions: {
            show: {
                operation: [
                    EFacebookPageOperation.UPDATE_COMMENT
                ]
            }
        }
    }
];
