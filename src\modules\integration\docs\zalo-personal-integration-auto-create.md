# Zalo Personal Integration - Tự động tạo Integration sau khi đăng nhập thành công

## Tổng quan

Hệ thống này tự động tạo Zalo Personal Integration trong bảng `integrations` sau khi người dùng đăng nhập thành công qua QR code từ automation-web service.

## Luồng hoạt động

```mermaid
sequenceDiagram
    participant Frontend
    participant MainApp as redai-v201-be-app
    participant AutoWeb as automation-web
    participant Database

    Frontend->>MainApp: POST /v1/marketing/zalo/qr-code/session
    MainApp->>AutoWeb: Tạo QR session
    AutoWeb->>Frontend: SSE stream với QR code

    Note over Frontend: User scan QR code

    AutoWeb->>AutoWeb: Kiểm tra đăng nhập thành công
    AutoWeb->>Database: Lưu vào zalo_account
    AutoWeb->>MainApp: POST /v1/integration/zalo-personal/webhook/login-success
    MainApp->>Database: Tạo record trong integrations
    MainApp->>AutoWeb: Response success
    AutoWeb->>Frontend: SSE event login_success
```

## Các thành phần đã tạo

### 1. Repository Layer

- **ZaloPersonalIntegrationRepository**: Xử lý thao tác database cho Zalo Personal Integration

### 2. Service Layer

- **ZaloPersonalIntegrationService**: Logic nghiệp vụ chính
- **ZaloPersonalWebhookService**: Xử lý webhook từ automation-web

### 3. Controller Layer

- **ZaloPersonalIntegrationController**: REST API endpoints

### 4. Automation-web Updates

- Thêm logic gọi webhook về main app sau khi đăng nhập thành công

## API Endpoints

### 1. Tạo Integration thủ công

```http
POST /api/v1/integration/zalo-personal
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "integrationName": "Zalo Personal - Nguyễn Văn A",
  "userId": "**********",
  "displayName": "Nguyễn Văn A",
  "avatarUrl": "https://example.com/avatar.jpg",
  "accessToken": "access_token_here",
  "sessionCookies": "session_cookies_here",
  "status": "active"
}
```

### 2. Webhook từ automation-web (Tự động) - KHÔNG CẦN JWT

```http
POST /api/v1/integration/zalo-personal/webhook/login-success
Content-Type: application/json
# ⚠️ Endpoint này KHÔNG cần Authorization header

{
  "userId": 1,
  "zaloUserId": "**********",
  "displayName": "Nguyễn Văn A",
  "avatarUrl": "https://example.com/avatar.jpg",
  "integrationId": "integration-uuid-123",
  "sessionCookies": "session_cookies_here",
  "browserFingerprint": "browser_fingerprint_here"
}
```

### 2.1. Webhook Error Handling

```http
# Webhook khi có lỗi đăng nhập
POST /api/v1/integration/zalo-personal/webhook/login-error
Content-Type: application/json

{
  "userId": 1,
  "integrationId": "integration-uuid-123",
  "error": "LOGIN_FAILED",
  "errorMessage": "QR code expired"
}

# Webhook khi session hết hạn
POST /api/v1/integration/zalo-personal/webhook/session-expired
Content-Type: application/json

{
  "userId": 1,
  "integrationId": "integration-uuid-123",
  "sessionId": "session-uuid-123"
}

# Health check cho automation-web
POST /api/v1/integration/zalo-personal/webhook/health
# Response: {"status": "healthy", "timestamp": 1640995200000}
```

### 3. Lấy danh sách Integration

```http
GET /api/v1/integration/zalo-personal
Authorization: Bearer <JWT_TOKEN>
```

### 4. Cập nhật Integration

```http
PUT /api/v1/integration/zalo-personal/{id}
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "integrationName": "Zalo Personal - Updated Name",
  "status": "active"
}
```

### 5. Xóa Integration

```http
DELETE /api/v1/integration/zalo-personal/{id}
Authorization: Bearer <JWT_TOKEN>
```

### 6. Lấy zalo_uid từ Integration ID

```http
GET /api/v1/integration/zalo-personal/{id}/zalo-uid
Authorization: Bearer <JWT_TOKEN>

Response:
{
  "success": true,
  "data": {
    "zalo_uid": "**********"
  },
  "message": "Lấy zalo_uid thành công"
}
```

### 7. Tìm Integration theo zalo_uid

```http
GET /api/v1/integration/zalo-personal/by-zalo-uid/{zaloUid}
Authorization: Bearer <JWT_TOKEN>

Response:
{
  "success": true,
  "data": [
    {
      "id": "integration-uuid",
      "integrationName": "Zalo Personal - User Name",
      "metadata": {
        "zalo_uid": "**********",
        "displayName": "User Name"
      }
    }
  ]
}
```

## Cấu trúc dữ liệu

### Integration Entity

```typescript
{
  id: "uuid",
  integrationName: "Zalo Personal - Nguyễn Văn A",
  typeId: 123, // ID của ZALO_PERSONAL provider
  userId: 1,
  ownedType: "USER",
  encryptedConfig: "encrypted_tokens_and_cookies",
  secretKey: "encryption_key",
  metadata: {
    zalo_uid: "**********",
    displayName: "Nguyễn Văn A",
    avatarUrl: "https://example.com/avatar.jpg",
    status: "active",
    browserInfo: {
      sessionCookies: "cookies_here",
      lastLoginAt: 1640995200000
    },
    settings: {
      autoSendMessage: false,
      dailyMessageLimit: 100,
      messageDelay: 1000
    }
  },
  createdAt: 1640995200000,
  updatedAt: 1640995200000
}
```

## Sử dụng với Automation-web APIs

Sau khi có integration, bạn có thể sử dụng `zalo_uid` để gọi các API của automation-web:

### 1. Lấy zalo_uid từ integration

```typescript
// Lấy zalo_uid từ integration ID
const response = await fetch(
  '/api/v1/integration/zalo-personal/{integrationId}/zalo-uid',
  {
    headers: { Authorization: 'Bearer ' + token },
  },
);
const { zalo_uid } = response.data;
```

### 2. Gọi automation-web APIs

```typescript
// Send friend request
const friendRequestResponse = await fetch(
  `${automationWebUrl}/zalo/${zalo_uid}/friend-request`,
  {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      phone_numbers: ['0901234567', '0987654321'],
      message: 'Xin chào, tôi muốn kết bạn với bạn!',
    }),
  },
);

// Send message
const messageResponse = await fetch(
  `${automationWebUrl}/zalo/${zalo_uid}/send-message`,
  {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      recipient_id: 'friend_zalo_uid',
      message: 'Hello from automation!',
    }),
  },
);
```

## Cách test

### 1. Test webhook endpoint

```bash
curl -X POST http://localhost:3000/api/v1/integration/zalo-personal/webhook/login-success \
  -H "Content-Type: application/json" \
  -d '{
    "userId": 1,
    "zaloUserId": "test123",
    "displayName": "Test User",
    "avatarUrl": "https://example.com/avatar.jpg",
    "integrationId": "test-integration-123",
    "sessionCookies": "test_cookies",
    "browserFingerprint": "test_fingerprint"
  }'
```

### 2. Test tạo integration thủ công

```bash
curl -X POST http://localhost:3000/api/v1/integration/zalo-personal \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "integrationName": "Test Zalo Personal",
    "userId": "test123",
    "displayName": "Test User",
    "accessToken": "test_token",
    "status": "active"
  }'
```

### 3. Test lấy danh sách

```bash
curl -X GET http://localhost:3000/api/v1/integration/zalo-personal \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Lưu ý quan trọng

### 1. Security

- Access token và session cookies được mã hóa trong `encryptedConfig`
- Sử dụng KeyPairEncryptionService để mã hóa/giải mã

### 2. Error Handling

- Webhook service có validation đầy đủ
- Tự động retry nếu có lỗi
- Log chi tiết cho debugging

### 3. Database

- Kiểm tra duplicate trước khi tạo
- Cập nhật metadata nếu integration đã tồn tại
- Transaction safety

### 4. Automation-web Configuration

- Cần cập nhật URL webhook trong automation-web
- Cần implement logic extract user_id từ integration_id
- Timeout 10 giây cho webhook call

## Troubleshooting

### 1. Webhook không được gọi

- Kiểm tra automation-web logs
- Verify webhook URL configuration
- Check network connectivity

### 2. Integration không được tạo

- Check webhook service logs
- Verify user_id exists
- Check database constraints

### 3. Duplicate integration error

- Integration đã tồn tại cho user + zaloUserId
- Sẽ cập nhật thay vì tạo mới

## Next Steps

1. **Frontend Integration**: Cập nhật frontend để hiển thị integration sau khi đăng nhập
2. **Notification**: Thêm notification khi tạo integration thành công
3. **Monitoring**: Thêm metrics và monitoring cho webhook
4. **Testing**: Viết unit tests và integration tests
5. **Documentation**: Cập nhật API documentation
