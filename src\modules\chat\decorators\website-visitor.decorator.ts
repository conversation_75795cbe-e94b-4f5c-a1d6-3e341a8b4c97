import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { PayloadLiveChatKey } from '@modules/integration/interfaces/website.interface';

/**
 * Website Visitor Decorator
 * 
 * Extracts the website payload from the request object that was set by the WebsiteKeyGuard.
 * This provides a clean way to access website visitor information in controllers
 * without manually extracting from the request object.
 * 
 * @example
 * ```typescript
 * @Post('messages')
 * @UseGuards(WebsiteKeyGuard)
 * async sendMessage(
 *   @WebsiteVisitor() websitePayload: PayloadLiveChatKey,
 *   @Body() messageDto: MessageDto,
 * ) {
 *   // Use websitePayload.websiteId, websitePayload.userId, etc.
 * }
 * ```
 */
export const WebsiteVisitor = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): PayloadLiveChatKey => {
    const request = ctx.switchToHttp().getRequest();
    const websitePayload = request['websitePayload'] as PayloadLiveChatKey;
    
    if (!websitePayload) {
      throw new Error(
        'Website payload not found. Ensure WebsiteKeyGuard is applied to this route.',
      );
    }
    
    return websitePayload;
  },
);