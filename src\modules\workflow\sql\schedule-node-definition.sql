-- <PERSON><PERSON> để insert Schedule Node Definition vào database
-- <PERSON><PERSON><PERSON> bản ghi node definition cho Schedule node v<PERSON><PERSON> đ<PERSON>y đủ properties

INSERT INTO node_definitions (
    id,
    type_name,
    version,
    display_name,
    description,
    group_name,
    icon,
    properties,
    inputs,
    outputs,
    credentials,
    created_at,
    updated_at
) VALUES (
    gen_random_uuid(),
    'schedule',
    1,
    'Schedule',
    'Schedule workflow execution at specific times or intervals. Supports cron expressions, daily/weekly/monthly schedules, and one-time executions.',
    'Utility',
    'calendar',
    '[
        {
            "name": "schedule_type",
            "displayName": "Schedule Type",
            "description": "Type of schedule to create",
            "type": "options",
            "required": true,
            "options": [
                {"name": "Once", "value": "once"},
                {"name": "Cron Expression", "value": "cron"},
                {"name": "Interval", "value": "interval"},
                {"name": "Daily", "value": "daily"},
                {"name": "Weekly", "value": "weekly"},
                {"name": "Monthly", "value": "monthly"}
            ],
            "default": "daily"
        },
        {
            "name": "name",
            "displayName": "Schedule Name",
            "description": "Name for this schedule",
            "type": "string",
            "required": true,
            "placeholder": "Daily Report Generation"
        },
        {
            "name": "once_datetime",
            "displayName": "Date & Time",
            "description": "When to run the schedule (for once type)",
            "type": "dateTime",
            "required": false,
            "displayOptions": {
                "show": {
                    "schedule_type": ["once"]
                }
            }
        },
        {
            "name": "cron_expression",
            "displayName": "Cron Expression",
            "description": "Cron expression (e.g., 0 9 * * 1-5 for weekdays at 9AM)",
            "type": "string",
            "required": false,
            "placeholder": "0 9 * * 1-5",
            "displayOptions": {
                "show": {
                    "schedule_type": ["cron"]
                }
            }
        },
        {
            "name": "interval_value",
            "displayName": "Interval Value",
            "description": "Interval value",
            "type": "number",
            "required": false,
            "minValue": 1,
            "default": 30,
            "displayOptions": {
                "show": {
                    "schedule_type": ["interval"]
                }
            }
        },
        {
            "name": "interval_type",
            "displayName": "Interval Type",
            "description": "Interval time unit",
            "type": "options",
            "required": false,
            "options": [
                {"name": "Seconds", "value": "seconds"},
                {"name": "Minutes", "value": "minutes"},
                {"name": "Hours", "value": "hours"},
                {"name": "Days", "value": "days"}
            ],
            "default": "minutes",
            "displayOptions": {
                "show": {
                    "schedule_type": ["interval"]
                }
            }
        },
        {
            "name": "daily_hour",
            "displayName": "Hour",
            "description": "Hour to run (0-23)",
            "type": "number",
            "required": false,
            "minValue": 0,
            "maxValue": 23,
            "default": 9,
            "displayOptions": {
                "show": {
                    "schedule_type": ["daily", "weekly", "monthly"]
                }
            }
        },
        {
            "name": "daily_minute",
            "displayName": "Minute",
            "description": "Minute to run (0-59)",
            "type": "number",
            "required": false,
            "minValue": 0,
            "maxValue": 59,
            "default": 0,
            "displayOptions": {
                "show": {
                    "schedule_type": ["daily", "weekly", "monthly"]
                }
            }
        },
        {
            "name": "weekly_days",
            "displayName": "Days of Week",
            "description": "Select days of week to run",
            "type": "options",
            "required": false,
            "multipleValues": true,
            "options": [
                {"name": "Sunday", "value": 0},
                {"name": "Monday", "value": 1},
                {"name": "Tuesday", "value": 2},
                {"name": "Wednesday", "value": 3},
                {"name": "Thursday", "value": 4},
                {"name": "Friday", "value": 5},
                {"name": "Saturday", "value": 6}
            ],
            "default": [1, 2, 3, 4, 5],
            "displayOptions": {
                "show": {
                    "schedule_type": ["weekly"]
                }
            }
        },
        {
            "name": "monthly_day",
            "displayName": "Day of Month",
            "description": "Day of month to run (1-31 or last)",
            "type": "options",
            "required": false,
            "options": [
                {"name": "1st", "value": 1},
                {"name": "2nd", "value": 2},
                {"name": "3rd", "value": 3},
                {"name": "4th", "value": 4},
                {"name": "5th", "value": 5},
                {"name": "10th", "value": 10},
                {"name": "15th", "value": 15},
                {"name": "20th", "value": 20},
                {"name": "25th", "value": 25},
                {"name": "Last Day", "value": "last"}
            ],
            "default": 1,
            "displayOptions": {
                "show": {
                    "schedule_type": ["monthly"]
                }
            }
        },
        {
            "name": "timezone",
            "displayName": "Timezone",
            "description": "Timezone for schedule execution",
            "type": "options",
            "required": false,
            "options": [
                {"name": "UTC", "value": "UTC"},
                {"name": "Asia/Ho_Chi_Minh", "value": "Asia/Ho_Chi_Minh"},
                {"name": "America/New_York", "value": "America/New_York"},
                {"name": "Europe/London", "value": "Europe/London"},
                {"name": "Asia/Tokyo", "value": "Asia/Tokyo"}
            ],
            "default": "Asia/Ho_Chi_Minh"
        },
        {
            "name": "execution_behavior",
            "displayName": "Execution Behavior",
            "description": "How to execute the workflow",
            "type": "options",
            "required": true,
            "options": [
                {"name": "Start Workflow", "value": "start_workflow"},
                {"name": "Start from Node", "value": "start_from_node"},
                {"name": "Trigger Event", "value": "trigger_event"}
            ],
            "default": "start_workflow"
        },
        {
            "name": "target_node_id",
            "displayName": "Target Node",
            "description": "Node to start execution from",
            "type": "string",
            "required": false,
            "displayOptions": {
                "show": {
                    "execution_behavior": ["start_from_node"]
                }
            }
        },
        {
            "name": "event_name",
            "displayName": "Event Name",
            "description": "Name of event to trigger",
            "type": "string",
            "required": false,
            "placeholder": "daily_report_trigger",
            "displayOptions": {
                "show": {
                    "execution_behavior": ["trigger_event"]
                }
            }
        },
        {
            "name": "overlap_behavior",
            "displayName": "Overlap Behavior",
            "description": "What to do if previous execution is still running",
            "type": "options",
            "required": true,
            "options": [
                {"name": "Allow", "value": "allow"},
                {"name": "Skip", "value": "skip"},
                {"name": "Replace", "value": "replace"},
                {"name": "Queue", "value": "queue"}
            ],
            "default": "skip"
        },
        {
            "name": "is_active",
            "displayName": "Active",
            "description": "Enable/disable this schedule",
            "type": "boolean",
            "default": true
        },
        {
            "name": "max_executions",
            "displayName": "Max Executions",
            "description": "Maximum number of executions (optional)",
            "type": "number",
            "required": false,
            "minValue": 1
        },
        {
            "name": "execution_timeout",
            "displayName": "Execution Timeout (ms)",
            "description": "Timeout for each execution in milliseconds",
            "type": "number",
            "required": false,
            "minValue": 1000,
            "default": 300000
        },
        {
            "name": "input_data",
            "displayName": "Input Data",
            "description": "Data to pass to workflow execution",
            "type": "json",
            "required": false
        },
        {
            "name": "retry_enabled",
            "displayName": "Enable Retry",
            "description": "Enable retry on failure",
            "type": "boolean",
            "default": false
        },
        {
            "name": "max_retries",
            "displayName": "Max Retries",
            "description": "Maximum number of retries",
            "type": "number",
            "required": false,
            "minValue": 1,
            "maxValue": 10,
            "default": 3,
            "displayOptions": {
                "show": {
                    "retry_enabled": [true]
                }
            }
        },
        {
            "name": "retry_delay",
            "displayName": "Retry Delay (ms)",
            "description": "Delay between retries in milliseconds",
            "type": "number",
            "required": false,
            "minValue": 1000,
            "default": 60000,
            "displayOptions": {
                "show": {
                    "retry_enabled": [true]
                }
            }
        }
    ]'::jsonb,
    '["trigger"]'::jsonb,
    '["main", "error"]'::jsonb,
    '[]'::jsonb,
    EXTRACT(epoch FROM now()) * 1000,
    EXTRACT(epoch FROM now()) * 1000
);

-- Verify the insertion
SELECT 
    id,
    type_name,
    version,
    display_name,
    description,
    group_name,
    icon,
    jsonb_array_length(properties) as properties_count,
    inputs,
    outputs
FROM node_definitions 
WHERE type_name = 'schedule' AND version = 1;
