import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty } from 'class-validator';

/**
 * DTO cho việc lấy danh sách sản phẩm khách hàng theo nhiều ID (Query parameters)
 */
export class GetCustomerProductsByIdsDto {
  @ApiProperty({
    description:
      'Danh sách ID sản phẩm cần lấy thông tin (cách nhau bởi dấu phẩy)',
    type: String,
    example: '1,2,3,4,5',
    required: true,
  })
  @IsString({ message: 'IDs phải là chuỗi các số cách nhau bởi dấu phẩy' })
  @IsNotEmpty({ message: 'Danh sách ID không được rỗng' })
  ids: string;
}
