-- Facebook Ads Node Definitions SQL Insert Script
-- This script inserts the 3 Facebook Ads integration modules into the node_definitions table

-- Insert Facebook Campaign Management Node Definition
INSERT INTO node_definitions (
    type_name,
    version,
    display_name,
    description,
    group_name,
    icon,
    properties,
    inputs,
    outputs,
    credentials,
    created_at,
    updated_at
) VALUES (
    'facebook-campaign-management',
    1,
    'Facebook Campaign Management',
    'Manage Facebook Ads campaigns, ad sets, and ads. List, update campaigns and ad sets, get reach estimates, search ad interests and locations.',
    'integration',
    'facebook',
    '[
        {
            "name": "operation",
            "displayName": "Operation",
            "type": "options",
            "required": true,
            "default": "listCampaigns",
            "description": "<PERSON><PERSON>n thao tác cần thực hiện",
            "options": [
                { "name": "List Campaigns", "value": "listCampaigns" },
                { "name": "Update a Campaign", "value": "updateCampaign" },
                { "name": "List Ad Sets", "value": "listAdSets" },
                { "name": "Update an Ad Set", "value": "updateAdSet" },
                { "name": "List Ads", "value": "listAds" },
                { "name": "Update an Ad", "value": "updateAd" },
                { "name": "Get a Reach Estimate", "value": "getReachEstimate" },
                { "name": "Search Ad Interests", "value": "searchAdInterests" },
                { "name": "Search Locations", "value": "searchLocations" }
            ]
        },
        {
            "name": "business_id",
            "displayName": "Business ID",
            "type": "string",
            "required": true,
            "displayOptions": {
                "show": {
                    "operation": ["listCampaigns", "updateCampaign", "listAdSets", "updateAdSet", "listAds", "updateAd", "getReachEstimate"]
                }
            }
        }
    ]'::jsonb,
    '["main"]'::jsonb,
    '["main"]'::jsonb,
    '[
        {
            "provider": "facebook",
            "name": "facebookOAuth",
            "displayName": "Facebook Ads Campaign Management OAuth2",
            "description": "OAuth2 authentication for Facebook Ads Campaign Management",
            "required": true,
            "authType": "oauth2",
            "testable": true,
            "testUrl": "/api/integrations/test-connection"
        }
    ]'::jsonb,
    EXTRACT(epoch FROM now()) * 1000,
    EXTRACT(epoch FROM now()) * 1000
);

-- Insert Facebook Custom Audiences Node Definition
INSERT INTO node_definitions (
    type_name,
    version,
    display_name,
    description,
    group_name,
    icon,
    properties,
    inputs,
    outputs,
    credentials,
    created_at,
    updated_at
) VALUES (
    'facebook-custom-audiences',
    1,
    'Facebook Custom Audiences',
    'Create and manage Facebook Custom Audiences and Lookalike Audiences. Add users, create lookalikes, and manage audience members.',
    'integration',
    'facebook',
    '[
        {
            "name": "operation",
            "displayName": "Operation",
            "type": "options",
            "required": true,
            "default": "createCustomAudience",
            "description": "Chọn thao tác cần thực hiện",
            "options": [
                { "name": "Create a Custom Audience", "value": "createCustomAudience" },
                { "name": "Add Emails to a Custom Audience", "value": "addEmailsToCustomAudience" },
                { "name": "Add Users to a Custom Audience", "value": "addUsersToCustomAudience" },
                { "name": "Remove Audience Members", "value": "removeAudienceMembers" },
                { "name": "Create a Lookalike Audience", "value": "createLookalikeAudience" },
                { "name": "Create a Page Fan Lookalike Audience", "value": "createPageFanLookalikeAudience" },
                { "name": "Create a Campaign or Ad Set Conversion Lookalikes", "value": "createCampaignConversionLookalikes" },
                { "name": "Create a Value-Based Custom Audience", "value": "createValueBasedCustomAudience" },
                { "name": "Populate a Seed Audience", "value": "populateSeedAudience" },
                { "name": "Create a Value-Based Lookalike", "value": "createValueBasedLookalike" }
            ]
        }
    ]'::jsonb,
    '["main"]'::jsonb,
    '["main"]'::jsonb,
    '[
        {
            "provider": "facebook",
            "name": "facebookOAuth",
            "displayName": "Facebook Custom Audiences OAuth2",
            "description": "OAuth2 authentication for Facebook Custom Audiences",
            "required": true,
            "authType": "oauth2",
            "testable": true,
            "testUrl": "/api/integrations/test-connection"
        }
    ]'::jsonb,
    EXTRACT(epoch FROM now()) * 1000,
    EXTRACT(epoch FROM now()) * 1000
);

-- Insert Facebook Lead Ads Node Definition
INSERT INTO node_definitions (
    type_name,
    version,
    display_name,
    description,
    group_name,
    icon,
    properties,
    inputs,
    outputs,
    credentials,
    created_at,
    updated_at
) VALUES (
    'facebook-lead-ads',
    1,
    'Facebook Lead Ads',
    'Manage Facebook Lead Ads webhooks and lead data. Handle new leads, get lead details, manage forms and webhooks.',
    'integration',
    'facebook',
    '[
        {
            "name": "operation",
            "displayName": "Operation",
            "type": "options",
            "required": true,
            "options": [
                { "name": "New Lead (Webhook)", "value": "newLead" },
                { "name": "Get Lead Detail", "value": "getLeadDetail" },
                { "name": "Get a Form", "value": "getForm" },
                { "name": "Unsubscribe a Webhook", "value": "unsubscribeWebhook" },
                { "name": "List Leads", "value": "listLeads" }
            ]
        }
    ]'::jsonb,
    '["main"]'::jsonb,
    '["main"]'::jsonb,
    '[
        {
            "provider": "facebook",
            "name": "facebookOAuth",
            "displayName": "Facebook Lead Ads OAuth2",
            "description": "OAuth2 authentication for Facebook Lead Ads",
            "required": true,
            "authType": "oauth2",
            "testable": true,
            "testUrl": "/api/integrations/test-connection"
        }
    ]'::jsonb,
    EXTRACT(epoch FROM now()) * 1000,
    EXTRACT(epoch FROM now()) * 1000
);

-- Verify the insertions
SELECT 
    type_name,
    version,
    display_name,
    group_name,
    icon,
    created_at
FROM node_definitions 
WHERE type_name IN ('facebook-campaign-management', 'facebook-custom-audiences', 'facebook-lead-ads')
ORDER BY type_name;
