# 🔐 Integration Credential Architecture

## 📋 **Overview**

<PERSON><PERSON><PERSON> là documentation về cách quản lý credentials trong workflow integration system, theo industry standards từ Make.com và n8n.

## 🏗️ **Architecture Pattern**

### ✅ **CORRECT: Node-Level Credentials**

```typescript
// Node Entity - Credential được attach tại node level
@Entity('nodes')
export class Node {
    @Column({ name: 'integration_id', type: 'uuid', nullable: true })
    integrationId: string; // ← Credential reference
    
    @Column({ type: 'jsonb', nullable: true })
    parameters: INodeParameters; // ← Business logic only
}

// Parameters - Chỉ chứa business logic
export interface ICreateDocumentParameters extends IActionParameters {
    operation: EGoogleDocsOperation.CREATE_DOCUMENT;
    
    // ✅ Business logic only
    name: string;
    content?: string;
    drive_id?: string;
    folder_id?: string;
}
```

### ❌ **INCORRECT: Parameter-Level Credentials**

```typescript
// ❌ BAD: Mixing credentials với business logic
export interface ICreateDocumentParameters extends IActionParameters {
    connection_id: string; // ← Security concern
    name: string;          // ← Business logic
    content?: string;      // ← Business logic
}
```

## 🔄 **Execution Flow**

### **1. Node Configuration (UI)**
```typescript
// User configures node:
{
    // Node level - Authentication
    integrationId: "google-oauth-user-123",
    
    // Parameters level - Business logic
    parameters: {
        operation: "CREATE_DOCUMENT",
        name: "My Document",
        content: "Hello World"
    }
}
```

### **2. Workflow Execution**
```typescript
async function executeGoogleDocsNode(
    node: Node,
    parameters: IGoogleDocsParameters,
    input: IGoogleDocsInput
): Promise<IGoogleDocsOutput> {
    
    // Step 1: Resolve credentials from node.integrationId
    const integration = await this.integrationService.findById(node.integrationId);
    const credentials = await this.credentialService.decrypt(integration);
    
    // Step 2: Initialize service client
    const googleDocs = new GoogleDocsClient(credentials);
    
    // Step 3: Execute business logic with parameters
    switch (parameters.operation) {
        case EGoogleDocsOperation.CREATE_DOCUMENT:
            return await googleDocs.createDocument({
                name: parameters.name,
                content: parameters.content,
                driveId: parameters.drive_id
            });
    }
}
```

## 🎯 **Benefits**

### **🔒 Security**
- Credentials tách biệt khỏi business parameters
- Không risk credential leakage trong parameter validation
- Centralized credential management

### **🏗️ Architecture**
- Clean separation of concerns
- Consistent với industry standards
- Easier to maintain và scale

### **🔄 Reusability**
```typescript
// Same parameters, different credentials
const userNode = { integrationId: "user-google", parameters: docParams };
const adminNode = { integrationId: "admin-google", parameters: docParams };
```

### **🎨 UI/UX**
```typescript
// Make.com style UI:
┌─────────────────────────────────┐
│ Google Docs                     │
├─────────────────────────────────┤
│ Connection: [My Google Account] │ ← Node level
├─────────────────────────────────┤
│ Operation: Create Document      │ ← Parameter
│ Name: [Document Name]           │ ← Parameter  
│ Content: [Document Content]     │ ← Parameter
└─────────────────────────────────┘
```

## 📝 **Implementation Checklist**

### ✅ **Interface Design**
- [ ] Remove `connection_id` from all parameter interfaces
- [ ] Keep only business logic in parameters
- [ ] Use `node.integrationId` for credential resolution

### ✅ **Node Properties**
- [ ] Remove connection properties from `PROPERTIES` arrays
- [ ] Update `dependsOn` to use `integration_id`
- [ ] Keep credential definition at node definition level

### ✅ **Validation**
- [ ] Remove credential validation from parameter validation
- [ ] Focus validation on business logic only
- [ ] Credential validation happens at node level

### ✅ **Execution Engine**
- [ ] Implement credential resolution from `node.integrationId`
- [ ] Initialize service clients with resolved credentials
- [ ] Execute operations with business parameters only

## 🌍 **Industry Standards**

### **Make.com Pattern**
- Connection selected at node level
- Parameters contain only operation-specific config
- Clean UI separation

### **n8n Pattern**  
- Credential defined in node definition
- Node instance references credential
- Parameters are pure business logic

### **Zapier Pattern**
- App authentication separate from action config
- Trigger/Action parameters focus on functionality
- Account connection managed independently

This architecture ensures our system follows industry best practices! 🚀
