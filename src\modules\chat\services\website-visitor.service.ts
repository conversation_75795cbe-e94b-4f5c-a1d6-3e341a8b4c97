import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Request } from 'express';
import { Platform } from '@/shared';
import axios from 'axios';

// Import entities
import { UserConvertCustomer } from '@/modules/business/entities/user-convert-customer.entity';
import { ExternalCustomerPlatformData } from '@/modules/business/entities/external-customer-platform-data.entity';
import { User } from '@/modules/user/entities/user.entity';
import { Settings } from '@/modules/user/entities/settings.entity';

// Import interfaces
import { PayloadLiveChatKey } from '@/modules/integration/interfaces/website.interface';
import {
  UserInfo,
  UserConvertCustomerInfo,
  WebsiteInfo,
} from '@/shared/queue/queue.types';

/**
 * Service for managing website visitor data and platform information
 * Handles UserConvertCustomer creation and ExternalCustomerPlatformData management
 */
@Injectable()
export class WebsiteVisitorService {
  private readonly logger = new Logger(WebsiteVisitorService.name);

  constructor(
    @InjectRepository(UserConvertCustomer)
    private readonly userConvertCustomerRepository: Repository<UserConvertCustomer>,
    @InjectRepository(ExternalCustomerPlatformData)
    private readonly platformDataRepository: Repository<ExternalCustomerPlatformData>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Settings)
    private readonly settingsRepository: Repository<Settings>,
  ) {}

  /**
   * Create visitor data: UserConvertCustomer + ExternalCustomerPlatformData
   */
  async createVisitorData(
    websitePayload: PayloadLiveChatKey,
    clientData?: Partial<WebsiteInfo['clientSide']>,
    request?: Request,
  ): Promise<{
    userConvertCustomerId: string;
    externalCustomerPlatformDataId: string;
  }> {
    this.logger.debug('Creating visitor data', {
      websiteId: websitePayload.websiteId,
      userId: websitePayload.userId,
    });

    // Extract server-side data from request
    const serverSideData = request ? await this.extractServerSideData(request) : {};

    // Build complete website info
    const websiteInfo = this.buildWebsiteInfo(serverSideData, clientData);

    // Create new UserConvertCustomer for this visitor session
    const userConvertCustomer = await this.createUserConvertCustomer(
      websitePayload,
      websiteInfo,
    );

    // Create ExternalCustomerPlatformData
    const platformData = await this.createExternalCustomerPlatformData(
      userConvertCustomer,
      websitePayload,
      websiteInfo,
    );

    this.logger.debug('Created visitor data successfully', {
      userConvertCustomerId: userConvertCustomer.id,
      externalCustomerPlatformDataId: platformData.id,
    });

    return {
      userConvertCustomerId: userConvertCustomer.id,
      externalCustomerPlatformDataId: platformData.id,
    };
  }

  /**
   * Create new UserConvertCustomer for each website visitor
   * Note: Each visitor session creates a NEW customer record.
   * We don't try to find existing ones because each visitor is unique.
   */
  async createUserConvertCustomer(
    websitePayload: PayloadLiveChatKey,
    websiteInfo: WebsiteInfo,
  ): Promise<UserConvertCustomer> {
    // Always create a new customer for each visitor session
    // userId refers to the website owner (business user)
    const newCustomer = this.userConvertCustomerRepository.create({
      userId: websitePayload.userId, // Website owner ID (business relationship)
      platform: Platform.WEBSITE,
      name: 'Website Visitor', // Default name for anonymous visitors
      timezone: websiteInfo.clientSide?.timeInfo?.timezone || 'UTC',
      createdAt: Date.now(),
      updatedAt: Date.now(),
      metadata: {
        source: 'website_chat',
        websiteId: websitePayload.websiteId,
        firstVisit: Date.now(),
        visitorSession: Date.now(), // Unique identifier for this visitor session
      },
    });

    const savedCustomer = await this.userConvertCustomerRepository.save(newCustomer);

    this.logger.debug('Created new UserConvertCustomer for website visitor', {
      customerId: savedCustomer.id,
      websiteOwnerId: websitePayload.userId,
      websiteId: websitePayload.websiteId,
    });

    return savedCustomer;
  }

  /**
   * Create ExternalCustomerPlatformData
   */
  async createExternalCustomerPlatformData(
    userConvertCustomer: UserConvertCustomer,
    websitePayload: PayloadLiveChatKey,
    websiteInfo: WebsiteInfo,
  ): Promise<ExternalCustomerPlatformData> {
    const platformData = this.platformDataRepository.create({
      userId: websitePayload.userId,
      userConvertCustomer,
      platform: Platform.WEBSITE,
      data: websiteInfo,
    });

    const savedPlatformData =
      await this.platformDataRepository.save(platformData);

    this.logger.debug('Created ExternalCustomerPlatformData', {
      platformDataId: savedPlatformData.id,
      userConvertCustomerId: userConvertCustomer.id,
    });

    return savedPlatformData;
  }

  /**
   * Extract server-side data from Express Request object
   */
  async extractServerSideData(request: Request): Promise<WebsiteInfo['serverSide']> {
    const clientIP = await this.extractClientIP(request);
    const forwardedIPs = this.extractForwardedIPs(request);
    
    return {
      clientIP,
      forwardedIPs,
      userAgent: request.headers['user-agent'],
      acceptLanguage: request.headers['accept-language'],
      acceptEncoding: Array.isArray(request.headers['accept-encoding'])
        ? request.headers['accept-encoding'].join(', ')
        : request.headers['accept-encoding'],
      connection: request.headers.connection as string,
      host: request.headers.host,
      origin: request.headers.origin,
      referer: request.headers.referer,
    };
  }

  /**
   * Extract the real client IP address from request
   * Handles proxy headers and localhost detection
   * For localhost, attempts to get public IP via external service
   */
  private async extractClientIP(request: Request): Promise<string> {
    // Priority order for IP extraction:
    // 1. CF-Connecting-IP (Cloudflare)
    // 2. X-Real-IP (Nginx proxy)
    // 3. X-Forwarded-For (Load balancers/proxies)
    // 4. X-Client-IP 
    // 5. X-Cluster-Client-IP
    // 6. request.ip (Express default)
    
    const cfConnectingIP = request.headers['cf-connecting-ip'] as string;
    if (cfConnectingIP && this.isValidIP(cfConnectingIP)) {
      return cfConnectingIP;
    }

    const xRealIP = request.headers['x-real-ip'] as string;
    if (xRealIP && this.isValidIP(xRealIP)) {
      return xRealIP;
    }

    const xForwardedFor = request.headers['x-forwarded-for'] as string;
    if (xForwardedFor) {
      const firstIP = xForwardedFor.split(',')[0].trim();
      if (this.isValidIP(firstIP)) {
        return firstIP;
      }
    }

    const xClientIP = request.headers['x-client-ip'] as string;
    if (xClientIP && this.isValidIP(xClientIP)) {
      return xClientIP;
    }

    const xClusterClientIP = request.headers['x-cluster-client-ip'] as string;
    if (xClusterClientIP && this.isValidIP(xClusterClientIP)) {
      return xClusterClientIP;
    }

    // Fallback to Express default (will be ::1 or 127.0.0.1 for localhost)
    const requestIP = request.ip || request.socket?.remoteAddress || 'unknown';
    
    // If localhost, try to get public IP
    if (requestIP === '::1' || requestIP === '127.0.0.1' || requestIP.startsWith('192.168.') || requestIP.startsWith('10.') || requestIP.startsWith('172.')) {
      const publicIP = await this.getPublicIP();
      if (publicIP) {
        this.logger.debug(`Localhost detected, using public IP: ${publicIP} (local was: ${requestIP})`);
        return publicIP;
      }
      
      // Convert IPv6 loopback to IPv4 for consistency if public IP lookup failed
      if (requestIP === '::1') {
        return '127.0.0.1';
      }
    }
    
    return requestIP;
  }

  /**
   * Extract and parse forwarded IPs from various headers
   */
  private extractForwardedIPs(request: Request): string[] | undefined {
    const forwardedHeaders = [
      'x-forwarded-for',
      'x-real-ip',
      'cf-connecting-ip',
      'x-client-ip',
      'x-cluster-client-ip'
    ];

    const allIPs: string[] = [];

    for (const header of forwardedHeaders) {
      const headerValue = request.headers[header] as string;
      if (headerValue) {
        if (header === 'x-forwarded-for') {
          // X-Forwarded-For can contain multiple IPs
          const ips = headerValue.split(',').map(ip => ip.trim());
          allIPs.push(...ips.filter(ip => this.isValidIP(ip)));
        } else {
          // Other headers typically contain single IP
          if (this.isValidIP(headerValue)) {
            allIPs.push(headerValue);
          }
        }
      }
    }

    return allIPs.length > 0 ? [...new Set(allIPs)] : undefined; // Remove duplicates
  }

  /**
   * Validate if a string is a valid IP address (IPv4 or IPv6)
   */
  private isValidIP(ip: string): boolean {
    if (!ip || typeof ip !== 'string') return false;
    
    // Remove any surrounding whitespace
    ip = ip.trim();
    
    // IPv4 regex
    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    
    // IPv6 regex (simplified)
    const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/;
    
    return ipv4Regex.test(ip) || ipv6Regex.test(ip);
  }

  /**
   * Get the public IP address of the server/client using external services
   * This is the same IP that curl commands would show
   */
  private async getPublicIP(): Promise<string | null> {
    const services = [
      'https://api.ipify.org?format=text',
      'https://icanhazip.com',
      'https://ipv4.icanhazip.com',
      'https://checkip.amazonaws.com',
      'https://ip.seeip.org',
    ];

    for (const service of services) {
      try {
        const response = await axios.get(service, {
          timeout: 3000, // 3 second timeout
          headers: {
            'User-Agent': 'RedAI-Backend/1.0'
          }
        });
        
        const ip = response.data.trim();
        if (this.isValidIP(ip)) {
          this.logger.debug(`Retrieved public IP from ${service}: ${ip}`);
          return ip;
        }
      } catch (error) {
        this.logger.debug(`Failed to get IP from ${service}:`, error.message);
        continue; // Try next service
      }
    }

    this.logger.warn('All public IP services failed, using local IP as fallback');
    return null;
  }

  /**
   * Build complete WebsiteInfo from server and client data
   */
  buildWebsiteInfo(
    serverSide: WebsiteInfo['serverSide'],
    clientSide?: Partial<WebsiteInfo['clientSide']>,
  ): WebsiteInfo {
    return {
      serverSide,
      clientSide: clientSide || {},
    };
  }

  /**
   * Build UserInfo for website owner (business user)
   */
  async buildWebsiteOwnerInfo(userId: number): Promise<UserInfo> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new Error(`Website owner not found: ${userId}`);
    }

    // Fetch user settings for timezone and currency
    const settings = await this.settingsRepository.findOne({
      where: { userId },
    });

    return {
      userId: user.id,
      fullName: user.fullName,
      email: user.email,
      gender: user.gender,
      dateOfBirth: user.dateOfBirth,
      type: user.type,
      countryCode: user.countryCode,
      pointsBalance: user.pointsBalance,
      isVerifyPhone: user.isVerifyPhone,
      address: user.address,
      timezone: settings?.timezone,
      currency: settings?.currency,
    };
  }

  /**
   * Build UserConvertCustomerInfo from external customer platform data
   */
  async buildWebsiteVisitorInfo(
    externalCustomerPlatformDataId: string,
  ): Promise<UserConvertCustomerInfo> {
    const platformData = await this.platformDataRepository.findOne({
      where: { id: externalCustomerPlatformDataId },
      relations: ['userConvertCustomer'],
    });

    if (!platformData) {
      throw new Error(
        `Platform data not found: ${externalCustomerPlatformDataId}`,
      );
    }

    const customer = platformData.userConvertCustomer;

    return {
      id: customer.id,
      externalPlatformId: externalCustomerPlatformDataId,
      name: customer.name,
      email: customer.email,
      phone: customer.phone,
      countryCode: customer.countryCode,
      metadata: customer.metadata,
      address: customer.address,
      tags: customer.tags,
    };
  }

  /**
   * Get website info from platform data
   */
  async getWebsiteInfo(
    externalCustomerPlatformDataId: string,
  ): Promise<WebsiteInfo> {
    const platformData = await this.platformDataRepository.findOne({
      where: { id: externalCustomerPlatformDataId },
      select: ['data'],
    });

    if (!platformData) {
      throw new Error(
        `Platform data not found: ${externalCustomerPlatformDataId}`,
      );
    }

    return platformData.data as WebsiteInfo;
  }

  /**
   * Update visitor context with new client-side data and optionally refresh server-side data
   */
  async updateVisitorContext(
    externalCustomerPlatformDataId: string,
    clientData: Partial<WebsiteInfo['clientSide']>,
    request?: Request,
  ): Promise<void> {
    const platformData = await this.platformDataRepository.findOne({
      where: { id: externalCustomerPlatformDataId },
    });

    if (!platformData) {
      throw new Error(
        `Platform data not found: ${externalCustomerPlatformDataId}`,
      );
    }

    const currentWebsiteInfo = platformData.data as WebsiteInfo;
    
    // Optionally refresh server-side data if request is provided
    const serverSideData = request 
      ? await this.extractServerSideData(request)
      : currentWebsiteInfo.serverSide;
    
    const updatedWebsiteInfo: WebsiteInfo = {
      serverSide: serverSideData,
      clientSide: {
        ...currentWebsiteInfo.clientSide,
        ...clientData,
      },
    };

    await this.platformDataRepository.update(externalCustomerPlatformDataId, {
      data: updatedWebsiteInfo as any,
    });

    this.logger.debug('Updated visitor context', {
      externalCustomerPlatformDataId,
      updatedFields: Object.keys(clientData),
    });
  }
}
