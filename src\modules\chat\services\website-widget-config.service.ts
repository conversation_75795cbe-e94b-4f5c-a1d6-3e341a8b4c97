import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CdnService } from '@/shared/services/cdn.service';

// Import entities
import { BoxChatConfig } from '@/modules/integration/entities/box-chat-config.entity';
import {
  DisplayMode,
  SideMode,
} from '@/modules/integration/entities/box-chat-config.entity';
import { BoxChatConfigMedia } from '@/modules/integration/entities/box-chat-config-media.entity';
import { Media } from '@/modules/data/media/entities/media.entity';
import { AgentConnection } from '@/modules/agent/entities/agent-connection.entity';
import { Agent } from '@/modules/agent/entities/agent.entity';

// Import DTOs
import { ChatWidgetConfigDto } from '../dto/chat-widget-config.dto';
import { PayloadLiveChatKey } from '@/modules/integration/interfaces/website.interface';
import { TimeIntervalEnum } from '@/shared/utils';
import { AppException } from '@/common';
import { CHAT_ERROR_CODES } from '../exceptions';
import { MediaStatusEnum } from '@/modules/data/media/enums';

/**
 * Service for fetching and processing chat widget configuration
 * Combines BoxChatConfig data with agent information to provide complete widget setup
 */
@Injectable()
export class WebsiteWidgetConfigService {
  private readonly logger = new Logger(WebsiteWidgetConfigService.name);

  constructor(
    @InjectRepository(BoxChatConfig)
    private readonly boxChatConfigRepository: Repository<BoxChatConfig>,
    private readonly cdnService: CdnService,
  ) {}

  /**
   * Get complete chat widget configuration for a website
   * Includes UI settings, agent information, and CDN URLs for media assets
   */
  async getWidgetConfiguration(
    websitePayload: PayloadLiveChatKey,
  ): Promise<ChatWidgetConfigDto> {
    this.logger.debug('Fetching widget configuration', {
      websiteId: websitePayload.websiteId,
    });

    // Query configuration with agent information
    const configData = await this.fetchConfigWithAgent(
      websitePayload.websiteId,
    );

    // Fetch slideshow images separately (optional)
    let slideshowImages: string[] = [];
    if (configData.config?.id) {
      slideshowImages = await this.fetchSlideshowImages(configData.config.id);
    }

    // Transform to DTO with CDN URLs
    const widgetConfig = this.buildWidgetConfigDto(configData, slideshowImages);

    this.logger.debug('Widget configuration fetched successfully', {
      websiteId: websitePayload.websiteId,
      hasConfig: !!configData.config,
      agentName: configData.agentName,
      slideshowImagesCount: slideshowImages.length,
    });

    return widgetConfig;
  }

  /**
   * Fetch box chat config with related agent information
   */
  private async fetchConfigWithAgent(integrationId: string): Promise<{
    config: BoxChatConfig;
    agentName: string;
    agentAvatar: string | null;
  }> {
    // Query with left joins to get config and agent data
    const query = this.boxChatConfigRepository
      .createQueryBuilder('config')
      .leftJoin(
        AgentConnection,
        'connection',
        'connection.integrationId = config.integrationId',
      )
      .leftJoin(Agent, 'agent', 'agent.id = connection.agentId')
      .select([
        'config.id as "configId"',
        'config.welcomeText as "welcomeText"',
        'config.avatar as "avatar"',
        'config.displayMode as "displayMode"',
        'config.sideMode as "sideMode"',
        'config.colorPrimary as "colorPrimary"',
        'config.icon as "icon"',
        'config.components as "components"',
        'config.quickMessages as "quickMessages"',
        'config.placeholderMessage as "placeholderMessage"',
        'agent.name as "agentName"',
        'agent.avatar as "agentAvatar"',
      ])
      .where('config.integrationId = :integrationId', { integrationId });

    const result = await query.getRawOne();

    // Handle case where no config exists
    if (!result) {
      this.logger.debug('No box chat config found, using defaults', {
        integrationId,
      });
      throw new AppException(
        CHAT_ERROR_CODES.NO_BOX_CHAT_CONFIG,
        `No chat widget configuration found for integration ID ${integrationId}`,
      );
    }

    // Build config object from raw result
    const config: BoxChatConfig = {
      id: result.configId,
      welcomeText: result.welcomeText,
      avatar: result.avatar,
      displayMode: result.displayMode,
      sideMode: result.sideMode,
      colorPrimary: result.colorPrimary,
      icon: result.icon,
      components: result.components,
      quickMessages: result.quickMessages || [],
      placeholderMessage: result.placeholderMessage,
      integrationId: integrationId,
    };

    return {
      config,
      agentName: result.agentName || 'Assistant',
      agentAvatar: result.agentAvatar || null,
    };
  }

  /**
   * Fetch slideshow images for a specific box chat config ID
   */
  private async fetchSlideshowImages(
    boxChatConfigId: number,
  ): Promise<string[]> {
    const query = this.boxChatConfigRepository
      .createQueryBuilder('config')
      .leftJoin(
        BoxChatConfigMedia,
        'configMedia',
        'configMedia.boxChatId = config.id',
      )
      .leftJoin(
        Media,
        'media',
        'media.id = configMedia.mediaId AND media.status NOT IN (:...excludedStatuses)',
        {
          excludedStatuses: [MediaStatusEnum.DELETED, MediaStatusEnum.REJECTED],
        },
      )
      .select(['media.storageKey as "storageKey"'])
      .where('config.id = :boxChatConfigId', { boxChatConfigId })
      .andWhere('media.storageKey IS NOT NULL');

    const results = await query.getRawMany();

    // Extract unique storage keys
    const storageKeys = results
      .map((row) => row.storageKey)
      .filter((key, index, array) => array.indexOf(key) === index); // Remove duplicates

    return storageKeys || [];
  }

  /**
   * Build ChatWidgetConfigDto with proper CDN URLs and defaults
   */
  private buildWidgetConfigDto(
    data: {
      config: BoxChatConfig;
      agentName: string;
      agentAvatar: string | null;
    },
    slideshowImages: string[] = [],
  ): ChatWidgetConfigDto {
    const { config, agentName, agentAvatar } = data;

    // Generate CDN URLs for media assets
    const avatarUrl = config?.avatar
      ? this.cdnService.generateUrlView(
          config.avatar,
          TimeIntervalEnum.FIVE_MINUTES,
        )
      : undefined;

    const iconUrl = config?.icon
      ? this.cdnService.generateUrlView(
          config.icon,
          TimeIntervalEnum.FIVE_MINUTES,
        )
      : undefined;

    const agentAvatarUrl = agentAvatar
      ? this.cdnService.generateUrlView(
          agentAvatar,
          TimeIntervalEnum.FIVE_MINUTES,
        )
      : undefined;

    // Generate CDN URLs for slideshow images
    const slideshowImageUrls: string[] = slideshowImages
      .map((storageKey) =>
        this.cdnService.generateUrlView(
          storageKey,
          TimeIntervalEnum.FIVE_MINUTES,
        ),
      )
      .filter((url) => !!url) as string[];

    // Build DTO with config data or defaults
    return new ChatWidgetConfigDto({
      welcomeText: config.welcomeText || 'Xin chào, tôi có thể giúp gì bạn?',
      placeholderMessage: config.placeholderMessage || undefined,
      displayMode: config.displayMode || DisplayMode.CENTER,
      sideMode: config.sideMode || SideMode.FLOATING,
      colorPrimary: config.colorPrimary,
      components: config.components,
      quickMessages: config.quickMessages || [],
      avatarUrl: avatarUrl || '',
      iconUrl: iconUrl || '',
      agentName,
      agentAvatarUrl: agentAvatarUrl || '',
      slideshowImages: slideshowImageUrls || [],
    });
  }
}
