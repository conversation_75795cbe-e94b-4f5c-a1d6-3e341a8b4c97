import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Integration } from '../entities/integration.entity';
import { IntegrationProvider } from '../entities/integration-provider.entity';
import { ProviderEnum } from '../constants/provider.enum';
import { OwnedTypeEnum } from '../enums/owned-type.enum';
import { ZaloPersonalMetadata } from '../interfaces/zalo-personal-metadata.interface';

/**
 * Repository cho Zalo Personal Integration
 * Xử lý các thao tác database liên quan đến Zalo Personal Integration
 */
@Injectable()
export class ZaloPersonalIntegrationRepository {
  private readonly logger = new Logger(ZaloPersonalIntegrationRepository.name);

  constructor(
    @InjectRepository(Integration)
    private readonly integrationRepository: Repository<Integration>,
    @InjectRepository(IntegrationProvider)
    private readonly integrationProviderRepository: Repository<IntegrationProvider>,
  ) {}

  /**
   * Tìm provider ZALO_PERSONAL
   */
  async findZaloPersonalProvider(): Promise<IntegrationProvider> {
    const provider = await this.integrationProviderRepository.findOne({
      where: { type: ProviderEnum.ZALO_PERSONAL },
    });

    if (!provider) {
      throw new Error('ZALO_PERSONAL provider not found');
    }

    return provider;
  }

  /**
   * Tạo Zalo Personal Integration
   */
  async createZaloPersonalIntegration(data: {
    integrationName: string;
    typeId: number;
    userId?: number;
    employeeId?: number;
    encryptedConfig: string;
    secretKey: string;
    metadata: ZaloPersonalMetadata;
  }): Promise<Integration> {
    const integration = this.integrationRepository.create({
      integrationName: data.integrationName,
      typeId: data.typeId,
      userId: data.userId || undefined,
      employeeId: data.employeeId || undefined,
      ownedType: data.userId ? OwnedTypeEnum.USER : OwnedTypeEnum.ADMIN,
      encryptedConfig: data.encryptedConfig,
      secretKey: data.secretKey,
      metadata: data.metadata as any,
    });

    return await this.integrationRepository.save(integration);
  }

  /**
   * Tìm Zalo Personal Integration theo userId và zaloUserId
   */
  async findByUserIdAndZaloUserId(
    userId: number,
    zaloUserId: string,
  ): Promise<Integration | null> {
    const provider = await this.findZaloPersonalProvider();

    return await this.integrationRepository.findOne({
      where: {
        userId,
        typeId: provider.id,
        metadata: {
          zalo_uid: zaloUserId,
        } as any,
      },
    });
  }

  /**
   * Tìm Zalo Personal Integration theo employeeId và zaloUserId
   */
  async findByEmployeeIdAndZaloUserId(
    employeeId: number,
    zaloUserId: string,
  ): Promise<Integration | null> {
    const provider = await this.findZaloPersonalProvider();

    return await this.integrationRepository.findOne({
      where: {
        employeeId,
        typeId: provider.id,
        metadata: {
          zalo_uid: zaloUserId,
        } as any,
      },
    });
  }

  /**
   * Tìm Zalo Personal Integration theo ID
   */
  async findById(id: string): Promise<Integration | null> {
    const provider = await this.findZaloPersonalProvider();

    return await this.integrationRepository.findOne({
      where: {
        id,
        typeId: provider.id,
      },
    });
  }

  /**
   * Lấy danh sách Zalo Personal Integration theo userId
   */
  async findByUserId(userId: number): Promise<Integration[]> {
    const provider = await this.findZaloPersonalProvider();

    return await this.integrationRepository.find({
      where: {
        userId,
        typeId: provider.id,
      },
      order: {
        createdAt: 'DESC',
      },
    });
  }

  /**
   * Lấy danh sách Zalo Personal Integration theo employeeId
   */
  async findByEmployeeId(employeeId: number): Promise<Integration[]> {
    const provider = await this.findZaloPersonalProvider();

    return await this.integrationRepository.find({
      where: {
        employeeId,
        typeId: provider.id,
      },
      order: {
        createdAt: 'DESC',
      },
    });
  }

  /**
   * Cập nhật Zalo Personal Integration
   */
  async updateZaloPersonalIntegration(
    id: string,
    data: {
      integrationName?: string;
      encryptedConfig?: string;
      secretKey?: string;
      metadata?: ZaloPersonalMetadata;
    },
  ): Promise<Integration> {
    await this.integrationRepository.update(id, {
      integrationName: data.integrationName,
      encryptedConfig: data.encryptedConfig,
      secretKey: data.secretKey,
      metadata: data.metadata as any,
    });

    const updatedIntegration = await this.findById(id);
    if (!updatedIntegration) {
      throw new Error(
        `Zalo Personal Integration with ID ${id} not found after update`,
      );
    }

    return updatedIntegration;
  }

  /**
   * Xóa Zalo Personal Integration
   */
  async deleteZaloPersonalIntegration(id: string): Promise<void> {
    const result = await this.integrationRepository.delete(id);

    if (result.affected === 0) {
      throw new Error(`Zalo Personal Integration with ID ${id} not found`);
    }
  }

  /**
   * Kiểm tra xem Zalo Personal Integration có tồn tại không
   */
  async exists(userId: number, zaloUserId: string): Promise<boolean> {
    const integration = await this.findByUserIdAndZaloUserId(
      userId,
      zaloUserId,
    );
    return !!integration;
  }
}
