[{"typeName": "facebook-campaign-management", "version": 1, "displayName": "Facebook Campaign Management", "description": "Manage Facebook Ads campaigns, ad sets, and ads. List, update campaigns and ad sets, get reach estimates, search ad interests and locations.", "groupName": "integration", "icon": "facebook", "properties": [{"name": "operation", "displayName": "Operation", "type": "options", "required": true, "default": "listCampaigns", "description": "<PERSON><PERSON><PERSON> thao tác cần thực hiện", "options": [{"name": "List Campaigns", "value": "listCampaigns"}, {"name": "Update a Campaign", "value": "updateCampaign"}, {"name": "List Ad Sets", "value": "listAdSets"}, {"name": "Update an Ad Set", "value": "updateAdSet"}, {"name": "List Ads", "value": "listAds"}, {"name": "Update an Ad", "value": "updateAd"}, {"name": "Get a Reach Estimate", "value": "getReachEstimate"}, {"name": "Search Ad Interests", "value": "searchAdInterests"}, {"name": "Search Locations", "value": "searchLocations"}]}, {"name": "business_id", "displayName": "Business ID", "type": "string", "required": true, "displayOptions": {"show": {"operation": ["listCampaigns", "updateCampaign", "listAdSets", "updateAdSet", "listAds", "updateAd", "getReachEstimate"]}}}, {"name": "name", "displayName": "Name", "type": "string", "required": true, "description": "Tên sở thích để tìm kiếm", "displayOptions": {"show": {"operation": ["searchAdInterests"]}}}, {"name": "name_update", "displayName": "Name", "type": "string", "description": "Tên chiến dịch/ad set/ad", "displayOptions": {"show": {"operation": ["updateCampaign", "updateAdSet", "updateAd"]}}}, {"name": "location_types", "displayName": "Location Types", "type": "options", "required": true, "multipleValues": true, "description": "<PERSON><PERSON><PERSON> địa điểm cần tìm kiếm", "options": [{"name": "Country", "value": "country"}, {"name": "Region", "value": "region"}, {"name": "City", "value": "city"}, {"name": "Zip", "value": "zip"}, {"name": "DMA", "value": "dma"}, {"name": "Electoral District", "value": "electoral_district"}, {"name": "Country Group", "value": "country_group"}], "displayOptions": {"show": {"operation": ["searchLocations"]}}}, {"name": "query", "displayName": "Query", "type": "string", "description": "Từ khóa tìm kiếm địa điểm", "displayOptions": {"show": {"operation": ["searchLocations"]}}}, {"name": "locale", "displayName": "Locale", "type": "string", "description": "<PERSON><PERSON>n ngữ/vùng miền. Ví dụ: en_US", "displayOptions": {"show": {"operation": ["searchAdInterests"]}}}, {"name": "effective_status", "displayName": "Effective Status", "type": "options", "description": "Trạng thái hi<PERSON>u l<PERSON> của ad sets/ads", "multipleValues": true, "options": [{"name": "Active", "value": "ACTIVE"}, {"name": "Paused", "value": "PAUSED"}, {"name": "Pending Review", "value": "PENDING_REVIEW"}, {"name": "Disapproved", "value": "DISAPPROVED"}, {"name": "Preapproved", "value": "PREAPPROVED"}, {"name": "Pending Billing Info", "value": "PENDING_BILLING_INFO"}, {"name": "Campaign Paused", "value": "CAMPAIGN_PAUSED"}, {"name": "Archived", "value": "ARCHIVED"}, {"name": "Adset Paused", "value": "ADSET_PAUSED"}, {"name": "In Process", "value": "IN_PROCESS"}, {"name": "With Issues", "value": "WITH_ISSUES"}], "displayOptions": {"show": {"operation": ["listAdSets", "listAds"]}}}, {"name": "status", "displayName": "Status", "type": "options", "description": "Trạng thái chiến dịch/ad set/ad", "options": [{"name": "Active", "value": "ACTIVE"}, {"name": "Paused", "value": "PAUSED"}, {"name": "Deleted", "value": "DELETED"}, {"name": "Archived", "value": "ARCHIVED"}], "displayOptions": {"show": {"operation": ["updateCampaign", "updateAdSet", "updateAd"]}}}, {"name": "limit_list_campaigns", "displayName": "Limit", "type": "number", "default": 10, "minValue": 1, "maxValue": 100, "description": "The maximum number of results to be worked with during one execution cycle", "displayOptions": {"show": {"operation": ["listCampaigns"]}}}, {"name": "limit_list_ad_sets", "displayName": "Limit", "type": "number", "default": 10, "minValue": 1, "maxValue": 100, "description": "The maximum number of results to be worked with during one execution cycle", "displayOptions": {"show": {"operation": ["listAdSets"]}}}, {"name": "limit_list_ads", "displayName": "Limit", "type": "number", "default": 10, "minValue": 1, "maxValue": 100, "description": "The maximum number of results to be worked with during one execution cycle", "displayOptions": {"show": {"operation": ["listAds"]}}}, {"name": "limit_search_ad_interests", "displayName": "Limit", "type": "number", "default": 10, "minValue": 1, "maxValue": 100, "description": "The maximum number of results to be worked with during one execution cycle", "displayOptions": {"show": {"operation": ["searchAdInterests"]}}}, {"name": "limit_search_locations", "displayName": "Limit", "type": "number", "default": 10, "minValue": 1, "maxValue": 100, "description": "The maximum number of results to be worked with during one execution cycle", "displayOptions": {"show": {"operation": ["searchLocations"]}}}, {"name": "daily_budget", "displayName": "Daily Budget", "type": "number", "description": "<PERSON>ân sách hàng ngày nhân với hệ số tiền tệ. Ví dụ: nhập \"10000\" cho ngân sách 100 USD", "displayOptions": {"show": {"operation": ["updateCampaign"]}}}, {"name": "lifetime_budget", "displayName": "Lifetime Budget", "type": "number", "description": "<PERSON>ân sách trọn đời nhân với hệ số tiền tệ. Ví dụ: nhập \"10000\" cho ngân sách 100 USD", "displayOptions": {"show": {"operation": ["updateCampaign"]}}}, {"name": "spend_cap", "displayName": "Spend Cap", "type": "number", "description": "Giới hạn chi tiêu nhân với hệ số tiền tệ. Ví dụ: nhập \"10000\" cho giới hạn 100 USD", "displayOptions": {"show": {"operation": ["updateCampaign"]}}}, {"name": "start_time", "displayName": "Start Time", "type": "dateTime", "description": "<PERSON><PERSON><PERSON><PERSON> gian b<PERSON>t đầu chiến dịch (Time zone: Asia/Bangkok)", "displayOptions": {"show": {"operation": ["updateCampaign"]}}}, {"name": "stop_time", "displayName": "Stop Time", "type": "dateTime", "description": "<PERSON><PERSON><PERSON><PERSON> gian kết thúc chiến dịch (Time zone: Asia/Bangkok)", "displayOptions": {"show": {"operation": ["updateCampaign"]}}}, {"name": "bid_strategy", "displayName": "Bid Strategy", "type": "string", "description": "<PERSON><PERSON><PERSON> l<PERSON><PERSON><PERSON> đấu giá", "displayOptions": {"show": {"operation": ["updateCampaign"]}}}, {"name": "bid_amount_ad", "displayName": "<PERSON><PERSON>", "type": "number", "description": "Số tiền đấu giá nhân với hệ số tiền tệ. Ví dụ: nhập \"1\" cho 0.01 USD hoặc 1 JPY", "displayOptions": {"show": {"operation": ["updateAd"]}}}, {"name": "object_store_url", "displayName": "Object Store URL", "type": "string", "description": "URL của ứng dụng trong app store (dùng cho mobile app campaign)", "displayOptions": {"show": {"operation": ["getReachEstimate"]}}}], "inputs": ["main"], "outputs": ["main"], "credentials": [{"provider": "facebook", "name": "facebookOAuth", "displayName": "Facebook Ads Campaign Management OAuth2", "description": "OAuth2 authentication for Facebook Ads Campaign Management", "required": true, "authType": "oauth2", "testable": true, "testUrl": "/api/integrations/test-connection"}]}, {"typeName": "facebook-custom-audiences", "version": 1, "displayName": "Facebook Custom Audiences", "description": "Create and manage Facebook Custom Audiences and Lookalike Audiences. Add users, create lookalikes, and manage audience members.", "groupName": "integration", "icon": "facebook", "properties": [{"name": "operation", "displayName": "Operation", "type": "options", "required": true, "default": "createCustomAudience", "description": "<PERSON><PERSON><PERSON> thao tác cần thực hiện", "options": [{"name": "Create a Custom Audience", "value": "createCustomAudience"}, {"name": "Add Emails to a Custom Audience", "value": "addEmailsToCustomAudience"}, {"name": "Add Users to a Custom Audience", "value": "addUsersToCustomAudience"}, {"name": "Remove Audience Members", "value": "removeAudienceMembers"}, {"name": "Create a Lookalike Audience", "value": "createLookalikeAudience"}, {"name": "Create a Page Fan Lookalike Audience", "value": "createPageFanLookalikeAudience"}, {"name": "Create a Campaign or Ad Set Conversion Lookalikes", "value": "createCampaignConversionLookalikes"}, {"name": "Create a Value-Based Custom Audience", "value": "createValueBasedCustomAudience"}, {"name": "Populate a Seed Audience", "value": "populateSeedAudience"}, {"name": "Create a Value-Based Lookalike", "value": "createValueBasedLookalike"}]}, {"name": "business_manager", "displayName": "Business Manager", "type": "string", "required": true, "description": "Business Manager ID", "displayOptions": {"show": {"operation": ["createCustomAudience", "addEmailsToCustomAudience", "addUsersToCustomAudience", "removeAudienceMembers", "createLookalikeAudience", "createPageFanLookalikeAudience", "createCampaignConversionLookalikes", "createValueBasedCustomAudience", "populateSeedAudience", "createValueBasedLookalike"]}}}, {"name": "name", "displayName": "Name", "type": "string", "required": true, "description": "<PERSON><PERSON><PERSON> c<PERSON> audience", "displayOptions": {"show": {"operation": ["createCustomAudience", "createLookalikeAudience", "createValueBasedCustomAudience", "createValueBasedLookalike"]}}}, {"name": "description", "displayName": "Description", "type": "string", "description": "Mô tả cho custom audience", "displayOptions": {"show": {"operation": ["createCustomAudience"]}}}, {"name": "emails", "displayName": "Emails", "type": "array", "required": true, "description": "<PERSON><PERSON> email cần thêm vào custom audience", "default": {}, "properties": [{"name": "email", "displayName": "Email", "type": "string", "required": true, "description": "Đ<PERSON>a chỉ email"}], "displayOptions": {"show": {"operation": ["addEmailsToCustomAudience"]}}}, {"name": "data", "displayName": "Data", "type": "array", "required": true, "description": "<PERSON><PERSON> sách dữ liệu người dùng cần thêm vào custom audience", "default": {}, "properties": [{"name": "email", "displayName": "Email", "type": "string", "description": "Đ<PERSON>a chỉ email"}, {"name": "phone", "displayName": "Phone", "type": "string", "description": "<PERSON><PERSON> điện thoại (loại bỏ ký hiệu, chữ cái, số 0 đầu. Thêm mã quốc gia nếu không có trường COUNTRY)"}, {"name": "gender", "displayName": "Gender", "type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h"}, {"name": "birth_year", "displayName": "Birth year", "type": "string", "description": "<PERSON><PERSON><PERSON>h (định dạng YYYY từ 1900 đến năm hiện tại)"}, {"name": "birth_month", "displayName": "Birth month", "type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> (định dạng MM: 01-12)"}, {"name": "birth_day", "displayName": "Birth day", "type": "string", "description": "<PERSON><PERSON><PERSON> (định dạng DD: 01-31)"}, {"name": "first_name", "displayName": "First name", "type": "string", "description": "T<PERSON><PERSON> (chỉ a-z, không dấu câu, ký tự đặc biệt UTF8)"}, {"name": "last_name", "displayName": "Last name", "type": "string", "description": "<PERSON><PERSON> (chỉ a-z, không dấu câu, ký tự đặc biệt UTF8)"}, {"name": "first_name_initial", "displayName": "First name initial", "type": "string", "description": "<PERSON>ữ cái đầu tên (chỉ a-z, ký tự đặc biệt UTF8)"}, {"name": "state", "displayName": "State", "type": "string", "description": "Bang/Tỉnh (mã vi<PERSON><PERSON> tắt ANSI 2 ký tự)"}, {"name": "city", "displayName": "City", "type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> phố (chỉ a-z, không dấu câu)"}, {"name": "zip", "displayName": "ZIP", "type": "string", "description": "<PERSON><PERSON> b<PERSON>u đi<PERSON>n"}, {"name": "country", "displayName": "Country", "type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> gia (mã 2 chữ cái ISO 3166-1 alpha-2)"}, {"name": "mobile_advertiser_id", "displayName": "Mobile advertiser ID", "type": "string", "description": "ID quảng cáo di động"}, {"name": "external_id", "displayName": "External ID", "type": "string", "description": "<PERSON> bên ngo<PERSON>i"}], "displayOptions": {"show": {"operation": ["addUsersToCustomAudience", "populateSeedAudience"]}}}], "inputs": ["main"], "outputs": ["main"], "credentials": [{"provider": "facebook", "name": "facebookOAuth", "displayName": "Facebook Custom Audiences OAuth2", "description": "OAuth2 authentication for Facebook Custom Audiences", "required": true, "authType": "oauth2", "testable": true, "testUrl": "/api/integrations/test-connection"}]}, {"typeName": "facebook-lead-ads", "version": 1, "displayName": "Facebook Lead Ads", "description": "Manage Facebook Lead Ads webhooks and lead data. Handle new leads, get lead details, manage forms and webhooks.", "groupName": "integration", "icon": "facebook", "properties": [{"name": "operation", "displayName": "Operation", "type": "options", "required": true, "options": [{"name": "New Lead (Webhook)", "value": "newLead"}, {"name": "Get Lead Detail", "value": "getLeadDetail"}, {"name": "Get a Form", "value": "getForm"}, {"name": "Unsubscribe a Webhook", "value": "unsubscribeWebhook"}, {"name": "List Leads", "value": "listLeads"}]}, {"name": "webhook_name", "displayName": "Webhook Name", "type": "string", "required": true, "displayOptions": {"show": {"operation": ["newLead"]}}}, {"name": "page_id", "displayName": "Page", "type": "options", "required": true, "loadOptions": {"resource": "integrations", "method": "getConnected", "dependsOn": ["integration_id"]}}, {"name": "lead_id", "displayName": "Lead ID", "type": "string", "required": true, "displayOptions": {"show": {"operation": ["getLeadDetail"]}}}, {"name": "form_id", "displayName": "Form ID", "type": "string", "required": true, "displayOptions": {"show": {"operation": ["getForm"]}}}, {"name": "output_fields", "displayName": "Output Fields", "type": "options", "required": false, "default": "all", "options": [{"name": "Select All", "value": "all"}, {"name": "Allow Organic Lead", "value": "allow_organic_lead"}, {"name": "Block Display for Non-targeted Viewer", "value": "block_display_for_non_targeted_viewer"}, {"name": "Context Card", "value": "context_card"}, {"name": "Created Time", "value": "created_time"}, {"name": "Expired Leads Count", "value": "expired_leads_count"}, {"name": "Follow-up Action Text", "value": "followup_action_text"}, {"name": "Follow-up Action URL", "value": "followup_action_url"}, {"name": "Is Optimized for Quality", "value": "is_optimized_for_quality"}, {"name": "Leads Count", "value": "leads_count"}, {"name": "Legal Content", "value": "legal_content"}, {"name": "Locale", "value": "locale"}, {"name": "Name", "value": "name"}, {"name": "Organic Leads Count", "value": "organic_leads_count"}, {"name": "Page", "value": "page"}, {"name": "Page ID", "value": "page_id"}, {"name": "Privacy Policy URL", "value": "privacy_policy_url"}, {"name": "Question Page Custom Headline", "value": "question_page_custom_headline"}, {"name": "Questions", "value": "questions"}, {"name": "Status", "value": "status"}, {"name": "Tracking Parameters", "value": "tracking_parameters"}, {"name": "Thank You Page", "value": "thank_you_page"}], "multipleValues": true, "displayOptions": {"show": {"operation": ["getForm"]}}}, {"name": "webhook_id", "displayName": "Webhook ID", "type": "string", "required": false, "displayOptions": {"show": {"operation": ["unsubscribeWebhook"]}}}, {"name": "limit", "displayName": "Limit", "type": "number", "required": true, "default": 25, "minValue": 1, "maxValue": 100, "displayOptions": {"show": {"operation": ["listLeads"]}}}, {"name": "older_than", "displayName": "Older Than", "type": "dateTime", "required": false, "description": "Filter leads older than this date", "displayOptions": {"show": {"operation": ["listLeads"]}}}], "inputs": ["main"], "outputs": ["main"], "credentials": [{"provider": "facebook", "name": "facebookOAuth", "displayName": "Facebook Lead Ads OAuth2", "description": "OAuth2 authentication for Facebook Lead Ads", "required": true, "authType": "oauth2", "testable": true, "testUrl": "/api/integrations/test-connection"}]}]