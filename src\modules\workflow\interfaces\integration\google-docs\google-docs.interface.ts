/**
 * @file Google Docs Integration Node Interface
 * 
 * Đ<PERSON><PERSON> nghĩa type-safe interface cho Google Docs node operations
 * Theo patterns từ Make.com và n8n industry standards
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

import {
    IBaseIntegrationParameters,
    ITriggerParameters,
    IActionParameters,
    ISearchParameters,
    IBaseIntegrationInput,
    IBaseIntegrationOutput,
    EIntegrationOperationType,
    EIntegrationErrorHandling,
    IBaseIntegrationCredential
} from '../base/base-integration.interface';

import {
    ECredentialName,
    ENodeAuthType,
    EPropertyType,
    ELoadOptionsResource,
    ELoadOptionsMethod,
    INodeProperty
} from '../../node-manager.interface';

import {
    ITypedNodeExecution
} from '../../execute.interface';

// =================================================================
// SECTION 1: GOOGLE DOCS ENUMS
// =================================================================

/**
 * Google Docs specific operations
 */
export enum EGoogleDocsOperation {
    // === TRIGGERS ===
    /** Watch for document updates */
    WATCH_DOCUMENT = 'watchDocument',
    /** Watch for new documents */
    WATCH_NEW_DOCUMENTS = 'watchNewDocuments',

    // === DOCUMENT ACTIONS ===
    /** Create new document */
    CREATE_DOCUMENT = 'createDocument',
    /** Get document content */
    GET_DOCUMENT = 'getDocument',
    /** Update document */
    UPDATE_DOCUMENT = 'updateDocument',
    /** Delete document */
    DELETE_DOCUMENT = 'deleteDocument',

    // === CONTENT ACTIONS ===
    /** Insert text */
    INSERT_TEXT = 'insertText',
    /** Replace text */
    REPLACE_TEXT = 'replaceText',
    /** Insert image */
    INSERT_IMAGE = 'insertImage',
    /** Insert table */
    INSERT_TABLE = 'insertTable',
    /** Insert page break */
    INSERT_PAGE_BREAK = 'insertPageBreak',

    // === FORMATTING ACTIONS ===
    /** Apply text style */
    APPLY_TEXT_STYLE = 'applyTextStyle',
    /** Apply paragraph style */
    APPLY_PARAGRAPH_STYLE = 'applyParagraphStyle',

    // === SEARCHES ===
    /** Search documents */
    SEARCH_DOCUMENTS = 'searchDocuments',
    /** List documents */
    LIST_DOCUMENTS = 'listDocuments',
    /** Search text in document */
    SEARCH_TEXT = 'searchText'
}

/**
 * Google Docs text alignment
 */
export enum ETextAlignment {
    /** Left alignment */
    START = 'START',
    /** Center alignment */
    CENTER = 'CENTER',
    /** Right alignment */
    END = 'END',
    /** Justify alignment */
    JUSTIFIED = 'JUSTIFIED'
}

/**
 * Google Docs named styles
 */
export enum ENamedStyleType {
    /** Normal text */
    NORMAL_TEXT = 'NORMAL_TEXT',
    /** Title */
    TITLE = 'TITLE',
    /** Subtitle */
    SUBTITLE = 'SUBTITLE',
    /** Heading 1 */
    HEADING_1 = 'HEADING_1',
    /** Heading 2 */
    HEADING_2 = 'HEADING_2',
    /** Heading 3 */
    HEADING_3 = 'HEADING_3',
    /** Heading 4 */
    HEADING_4 = 'HEADING_4',
    /** Heading 5 */
    HEADING_5 = 'HEADING_5',
    /** Heading 6 */
    HEADING_6 = 'HEADING_6'
}

// =================================================================
// SECTION 2: GOOGLE DOCS PARAMETERS
// =================================================================

/**
 * Watch Document trigger parameters
 */
export interface IWatchDocumentParameters extends ITriggerParameters {
    operation: EGoogleDocsOperation.WATCH_DOCUMENT;

    /** Document ID to watch */
    document_id: string;

    /** Include revision history */
    include_revisions?: boolean;

    /** Watch for specific changes */
    watch_changes?: ('content' | 'suggestions' | 'comments')[];
}

/**
 * Create Document parameters
 */
export interface ICreateDocumentParameters extends IActionParameters {
    operation: EGoogleDocsOperation.CREATE_DOCUMENT;

    /** Document name/title */
    name: string;

    /** Document content (supports HTML format) */
    content?: string;

    /** Content format type */
    content_format?: 'text' | 'html';

    /** Choose a Drive */
    drive_id?: string;

    /** New Document's Location - folder ID */
    folder_id?: string;

    /** Use location mapping */
    use_location_mapping?: boolean;

    /** Template document ID */
    template_id?: string;
}

/**
 * Insert Text parameters
 */
export interface IInsertTextParameters extends IActionParameters {
    operation: EGoogleDocsOperation.INSERT_TEXT;

    /** Document ID */
    document_id: string;

    /** Text to insert */
    text: string;

    /** Insert location (index) */
    location: number;

    /** Text style */
    text_style?: {
        bold?: boolean;
        italic?: boolean;
        underline?: boolean;
        strikethrough?: boolean;
        font_size?: number;
        font_family?: string;
        foreground_color?: string;
        background_color?: string;
    };
}

/**
 * Replace Text parameters
 */
export interface IReplaceTextParameters extends IActionParameters {
    operation: EGoogleDocsOperation.REPLACE_TEXT;

    /** Document ID */
    document_id: string;

    /** Text to find */
    find_text: string;

    /** Replacement text */
    replace_text: string;

    /** Replace all occurrences */
    replace_all?: boolean;

    /** Match case */
    match_case?: boolean;
}

/**
 * Insert Image parameters
 */
export interface IInsertImageParameters extends IActionParameters {
    operation: EGoogleDocsOperation.INSERT_IMAGE;

    /** Document ID */
    document_id: string;

    /** Image URL or base64 data */
    image: string;

    /** Insert location (index) */
    location: number;

    /** Image width (points) */
    width?: number;

    /** Image height (points) */
    height?: number;

    /** Alt text */
    alt_text?: string;
}

/**
 * Insert Table parameters
 */
export interface IInsertTableParameters extends IActionParameters {
    operation: EGoogleDocsOperation.INSERT_TABLE;

    /** Document ID */
    document_id: string;

    /** Insert location (index) */
    location: number;

    /** Number of rows */
    rows: number;

    /** Number of columns */
    columns: number;

    /** Table data */
    data?: string[][];

    /** Include header row */
    include_header?: boolean;
}

/**
 * Apply Text Style parameters
 */
export interface IApplyTextStyleParameters extends IActionParameters {
    operation: EGoogleDocsOperation.APPLY_TEXT_STYLE;

    /** Document ID */
    document_id: string;

    /** Start index */
    start_index: number;

    /** End index */
    end_index: number;

    /** Text style */
    text_style: {
        bold?: boolean;
        italic?: boolean;
        underline?: boolean;
        strikethrough?: boolean;
        font_size?: number;
        font_family?: string;
        foreground_color?: string;
        background_color?: string;
    };
}

/**
 * Apply Paragraph Style parameters
 */
export interface IApplyParagraphStyleParameters extends IActionParameters {
    operation: EGoogleDocsOperation.APPLY_PARAGRAPH_STYLE;

    /** Document ID */
    document_id: string;

    /** Start index */
    start_index: number;

    /** End index */
    end_index: number;

    /** Paragraph style */
    paragraph_style: {
        named_style_type?: ENamedStyleType;
        alignment?: ETextAlignment;
        line_spacing?: number;
        space_above?: number;
        space_below?: number;
        indent_first_line?: number;
        indent_start?: number;
        indent_end?: number;
    };
}

/**
 * Search Text parameters
 */
export interface ISearchTextParameters extends ISearchParameters {
    operation: EGoogleDocsOperation.SEARCH_TEXT;

    /** Document ID */
    document_id: string;

    /** Search query */
    query: string;

    /** Match case */
    match_case?: boolean;

    /** Include context */
    include_context?: boolean;

    /** Context length */
    context_length?: number;
}

/**
 * Union type cho tất cả Google Docs parameters
 */
export type IGoogleDocsParameters = 
    | IWatchDocumentParameters
    | ICreateDocumentParameters
    | IInsertTextParameters
    | IReplaceTextParameters
    | IInsertImageParameters
    | IInsertTableParameters
    | IApplyTextStyleParameters
    | IApplyParagraphStyleParameters
    | ISearchTextParameters;

// =================================================================
// SECTION 3: INPUT/OUTPUT INTERFACES
// =================================================================

/**
 * Google Docs input interface
 */
export interface IGoogleDocsInput extends IBaseIntegrationInput {
    /** Document data */
    document?: {
        id?: string;
        title?: string;
        url?: string;
        content?: string;
    };

    /** Text data */
    text?: {
        content?: string;
        style?: any;
        location?: number;
    };

    /** Image data */
    image?: {
        url?: string;
        data?: any;
        width?: number;
        height?: number;
        alt_text?: string;
    };

    /** Table data */
    table?: {
        rows?: number;
        columns?: number;
        data?: string[][];
    };
}

/**
 * Google Docs output interface
 */
export interface IGoogleDocsOutput extends IBaseIntegrationOutput {
    /** Google Docs specific data */
    google_docs?: {
        document_id?: string;
        document_url?: string;
        revision_id?: string;
        word_count?: number;
        character_count?: number;
        page_count?: number;
        content?: string;
        changes?: Array<{
            type: string;
            location: number;
            content: string;
        }>;
    };
}

// =================================================================
// SECTION 4: NODE PROPERTIES DEFINITION
// =================================================================

/**
 * Google Docs node properties
 */
export const GOOGLE_DOCS_PROPERTIES: INodeProperty[] = [
    {
        name: 'operation',
        displayName: 'Operation',
        type: EPropertyType.Options,
        required: true,
        options: [
            // Triggers
            { name: 'Watch Document', value: EGoogleDocsOperation.WATCH_DOCUMENT },
            { name: 'Watch New Documents', value: EGoogleDocsOperation.WATCH_NEW_DOCUMENTS },
            
            // Document Actions
            { name: 'Create Document', value: EGoogleDocsOperation.CREATE_DOCUMENT },
            { name: 'Get Document', value: EGoogleDocsOperation.GET_DOCUMENT },
            { name: 'Update Document', value: EGoogleDocsOperation.UPDATE_DOCUMENT },
            
            // Content Actions
            { name: 'Insert Text', value: EGoogleDocsOperation.INSERT_TEXT },
            { name: 'Replace Text', value: EGoogleDocsOperation.REPLACE_TEXT },
            { name: 'Insert Image', value: EGoogleDocsOperation.INSERT_IMAGE },
            { name: 'Insert Table', value: EGoogleDocsOperation.INSERT_TABLE },
            
            // Searches
            { name: 'Search Documents', value: EGoogleDocsOperation.SEARCH_DOCUMENTS },
            { name: 'Search Text', value: EGoogleDocsOperation.SEARCH_TEXT }
        ]
    },
    {
        name: 'document_id',
        displayName: 'Document',
        type: EPropertyType.Options,
        required: true,
        loadOptions: {
            resource: ELoadOptionsResource.GOOGLE_DOCS,
            method: ELoadOptionsMethod.GET_DOCUMENTS,
            dependsOn: ['integration_id']
        }
    },

    {
        name: 'name',
        displayName: 'Name',
        type: EPropertyType.String,
        required: true,
        description: 'Document name/title'
    },
    {
        name: 'content',
        displayName: 'Content',
        type: EPropertyType.String,
        description: 'This parameter supports HTML format'
    },
    {
        name: 'drive_id',
        displayName: 'Choose a Drive',
        type: EPropertyType.Options,
        loadOptions: {
            resource: ELoadOptionsResource.GOOGLE_DRIVE,
            method: ELoadOptionsMethod.GET_DRIVES,
            dependsOn: ['integration_id']
        }
    },
    {
        name: 'folder_id',
        displayName: 'New Document\'s Location',
        type: EPropertyType.String,
        description: 'The folder, where the new document should be placed'
    },
    {
        name: 'use_location_mapping',
        displayName: 'Map',
        type: EPropertyType.Boolean,
        description: 'Use location mapping'
    },
    {
        name: 'text',
        displayName: 'Text',
        type: EPropertyType.String,
        required: true
    },
    {
        name: 'location',
        displayName: 'Insert Location',
        type: EPropertyType.Number,
        minValue: 0,
        description: 'Character index where to insert content'
    },
    {
        name: 'text_style',
        displayName: 'Text Style',
        type: EPropertyType.Collection,
        properties: [
            {
                name: 'bold',
                displayName: 'Bold',
                type: EPropertyType.Boolean
            },
            {
                name: 'italic',
                displayName: 'Italic',
                type: EPropertyType.Boolean
            },
            {
                name: 'underline',
                displayName: 'Underline',
                type: EPropertyType.Boolean
            },
            {
                name: 'font_size',
                displayName: 'Font Size',
                type: EPropertyType.Number,
                minValue: 6,
                maxValue: 400
            },
            {
                name: 'font_family',
                displayName: 'Font Family',
                type: EPropertyType.String
            }
        ]
    },
    {
        name: 'named_style_type',
        displayName: 'Style Type',
        type: EPropertyType.Options,
        options: [
            { name: 'Normal Text', value: ENamedStyleType.NORMAL_TEXT },
            { name: 'Title', value: ENamedStyleType.TITLE },
            { name: 'Subtitle', value: ENamedStyleType.SUBTITLE },
            { name: 'Heading 1', value: ENamedStyleType.HEADING_1 },
            { name: 'Heading 2', value: ENamedStyleType.HEADING_2 },
            { name: 'Heading 3', value: ENamedStyleType.HEADING_3 }
        ]
    }
];

// =================================================================
// SECTION 5: CREDENTIAL DEFINITION
// =================================================================

/**
 * Google Docs credential definition
 */
export const GOOGLE_DOCS_CREDENTIAL: IBaseIntegrationCredential = {
    provider: 'google',
    name: ECredentialName.GOOGLE_OAUTH,
    displayName: 'Google OAuth2',
    description: 'OAuth2 authentication for Google Docs',
    required: true,
    authType: ENodeAuthType.OAUTH2,
    testable: true,
    testUrl: '/api/integrations/test-connection'
};

// =================================================================
// SECTION 6: VALIDATION FUNCTIONS
// =================================================================

/**
 * Validate Google Docs parameters
 */
export function validateGoogleDocsParameters(
    params: Partial<IGoogleDocsParameters>
): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check document_id for operations that need it
    const needsDocumentId = params.operation !== EGoogleDocsOperation.CREATE_DOCUMENT;
    if (needsDocumentId && !(params as any).document_id) {
        errors.push('Document ID is required');
    }

    // Operation specific validation
    if (params.operation === EGoogleDocsOperation.CREATE_DOCUMENT) {
        const createParams = params as ICreateDocumentParameters;
        if (!createParams.name) {
            errors.push('Document name is required');
        }
    }

    if (params.operation === EGoogleDocsOperation.INSERT_TEXT) {
        const insertParams = params as IInsertTextParameters;
        if (!insertParams.text) {
            errors.push('Text is required');
        }
        if (insertParams.location === undefined) {
            errors.push('Insert location is required');
        }
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}

/**
 * Type guard cho Google Docs parameters
 */
export function isGoogleDocsParameters(params: any): params is IGoogleDocsParameters {
    return params && Object.values(EGoogleDocsOperation).includes(params.operation);
}

/**
 * Type-safe node execution cho Google Docs
 */
export type IGoogleDocsNodeExecution = ITypedNodeExecution<
    IGoogleDocsInput,
    IGoogleDocsOutput,
    IGoogleDocsParameters
>;
