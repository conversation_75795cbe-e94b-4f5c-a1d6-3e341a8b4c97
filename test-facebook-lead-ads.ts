/**
 * Test file để kiểm tra Facebook Lead Ads module
 */

import {
    EFacebookLeadAdsOperation,
    EFacebookLeadFormField,
    INewLeadParameters,
    IGetLeadDetailParameters,
    IGetFormParameters,
    IUnsubscribeWebhookParameters,
    IListLeadsParameters,
    validateFacebookLeadAdsParameters,
    isFacebookLeadAdsParameters,
    FACEBOOK_LEAD_ADS_PROPERTIES,
    FACEBOOK_LEAD_ADS_CREDENTIAL
} from './src/modules/workflow/interfaces/integration/facebook-ads/facebook-lead-ads';

// Test enum values
console.log('Facebook Lead Ads Operations:', Object.values(EFacebookLeadAdsOperation));
console.log('Facebook Lead Form Fields:', Object.values(EFacebookLeadFormField));

// Test parameter validation
const testNewLeadParams: INewLeadParameters = {
    operation: EFacebookLeadAdsOperation.NEW_LEAD,
    integration_id: 'test-integration-id',
    webhook_name: 'Test Webhook',
    connection_id: 'test-connection',
    page_id: 'test-page-id'
};

const validation = validateFacebookLeadAdsParameters(testNewLeadParams);
console.log('Validation result:', validation);

// Test type guard
console.log('Is valid Facebook Lead Ads params:', isFacebookLeadAdsParameters(testNewLeadParams));

// Test properties length
console.log('Number of properties:', FACEBOOK_LEAD_ADS_PROPERTIES.length);

// Test credential
console.log('Credential provider:', FACEBOOK_LEAD_ADS_CREDENTIAL.provider);

console.log('✅ Facebook Lead Ads module test completed successfully!');
