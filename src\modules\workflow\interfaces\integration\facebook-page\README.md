# Facebook Page Integration

## 📋 Implementation Status

### ✅ Completed
1. **List Posts Operation** - <PERSON><PERSON> hoàn thành
   - Parameters: `page_id`, `include_hidden`, `limit`
   - Validation: Page ID required, limit 1-500
   - Properties: Dropdown options, field validation

2. **Get a Post Operation** - <PERSON><PERSON> hoàn thành
   - Parameters: `page_id`, `post_id`, `fields`
   - Validation: Page ID & Post ID required, Post ID format validation
   - Properties: String input fields, field validation

3. **Get Post Reactions Operation** - Đ<PERSON> hoàn thành
   - Parameters: `page_id`, `post_id`, `type`, `limit`
   - Validation: Page ID & Post ID required, limit 1-100
   - Properties: Dropdown for reaction types, number input for limit

4. **Create a Post Operation** - <PERSON><PERSON> hoàn thành
   - Parameters: `page_id`, `message`, `link`, `published`, `scheduled_publish_time`, `privacy`
   - Validation: Page ID required, message or link required, ISO date validation
   - Properties: Text inputs, boolean toggle, datetime picker, privacy dropdown

5. **Create a Post with Photos Operation** - <PERSON><PERSON> hoàn thành
   - Parameters: `page_id`, `photos[]`, `message`, `published`, `scheduled_publish_time`, `privacy`
   - Photo Actions: "upload a photo" (file + filename + data), "download photo from URL" (url)
   - Validation: Page ID required, photos array min 1 item, URL/file validation
   - Properties: Collection with conditional fields, action dropdown

6. **Update a Post Operation** - Đã hoàn thành
   - Parameters: `page_id`, `post_id`, `message`
   - Validation: Page ID & Post ID required, Post ID format validation, message optional
   - Properties: String inputs for Post ID and Message

### 🏗️ File Structure
```
/facebook-page/
├── facebook-page.interface.ts     ✅ Main interfaces & parameters
├── facebook-page.properties.ts    ✅ Node properties for UI
├── facebook-page.types.ts         ✅ Enums & types
├── facebook-page.validation.ts    ✅ Validation functions
├── index.ts                       ✅ Export all modules
└── README.md                      ✅ This file
```

### 📊 Operations Overview

#### **POSTS (11 operations)**
- ✅ **List Posts** - Returns posts
- ✅ **Get a Post** - Returns info about a post
- ✅ **Get Post Reactions** - Returns number of reactions for a post
- ✅ **Create a Post** - Creates a post
- ✅ **Create a Post with Photos** - Creates a multi-photo post
- ✅ **Update a Post** - Updates a post
- ⏳ **Watch Posts** - Triggers when a new post is added
- ⏳ **Watch Posts (public page)** - Triggers when a new post is added to selected public page
- ⏳ **Get Post Reactions** - Returns number of reactions for a post
- ⏳ **Create a Post** - Creates a post
- ⏳ **Create a Post with Photos** - Creates a multi-photo post
- ⏳ **Update a Post** - Updates a post
- ⏳ **Delete a Post** - Deletes a post
- ⏳ **Like a Post** - Likes a post
- ⏳ **Unlike a Post** - Unlikes a post

#### **VIDEOS (6 operations)**
- ⏳ **Watch Videos** - Triggers when a new video is added
- ⏳ **List Videos** - Returns videos
- ⏳ **Get a Video** - Returns info about a video
- ⏳ **Upload a Video** - Uploads a video
- ⏳ **Update a Video** - Updates a video
- ⏳ **Delete a Video** - Deletes a video

#### **PHOTOS (5 operations)**
- ⏳ **Watch Photos** - Triggers when a new photo is added
- ⏳ **List Photos** - Returns photos
- ⏳ **Get a Photo** - Returns info about a photo
- ⏳ **Upload a Photo** - Uploads a photo
- ⏳ **Delete a Photo** - Deletes a photo

#### **COMMENTS (6 operations)**
- ⏳ **Watch Comments** - Triggers when a new comment is added
- ⏳ **List Comments** - Returns comments for a post
- ⏳ **Get a Comment** - Returns info about a comment
- ⏳ **Create a Comment** - Creates a comment
- ⏳ **Update a Comment** - Updates a comment
- ⏳ **Delete a Comment** - Deletes a comment

#### **PAGE (2 operations)**
- ⏳ **Get a Page** - Returns details about a page
- ⏳ **Update a Page** - Update details on a page, such as phone or email

#### **OTHER (1 operation)**
- ⏳ **Publish a Reel** - Uploads a reel

### 🎯 Next Steps

1. **Continue with next operation** - Implement "Delete a Post" operation
2. **Add more parameters** - Expand properties for each operation
3. **Add validation** - Complete validation for all operations
4. **Test integration** - Verify with Make.com patterns

### 📝 List Posts Implementation Details

**Parameters from Make.com:**
- `Connection` (required) - Facebook connection
- `Page` (required) - Page selection 
- `Include hidden posts` - Yes/No/Empty (default: Empty)
- `Limit` (required) - Maximum results (default: 2, max: 500)

**Our Implementation:**
```typescript
interface IListPostsParameters extends IActionParameters {
    operation: EFacebookPageOperation.LIST_POSTS;
    page_id: string;                    // Required
    include_hidden?: boolean;           // Optional
    limit: number;                      // Required, 1-500
    fields?: EFacebookPostField[];      // Optional
}
```

**Validation:**
- Page ID is required
- Limit must be between 1 and 500
- Include hidden must be "yes", "no", or "empty"

### 📝 Get a Post Implementation Details

**Parameters from Make.com (Expected):**
- `Connection` (required) - Facebook connection
- `Page` (required) - Page selection
- `Post ID` (required) - ID của bài đăng cần lấy thông tin

**Our Implementation:**
```typescript
interface IGetPostParameters extends IActionParameters {
    operation: EFacebookPageOperation.GET_POST;
    page_id: string;                    // Required
    post_id: string;                    // Required
    fields?: EFacebookPostField[];      // Optional
}
```

**Validation:**
- Page ID is required
- Post ID is required
- Post ID must be in valid format (numeric or page_id_postid)

### 📝 Get Post Reactions Implementation Details

**Parameters from Make.com (Observed):**
- `Connection` (required) - Facebook connection
- `Page` (required) - Page selection
- `Type` - Loại reaction cần lấy

**Our Implementation:**
```typescript
interface IGetPostReactionsParameters extends IActionParameters {
    operation: EFacebookPageOperation.GET_POST_REACTIONS;
    page_id: string;                    // Required
    post_id: string;                    // Required
    type?: EFacebookReactionType;       // Optional
    limit?: number;                     // Optional, 1-100
}
```

**Validation:**
- Page ID is required
- Post ID is required
- Post ID must be in valid format (numeric or page_id_postid)
- Limit must be between 1 and 100 if provided

### 📝 Create a Post Implementation Details

**Parameters from Make.com (Observed):**
- `Connection` (required) - Facebook connection
- `Page` (required) - Page selection
- `Message` - Nội dung bài đăng
- `Link` - Liên kết đính kèm
- `Show advanced settings` - Có thể có thêm options

**Our Implementation:**
```typescript
interface ICreatePostParameters extends IActionParameters {
    operation: EFacebookPageOperation.CREATE_POST;
    page_id: string;                    // Required
    message?: string;                   // Optional
    link?: string;                      // Optional
    published?: boolean;                // Optional, default: true
    scheduled_publish_time?: string;    // Optional, ISO string
    privacy?: EFacebookPostPrivacy;     // Optional
}
```

**Validation:**
- Page ID is required
- Either message or link must be provided
- Scheduled publish time must be valid ISO string if provided
- Published must be false when scheduled publish time is provided

### 📝 Create a Post with Photos Implementation Details

**Parameters from Make.com (Observed):**
- `Connection` (required) - Facebook connection
- `Page` (required) - Page selection
- `Photos` (required) - Array of photos với 2 actions:
  1. **"upload a photo"**: File name + Data (required)
  2. **"download photo from URL"**: URL (required)
- `Caption` (optional) - Caption cho từng ảnh
- `Message` (optional) - Nội dung bài đăng
- `Show advanced settings` - Có thể có thêm options

**Our Implementation:**
```typescript
interface ICreatePostWithPhotosParameters extends IActionParameters {
    operation: EFacebookPageOperation.CREATE_POST_WITH_PHOTOS;
    page_id: string;                    // Required
    photos: IFacebookPhotoItem[];       // Required, min: 1
    message?: string;                   // Optional
    published?: boolean;                // Optional, default: true
    scheduled_publish_time?: string;    // Optional, ISO string
    privacy?: EFacebookPostPrivacy;     // Optional
}

interface IFacebookPhotoItem {
    action: EFacebookPhotoAction;       // Required: 'upload a photo' | 'download photo from URL'
    file?: { filename: string; data: string | Buffer }; // For upload action
    url?: string;                       // For download action
    caption?: string;                   // Optional
}
```

**Validation:**
- Page ID is required
- Photos array must have at least 1 item
- For upload action: file.filename and file.data are required
- For download action: url is required and must be valid URL
- Scheduled publish time must be valid ISO string if provided
- Published must be false when scheduled publish time is provided

### 📝 Update a Post Implementation Details

**Parameters from Make.com (Observed):**
- `Connection` (required) - Facebook connection
- `Page` (required) - Page selection
- `Post ID` (required) - ID của bài đăng cần update
- `Message` (optional) - Nội dung bài đăng mới

**Our Implementation:**
```typescript
interface IUpdatePostParameters extends IActionParameters {
    operation: EFacebookPageOperation.UPDATE_POST;
    page_id: string;                    // Required
    post_id: string;                    // Required
    message?: string;                   // Optional
}
```

**Validation:**
- Page ID is required
- Post ID is required
- Post ID must be in valid format (numeric or page_id_postid)
- Message is optional but cannot be empty if provided

### 🔄 Pattern Consistency

Following Facebook Ads integration patterns:
- ✅ Separate files for types, properties, validation
- ✅ Enum-based operations
- ✅ Type-safe interfaces
- ✅ Comprehensive validation
- ✅ Make.com compatible structure
