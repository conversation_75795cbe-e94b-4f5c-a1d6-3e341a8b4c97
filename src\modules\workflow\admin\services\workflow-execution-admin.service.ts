import { Inject, Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { WorkflowRepository } from '../../repositories';
import { NodeRepository } from '../../repositories/node.repository';
import { WORKFLOW_ADMIN_ERROR_CODES } from '../../exceptions/workflow.exception';
import { ClientProxy } from '@nestjs/microservices';
import { firstValueFrom } from 'rxjs';

/**
 * Service xử lý thực thi workflow cho Admin
 */
@Injectable()
export class WorkflowExecutionAdminService {
  private readonly logger = new Logger(WorkflowExecutionAdminService.name);

  constructor(
    private readonly workflowRepository: WorkflowRepository,
    private readonly nodeRepository: NodeRepository,
    @Inject('REDIS_CLIENT') private readonly client: ClientProxy,
  ) {}

  /**
   * Thực thi workflow (Admin)
   * @param employeeId ID của employee
   * @param workflowId ID của workflow cần thực thi
   * @returns Kết quả thực thi workflow
   */
  async executeWorkflow(employeeId: number, workflowId: string): Promise<any> {
    this.logger.log(`Admin ${employeeId} thực thi workflow: ${workflowId}`);

    try {
      // Admin có thể thực thi bất kỳ workflow nào, nhưng vẫn cần kiểm tra workflow có tồn tại không
      const workflow = await this.workflowRepository.findWorkflowById(workflowId);
      if (!workflow) {
        throw new AppException(WORKFLOW_ADMIN_ERROR_CODES.WORKFLOW_NOT_FOUND);
      }

      // TODO: Implement workflow execution logic
      // Placeholder implementation - sẽ được implement sau
      const executionId = `admin_exec_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // Tạo payload để gửi đến microservice
      const payload = {
        workflowId,
        employeeId,
        executionId,
        executedBy: 'admin',
        timestamp: new Date().toISOString()
      };

      // Gửi message đến Redis để xử lý workflow (comment out để tránh lỗi khi chưa có microservice)
      // const response = await firstValueFrom(this.client.send({ cmd: 'admin_process_workflow' }, payload));
      
      this.logger.log(`Admin ${employeeId} khởi chạy workflow ${workflowId} với execution ID: ${executionId}`);

      return {
        executionId,
        workflowId,
        status: 'running',
        startedAt: new Date().toISOString(),
        executedBy: 'admin',
        adminId: employeeId,
        message: 'Workflow đã được khởi chạy thành công bởi admin'
      };

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      
      this.logger.error(`Lỗi khi admin ${employeeId} thực thi workflow ${workflowId}:`, error);
      throw new AppException(WORKFLOW_ADMIN_ERROR_CODES.WORKFLOW_EXECUTION_ERROR);
    }
  }

  /**
   * Thực thi node cụ thể trong workflow (Admin)
   * @param employeeId ID của employee
   * @param workflowId ID của workflow
   * @param nodeId ID của node cần thực thi
   * @returns Kết quả thực thi node
   */
  async executeWorkflowNode(employeeId: number, workflowId: string, nodeId: string): Promise<any> {
    this.logger.log(`Admin ${employeeId} thực thi node ${nodeId} trong workflow: ${workflowId}`);

    try {
      // Admin có thể thực thi bất kỳ workflow nào, nhưng vẫn cần kiểm tra workflow có tồn tại không
      const workflow = await this.workflowRepository.findWorkflowById(workflowId);
      if (!workflow) {
        throw new AppException(
          WORKFLOW_ADMIN_ERROR_CODES.WORKFLOW_NOT_FOUND,
          'Không tìm thấy workflow'
        );
      }

      // Kiểm tra node có tồn tại trong workflow không
      const node = await this.nodeRepository.findByIdAndWorkflow(nodeId, workflowId);
      if (!node) {
        throw new AppException(WORKFLOW_ADMIN_ERROR_CODES.NODE_NOT_FOUND);
      }

      // TODO: Implement node execution logic
      // Placeholder implementation - sẽ được implement sau
      const executionId = `admin_exec_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // Tạo payload để gửi đến microservice
      const payload = {
        workflowId,
        nodeId,
        employeeId,
        executionId,
        executedBy: 'admin',
        timestamp: new Date().toISOString()
      };

      // Gửi message đến Redis để xử lý node (comment out để tránh lỗi khi chưa có microservice)
      // const response = await firstValueFrom(this.client.send({ cmd: 'admin_process_node' }, payload));
      
      this.logger.log(`Admin ${employeeId} khởi chạy node ${nodeId} trong workflow ${workflowId} với execution ID: ${executionId}`);

      return {
        executionId,
        workflowId,
        nodeId,
        nodeName: node.name,
        status: 'running',
        startedAt: new Date().toISOString(),
        executedBy: 'admin',
        adminId: employeeId,
        message: 'Node đã được khởi chạy thành công bởi admin'
      };

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      
      this.logger.error(`Lỗi khi admin ${employeeId} thực thi node ${nodeId} trong workflow ${workflowId}:`, error);
      throw new AppException(WORKFLOW_ADMIN_ERROR_CODES.WORKFLOW_EXECUTION_ERROR);
    }
  }
}
