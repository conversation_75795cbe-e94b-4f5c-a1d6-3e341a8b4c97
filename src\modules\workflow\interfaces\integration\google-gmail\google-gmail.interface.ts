/**
 * @file Google Gmail Integration Node Interface
 * 
 * Đ<PERSON><PERSON> nghĩa type-safe interface cho Google Gmail node operations
 * Theo patterns từ Make.com và n8n industry standards
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

import {
    IBaseIntegrationParameters,
    ITriggerParameters,
    IActionParameters,
    ISearchParameters,
    IBaseIntegrationInput,
    IBaseIntegrationOutput,
    EIntegrationOperationType,
    EIntegrationErrorHandling,
    IBaseIntegrationCredential
} from '../base/base-integration.interface';

import {
    ECredentialName,
    ENodeAuthType,
    EPropertyType,
    ELoadOptionsResource,
    ELoadOptionsMethod,
    INodeProperty
} from '../../node-manager.interface';

import {
    ITypedNodeExecution
} from '../../execute.interface';

// =================================================================
// SECTION 1: GOOGLE GMAIL ENUMS
// =================================================================

/**
 * Google Gmail specific operations
 */
export enum EGoogleGmailOperation {
    // === TRIGGERS ===
    /** Watch for new emails */
    WATCH_EMAILS = 'watchEmails',
    /** Watch for new emails in label */
    WATCH_LABEL = 'watchLabel',

    // === EMAIL ACTIONS ===
    /** Send email */
    SEND_EMAIL = 'sendEmail',
    /** Reply to email */
    REPLY_EMAIL = 'replyEmail',
    /** Forward email */
    FORWARD_EMAIL = 'forwardEmail',
    /** Get email */
    GET_EMAIL = 'getEmail',
    /** Delete email */
    DELETE_EMAIL = 'deleteEmail',

    // === LABEL ACTIONS ===
    /** Add label to email */
    ADD_LABEL = 'addLabel',
    /** Remove label from email */
    REMOVE_LABEL = 'removeLabel',
    /** Create label */
    CREATE_LABEL = 'createLabel',
    /** Delete label */
    DELETE_LABEL = 'deleteLabel',

    // === ATTACHMENT ACTIONS ===
    /** Download attachment */
    DOWNLOAD_ATTACHMENT = 'downloadAttachment',
    /** Get attachment info */
    GET_ATTACHMENT = 'getAttachment',

    // === SEARCHES ===
    /** Search emails */
    SEARCH_EMAILS = 'searchEmails',
    /** List emails */
    LIST_EMAILS = 'listEmails',
    /** List labels */
    LIST_LABELS = 'listLabels'
}

/**
 * Gmail email format
 */
export enum EEmailFormat {
    /** Full email format */
    FULL = 'full',
    /** Metadata only */
    METADATA = 'metadata',
    /** Headers only */
    MINIMAL = 'minimal',
    /** Raw email */
    RAW = 'raw'
}

/**
 * Gmail label types
 */
export enum ELabelType {
    /** System label */
    SYSTEM = 'system',
    /** User label */
    USER = 'user'
}

/**
 * Gmail message visibility
 */
export enum EMessageVisibility {
    /** Show message */
    SHOW = 'show',
    /** Hide message */
    HIDE = 'hide'
}

// =================================================================
// SECTION 2: GOOGLE GMAIL PARAMETERS
// =================================================================

/**
 * Watch Emails trigger parameters
 */
export interface IWatchEmailsParameters extends ITriggerParameters {
    operation: EGoogleGmailOperation.WATCH_EMAILS;

    /** Label IDs to watch */
    label_ids?: string[];

    /** Include spam and trash */
    include_spam_trash?: boolean;

    /** Maximum emails per poll */
    limit?: number;

    /** Query filter */
    query?: string;
}

/**
 * Send Email parameters
 */
export interface ISendEmailParameters extends IActionParameters {
    operation: EGoogleGmailOperation.SEND_EMAIL;

    /** Recipient email addresses */
    to: string[];

    /** CC email addresses */
    cc?: string[];

    /** BCC email addresses */
    bcc?: string[];

    /** Email subject */
    subject: string;

    /** Email body (HTML) */
    body_html?: string;

    /** Email body (plain text) */
    body_text?: string;

    /** Attachments */
    attachments?: Array<{
        filename: string;
        content: string; // base64 encoded
        content_type: string;
    }>;

    /** Reply to email address */
    reply_to?: string;

    /** Send as (for delegated accounts) */
    send_as?: string;

    /** Thread ID (for threading) */
    thread_id?: string;
}

/**
 * Reply Email parameters
 */
export interface IReplyEmailParameters extends IActionParameters {
    operation: EGoogleGmailOperation.REPLY_EMAIL;

    /** Original message ID */
    message_id: string;

    /** Reply body (HTML) */
    body_html?: string;

    /** Reply body (plain text) */
    body_text?: string;

    /** Reply to all recipients */
    reply_all?: boolean;

    /** Additional attachments */
    attachments?: Array<{
        filename: string;
        content: string;
        content_type: string;
    }>;
}

/**
 * Get Email parameters
 */
export interface IGetEmailParameters extends IActionParameters {
    operation: EGoogleGmailOperation.GET_EMAIL;

    /** Message ID */
    message_id: string;

    /** Email format */
    format?: EEmailFormat;

    /** Include attachments */
    include_attachments?: boolean;

    /** Metadata headers to include */
    metadata_headers?: string[];
}

/**
 * Add Label parameters
 */
export interface IAddLabelParameters extends IActionParameters {
    operation: EGoogleGmailOperation.ADD_LABEL;

    /** Message ID */
    message_id: string;

    /** Label IDs to add */
    label_ids: string[];
}

/**
 * Create Label parameters
 */
export interface ICreateLabelParameters extends IActionParameters {
    operation: EGoogleGmailOperation.CREATE_LABEL;

    /** Label name */
    name: string;

    /** Message list visibility */
    message_list_visibility?: EMessageVisibility;

    /** Label list visibility */
    label_list_visibility?: EMessageVisibility;

    /** Label color */
    color?: {
        text_color?: string;
        background_color?: string;
    };
}

/**
 * Search Emails parameters
 */
export interface ISearchEmailsParameters extends ISearchParameters {
    operation: EGoogleGmailOperation.SEARCH_EMAILS;

    /** Search query (Gmail search syntax) */
    query: string;

    /** Label IDs to search in */
    label_ids?: string[];

    /** Include spam and trash */
    include_spam_trash?: boolean;

    /** Email format */
    format?: EEmailFormat;

    /** Maximum results */
    max_results?: number;

    /** Page token for pagination */
    page_token?: string;
}

/**
 * Union type cho tất cả Google Gmail parameters
 */
export type IGoogleGmailParameters = 
    | IWatchEmailsParameters
    | ISendEmailParameters
    | IReplyEmailParameters
    | IGetEmailParameters
    | IAddLabelParameters
    | ICreateLabelParameters
    | ISearchEmailsParameters;

// =================================================================
// SECTION 3: INPUT/OUTPUT INTERFACES
// =================================================================

/**
 * Google Gmail input interface
 */
export interface IGoogleGmailInput extends IBaseIntegrationInput {
    /** Email data */
    email?: {
        to?: string[];
        cc?: string[];
        bcc?: string[];
        subject?: string;
        body_html?: string;
        body_text?: string;
        attachments?: any[];
    };

    /** Message data */
    message?: {
        id?: string;
        thread_id?: string;
        label_ids?: string[];
    };

    /** Label data */
    label?: {
        id?: string;
        name?: string;
        type?: string;
    };
}

/**
 * Google Gmail output interface
 */
export interface IGoogleGmailOutput extends IBaseIntegrationOutput {
    /** Google Gmail specific data */
    google_gmail?: {
        message_id?: string;
        thread_id?: string;
        label_id?: string;
        snippet?: string;
        size_estimate?: number;
        history_id?: string;
        internal_date?: string;
        payload?: {
            headers?: Array<{
                name: string;
                value: string;
            }>;
            body?: {
                size: number;
                data?: string;
            };
            parts?: any[];
        };
        attachments?: Array<{
            attachment_id: string;
            filename: string;
            size: number;
            content_type: string;
        }>;
    };
}

// =================================================================
// SECTION 4: NODE PROPERTIES DEFINITION
// =================================================================

/**
 * Google Gmail node properties
 */
export const GOOGLE_GMAIL_PROPERTIES: INodeProperty[] = [
    {
        name: 'operation',
        displayName: 'Operation',
        type: EPropertyType.Options,
        required: true,
        options: [
            // Triggers
            { name: 'Watch Emails', value: EGoogleGmailOperation.WATCH_EMAILS },
            { name: 'Watch Label', value: EGoogleGmailOperation.WATCH_LABEL },
            
            // Email Actions
            { name: 'Send Email', value: EGoogleGmailOperation.SEND_EMAIL },
            { name: 'Reply Email', value: EGoogleGmailOperation.REPLY_EMAIL },
            { name: 'Get Email', value: EGoogleGmailOperation.GET_EMAIL },
            { name: 'Delete Email', value: EGoogleGmailOperation.DELETE_EMAIL },
            
            // Label Actions
            { name: 'Add Label', value: EGoogleGmailOperation.ADD_LABEL },
            { name: 'Remove Label', value: EGoogleGmailOperation.REMOVE_LABEL },
            { name: 'Create Label', value: EGoogleGmailOperation.CREATE_LABEL },
            
            // Searches
            { name: 'Search Emails', value: EGoogleGmailOperation.SEARCH_EMAILS },
            { name: 'List Emails', value: EGoogleGmailOperation.LIST_EMAILS },
            { name: 'List Labels', value: EGoogleGmailOperation.LIST_LABELS }
        ]
    },
    {
        name: 'to',
        displayName: 'To',
        type: EPropertyType.Array,
        required: true,
        properties: [
            {
                name: 'email',
                displayName: 'Email Address',
                type: EPropertyType.String
            }
        ]
    },
    {
        name: 'subject',
        displayName: 'Subject',
        type: EPropertyType.String,
        required: true
    },
    {
        name: 'body_html',
        displayName: 'Body (HTML)',
        type: EPropertyType.String,
        description: 'HTML content of the email'
    },
    {
        name: 'body_text',
        displayName: 'Body (Text)',
        type: EPropertyType.String,
        description: 'Plain text content of the email'
    },
    {
        name: 'attachments',
        displayName: 'Attachments',
        type: EPropertyType.Array,
        properties: [
            {
                name: 'filename',
                displayName: 'Filename',
                type: EPropertyType.String
            },
            {
                name: 'content',
                displayName: 'Content (Base64)',
                type: EPropertyType.String
            },
            {
                name: 'content_type',
                displayName: 'Content Type',
                type: EPropertyType.String
            }
        ]
    },
    {
        name: 'label_ids',
        displayName: 'Labels',
        type: EPropertyType.Array,
        properties: [
            {
                name: 'label_id',
                displayName: 'Label ID',
                type: EPropertyType.String
            }
        ],
        loadOptions: {
            resource: ELoadOptionsResource.GOOGLE_GMAIL,
            method: ELoadOptionsMethod.GET_LABELS,
            dependsOn: ['integration_id']
        }
    },
    {
        name: 'query',
        displayName: 'Search Query',
        type: EPropertyType.String,
        placeholder: 'from:<EMAIL> subject:important',
        description: 'Gmail search query syntax'
    },
    {
        name: 'format',
        displayName: 'Email Format',
        type: EPropertyType.Options,
        options: [
            { name: 'Full', value: EEmailFormat.FULL },
            { name: 'Metadata', value: EEmailFormat.METADATA },
            { name: 'Minimal', value: EEmailFormat.MINIMAL },
            { name: 'Raw', value: EEmailFormat.RAW }
        ]
    }
];

// =================================================================
// SECTION 5: CREDENTIAL DEFINITION
// =================================================================

/**
 * Google Gmail credential definition
 */
export const GOOGLE_GMAIL_CREDENTIAL: IBaseIntegrationCredential = {
    provider: 'google',
    name: ECredentialName.GOOGLE_OAUTH,
    displayName: 'Google OAuth2',
    description: 'OAuth2 authentication for Gmail',
    required: true,
    authType: ENodeAuthType.OAUTH2,
    testable: true,
    testUrl: '/api/integrations/test-connection'
};

// =================================================================
// SECTION 6: VALIDATION FUNCTIONS
// =================================================================

/**
 * Validate Google Gmail parameters
 */
export function validateGoogleGmailParameters(
    params: Partial<IGoogleGmailParameters>
): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Operation specific validation
    if (params.operation === EGoogleGmailOperation.SEND_EMAIL) {
        const sendParams = params as ISendEmailParameters;
        if (!sendParams.to || sendParams.to.length === 0) {
            errors.push('At least one recipient is required');
        }
        if (!sendParams.subject) {
            errors.push('Subject is required');
        }
        if (!sendParams.body_html && !sendParams.body_text) {
            errors.push('Email body (HTML or text) is required');
        }
    }

    if (params.operation === EGoogleGmailOperation.GET_EMAIL || 
        params.operation === EGoogleGmailOperation.REPLY_EMAIL) {
        if (!(params as IGetEmailParameters).message_id) {
            errors.push('Message ID is required');
        }
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}

/**
 * Type guard cho Google Gmail parameters
 */
export function isGoogleGmailParameters(params: any): params is IGoogleGmailParameters {
    return params && Object.values(EGoogleGmailOperation).includes(params.operation);
}

/**
 * Type-safe node execution cho Google Gmail
 */
export type IGoogleGmailNodeExecution = ITypedNodeExecution<
    IGoogleGmailInput,
    IGoogleGmailOutput,
    IGoogleGmailParameters
>;
