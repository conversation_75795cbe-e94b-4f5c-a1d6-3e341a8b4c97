/**
 * Interface cho backup tokens của Zalo Personal trong trường hợp encryption bị lỗi
 */
export interface ZaloPersonalBackupTokens {
  /**
   * Access token backup
   */
  accessToken: string;

  /**
   * Refresh token backup
   */
  refreshToken?: string;
}

/**
 * Interface cho metadata của Zalo Personal trong Integration entity
 */
export interface ZaloPersonalMetadata {
  /**
   * ID của tài khoản Zalo cá nhân (zalo_uid)
   */
  zalo_uid: string;

  /**
   * Tên hiển thị của tài khoản Zalo
   */
  displayName: string;

  /**
   * Tên người dùng (username)
   */
  username?: string;

  /**
   * URL avatar của tài khoản
   */
  avatarUrl?: string;

  /**
   * Số điện thoại (nếu có)
   */
  phoneNumber?: string;

  /**
   * Email (nếu có)
   */
  email?: string;

  /**
   * Thời gian hết hạn của access token (Unix timestamp)
   * Access Token có hiệu lực theo thời gian đư<PERSON> cấp
   */
  expiresAt: number;

  /**
   * Thời gian hết hạn của refresh token (Unix timestamp)
   * Refresh Token có hiệu lực dài hạn
   */
  refreshTokenExpiresAt?: number;

  /**
   * Trạng thái kết nối (active, inactive, NEEDS_REAUTH, expired)
   */
  status: string;

  /**
   * Thời điểm tạo (Unix timestamp)
   */
  createdAt: number;

  /**
   * Thời điểm cập nhật (Unix timestamp)
   */
  updatedAt: number;

  /**
   * Backup tokens trong trường hợp encryption bị lỗi
   */
  backupTokens?: ZaloPersonalBackupTokens;

  /**
   * Lý do lỗi nếu có
   */
  errorReason?: string;

  /**
   * Thời điểm xảy ra lỗi
   */
  corruptedAt?: string;

  /**
   * Thông tin trình duyệt ảo được sử dụng để đăng nhập
   */
  browserInfo?: {
    /**
     * User agent của trình duyệt
     */
    userAgent?: string;

    /**
     * Cookies session
     */
    sessionCookies?: string;

    /**
     * Thời gian đăng nhập cuối cùng
     */
    lastLoginAt?: number;

    /**
     * IP address được sử dụng
     */
    ipAddress?: string;
  };

  /**
   * Cấu hình bổ sung cho Zalo Personal
   */
  settings?: {
    /**
     * Có tự động gửi tin nhắn không
     */
    autoSendMessage?: boolean;

    /**
     * Giới hạn số tin nhắn mỗi ngày
     */
    dailyMessageLimit?: number;

    /**
     * Delay giữa các tin nhắn (ms)
     */
    messageDelay?: number;

    /**
     * Có sử dụng proxy không
     */
    useProxy?: boolean;

    /**
     * Thông tin proxy
     */
    proxyConfig?: {
      host: string;
      port: number;
      username?: string;
      password?: string;
    };
  };
}
