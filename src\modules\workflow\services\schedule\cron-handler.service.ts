/**
 * @file CRON Handler Service
 * 
 * Service xử lý CRON expressions cho delayed jobs
 * Convert CRON patterns thành next execution times
 * 
 */

import { Injectable, Logger } from '@nestjs/common';
import { DateTime } from 'luxon';
const cronParser = require('cron-parser');

/**
 * Interface cho CRON configuration
 */
export interface ICronConfig {
    expression: string;
    timezone?: string;
    startDate?: string;
    endDate?: string;
}

/**
 * Interface cho CRON execution result
 */
export interface ICronExecutionInfo {
    nextExecutionTime: Date;
    delay: number;
    isValid: boolean;
    cronExpression: string;
    timezone: string;
    executionCount?: number;
}

@Injectable()
export class CronHandlerService {
    private readonly logger = new Logger(CronHandlerService.name);
    
    /**
     * Calculate next execution time từ CRON expression
     */
    async calculateNextCronExecution(
        cronConfig: ICronConfig,
        currentTime?: Date
    ): Promise<ICronExecutionInfo | null> {
        try {
            const { expression, timezone = 'Asia/Ho_Chi_Minh' } = cronConfig;
            
            // Validate CRON expression
            if (!this.isValidCronExpression(expression)) {
                this.logger.error(`Invalid CRON expression: ${expression}`);
                return null;
            }
            
            const now = currentTime || new Date();
            
            // Parse CRON expression với timezone
            const interval = cronParser.parseExpression(expression, {
                currentDate: now,
                tz: timezone,
                startDate: cronConfig.startDate ? new Date(cronConfig.startDate) : undefined,
                endDate: cronConfig.endDate ? new Date(cronConfig.endDate) : undefined
            });
            
            // Get next execution time
            const nextExecution = interval.next().toDate();
            const delay = nextExecution.getTime() - now.getTime();
            
            // Validate future time
            if (delay <= 0) {
                this.logger.warn(`CRON next execution is in the past: ${nextExecution}`);
                return null;
            }
            
            return {
                nextExecutionTime: nextExecution,
                delay,
                isValid: true,
                cronExpression: expression,
                timezone,
                executionCount: 0
            };
            
        } catch (error) {
            this.logger.error(`Failed to calculate CRON execution: ${cronConfig.expression}`, error);
            return null;
        }
    }
    
    /**
     * Calculate multiple next executions (for preview)
     */
    async getNextCronExecutions(
        cronConfig: ICronConfig,
        count: number = 5,
        currentTime?: Date
    ): Promise<Date[]> {
        try {
            const { expression, timezone = 'Asia/Ho_Chi_Minh' } = cronConfig;
            
            if (!this.isValidCronExpression(expression)) {
                return [];
            }
            
            const now = currentTime || new Date();
            const executions: Date[] = [];
            
            const interval = cronParser.parseExpression(expression, {
                currentDate: now,
                tz: timezone
            });
            
            for (let i = 0; i < count; i++) {
                const nextExecution = interval.next().toDate();
                executions.push(nextExecution);
            }
            
            return executions;
            
        } catch (error) {
            this.logger.error(`Failed to get CRON executions: ${cronConfig.expression}`, error);
            return [];
        }
    }
    
    /**
     * Validate CRON expression
     */
    isValidCronExpression(expression: string): boolean {
        try {
            // Support both 5-field và 6-field CRON expressions
            cronParser.parseExpression(expression);
            return true;
        } catch (error) {
            return false;
        }
    }
    
    /**
     * Get CRON expression description
     */
    getCronDescription(expression: string): string {
        try {
            // Basic description logic
            const parts = expression.split(' ');
            
            if (parts.length < 5) {
                return 'Invalid CRON expression';
            }
            
            const [minute, hour, dayOfMonth, month, dayOfWeek] = parts;
            
            // Common patterns
            if (expression === '0 0 * * *') return 'Daily at midnight';
            if (expression === '0 9 * * *') return 'Daily at 9:00 AM';
            if (expression === '0 0 * * 0') return 'Weekly on Sunday at midnight';
            if (expression === '0 0 1 * *') return 'Monthly on the 1st at midnight';
            if (expression === '*/5 * * * *') return 'Every 5 minutes';
            if (expression === '0 */2 * * *') return 'Every 2 hours';
            
            // Generic description
            let desc = 'Runs ';
            
            if (minute === '*') desc += 'every minute';
            else if (minute.startsWith('*/')) desc += `every ${minute.slice(2)} minutes`;
            else desc += `at minute ${minute}`;
            
            if (hour !== '*') {
                if (hour.startsWith('*/')) desc += ` every ${hour.slice(2)} hours`;
                else desc += ` at hour ${hour}`;
            }
            
            if (dayOfMonth !== '*') {
                desc += ` on day ${dayOfMonth} of month`;
            }
            
            if (dayOfWeek !== '*') {
                const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
                if (!isNaN(Number(dayOfWeek))) {
                    desc += ` on ${days[Number(dayOfWeek)]}`;
                }
            }
            
            return desc;
            
        } catch (error) {
            return 'Invalid CRON expression';
        }
    }
    
    /**
     * Convert common schedule patterns to CRON
     */
    convertToCronExpression(scheduleType: string, config: any): string | null {
        try {
            switch (scheduleType) {
                case 'daily':
                    const { hour = 0, minute = 0, second = 0 } = config;
                    return `${second} ${minute} ${hour} * * *`;
                    
                case 'weekly':
                    const { days_of_week = [0], hour: wHour = 0, minute: wMinute = 0 } = config;
                    const daysList = days_of_week.join(',');
                    return `0 ${wMinute} ${wHour} * * ${daysList}`;
                    
                case 'monthly':
                    const { day_of_month = 1, hour: mHour = 0, minute: mMinute = 0 } = config;
                    return `0 ${mMinute} ${mHour} ${day_of_month} * *`;
                    
                case 'interval':
                    const { value, type } = config;
                    switch (type) {
                        case 'minutes':
                            return `*/${value} * * * *`;
                        case 'hours':
                            return `0 */${value} * * *`;
                        default:
                            return null;
                    }
                    
                default:
                    return null;
            }
        } catch (error) {
            this.logger.error(`Failed to convert to CRON: ${scheduleType}`, error);
            return null;
        }
    }
    
    /**
     * Check if CRON should run now (for immediate execution)
     */
    shouldRunNow(cronExpression: string, timezone: string = 'Asia/Ho_Chi_Minh'): boolean {
        try {
            const now = DateTime.now().setZone(timezone);
            const interval = cronParser.parseExpression(cronExpression, {
                currentDate: now.minus({ minutes: 1 }).toJSDate(),
                tz: timezone
            });
            
            const nextExecution = DateTime.fromJSDate(interval.next().toDate());
            const timeDiff = Math.abs(now.diff(nextExecution, 'seconds').seconds);
            
            // Consider "now" if within 30 seconds
            return timeDiff <= 30;
            
        } catch (error) {
            return false;
        }
    }
    
    /**
     * Get time until next CRON execution
     */
    async getTimeUntilNextExecution(
        cronExpression: string,
        timezone: string = 'Asia/Ho_Chi_Minh'
    ): Promise<number | null> {
        try {
            const cronInfo = await this.calculateNextCronExecution({
                expression: cronExpression,
                timezone
            });

            return cronInfo?.delay || null;
            
        } catch (error) {
            this.logger.error(`Failed to get time until next execution: ${cronExpression}`, error);
            return null;
        }
    }
    
    /**
     * Validate CRON configuration
     */
    validateCronConfig(cronConfig: ICronConfig): { isValid: boolean; errors: string[] } {
        const errors: string[] = [];
        
        // Check expression
        if (!cronConfig.expression) {
            errors.push('CRON expression is required');
        } else if (!this.isValidCronExpression(cronConfig.expression)) {
            errors.push('Invalid CRON expression format');
        }
        
        // Check timezone
        if (cronConfig.timezone) {
            try {
                DateTime.now().setZone(cronConfig.timezone);
            } catch (error) {
                errors.push('Invalid timezone');
            }
        }
        
        // Check date range
        if (cronConfig.startDate && cronConfig.endDate) {
            const start = new Date(cronConfig.startDate);
            const end = new Date(cronConfig.endDate);
            
            if (start >= end) {
                errors.push('Start date must be before end date');
            }
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }
    
    /**
     * Get CRON execution statistics
     */
    async getCronStats(
        cronExpression: string,
        timezone: string = 'Asia/Ho_Chi_Minh',
        days: number = 7
    ): Promise<{
        totalExecutions: number;
        averageInterval: number;
        nextExecutions: Date[];
        description: string;
    }> {
        try {
            const nextExecutions = await this.getNextCronExecutions({
                expression: cronExpression,
                timezone
            }, 10);
            
            // Calculate average interval
            let totalInterval = 0;
            for (let i = 1; i < nextExecutions.length; i++) {
                totalInterval += nextExecutions[i].getTime() - nextExecutions[i - 1].getTime();
            }
            const averageInterval = totalInterval / (nextExecutions.length - 1);
            
            // Estimate executions in time period
            const millisecondsInPeriod = days * 24 * 60 * 60 * 1000;
            const totalExecutions = Math.floor(millisecondsInPeriod / averageInterval);
            
            return {
                totalExecutions,
                averageInterval,
                nextExecutions: nextExecutions.slice(0, 5),
                description: this.getCronDescription(cronExpression)
            };
            
        } catch (error) {
            this.logger.error(`Failed to get CRON stats: ${cronExpression}`, error);
            return {
                totalExecutions: 0,
                averageInterval: 0,
                nextExecutions: [],
                description: 'Invalid CRON expression'
            };
        }
    }
}
