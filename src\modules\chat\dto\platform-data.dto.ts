import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsOptional,
  IsNumber,
  IsString,
  IsBoolean,
  ValidateNested,
  IsObject,
} from 'class-validator';
import { Type } from 'class-transformer';

/**
 * Browser Information DTO
 * Captures client-side browser and environment data
 */
export class BrowserInfoDto {
  @ApiPropertyOptional({
    description: 'User agent string',
    example: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  })
  @IsOptional()
  @IsString()
  userAgent?: string;

  @ApiPropertyOptional({
    description: 'Platform information',
    example: 'Win32',
  })
  @IsOptional()
  @IsString()
  platform?: string;

  @ApiPropertyOptional({
    description: 'Browser language',
    example: 'en-US',
  })
  @IsOptional()
  @IsString()
  language?: string;

  @ApiPropertyOptional({
    description: 'Preferred languages',
    type: [String],
    example: ['en-US', 'en', 'vi'],
  })
  @IsOptional()
  languages?: string[];

  @ApiPropertyOptional({
    description: 'Whether cookies are enabled',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  cookieEnabled?: boolean;

  @ApiPropertyOptional({
    description: 'Whether browser is online',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  onLine?: boolean;

  @ApiPropertyOptional({
    description: 'Number of logical processors',
    example: 8,
  })
  @IsOptional()
  @IsNumber()
  hardwareConcurrency?: number;

  @ApiPropertyOptional({
    description: 'Maximum number of simultaneous touch points',
    example: 0,
  })
  @IsOptional()
  @IsNumber()
  maxTouchPoints?: number;

  @ApiPropertyOptional({
    description: 'Browser vendor',
    example: 'Google Inc.',
  })
  @IsOptional()
  @IsString()
  vendor?: string;

  @ApiPropertyOptional({
    description: 'Browser vendor sub-version',
    example: '',
  })
  @IsOptional()
  @IsString()
  vendorSub?: string;

  @ApiPropertyOptional({
    description: 'Browser product name',
    example: 'Gecko',
  })
  @IsOptional()
  @IsString()
  product?: string;

  @ApiPropertyOptional({
    description: 'Browser product sub-version',
    example: '20030107',
  })
  @IsOptional()
  @IsString()
  productSub?: string;
}

/**
 * Screen Information DTO
 * Captures screen and display properties
 */
export class ScreenInfoDto {
  @ApiPropertyOptional({
    description: 'Screen width in pixels',
    example: 1920,
  })
  @IsOptional()
  @IsNumber()
  width?: number;

  @ApiPropertyOptional({
    description: 'Screen height in pixels',
    example: 1080,
  })
  @IsOptional()
  @IsNumber()
  height?: number;

  @ApiPropertyOptional({
    description: 'Available screen width',
    example: 1920,
  })
  @IsOptional()
  @IsNumber()
  availWidth?: number;

  @ApiPropertyOptional({
    description: 'Available screen height',
    example: 1040,
  })
  @IsOptional()
  @IsNumber()
  availHeight?: number;

  @ApiPropertyOptional({
    description: 'Screen color depth',
    example: 24,
  })
  @IsOptional()
  @IsNumber()
  colorDepth?: number;

  @ApiPropertyOptional({
    description: 'Screen pixel depth',
    example: 24,
  })
  @IsOptional()
  @IsNumber()
  pixelDepth?: number;

  @ApiPropertyOptional({
    description: 'Screen orientation',
    example: 'landscape-primary',
  })
  @IsOptional()
  @IsString()
  orientation?: string;
}

/**
 * Window Information DTO
 * Captures browser window properties
 */
export class WindowInfoDto {
  @ApiPropertyOptional({
    description: 'Inner window width',
    example: 1920,
  })
  @IsOptional()
  @IsNumber()
  innerWidth?: number;

  @ApiPropertyOptional({
    description: 'Inner window height',
    example: 969,
  })
  @IsOptional()
  @IsNumber()
  innerHeight?: number;

  @ApiPropertyOptional({
    description: 'Outer window width',
    example: 1936,
  })
  @IsOptional()
  @IsNumber()
  outerWidth?: number;

  @ApiPropertyOptional({
    description: 'Outer window height',
    example: 1056,
  })
  @IsOptional()
  @IsNumber()
  outerHeight?: number;

  @ApiPropertyOptional({
    description: 'Device pixel ratio',
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  devicePixelRatio?: number;

  @ApiPropertyOptional({
    description: 'Horizontal scroll position',
    example: 0,
  })
  @IsOptional()
  @IsNumber()
  scrollX?: number;

  @ApiPropertyOptional({
    description: 'Vertical scroll position',
    example: 0,
  })
  @IsOptional()
  @IsNumber()
  scrollY?: number;
}

/**
 * Page Information DTO
 * Captures current page context
 */
export class PageInfoDto {
  @ApiPropertyOptional({
    description: 'Current page URL',
    example: 'https://example.com/contact',
  })
  @IsOptional()
  @IsString()
  url?: string;

  @ApiPropertyOptional({
    description: 'URL pathname',
    example: '/contact',
  })
  @IsOptional()
  @IsString()
  pathname?: string;

  @ApiPropertyOptional({
    description: 'URL search parameters',
    example: '?ref=homepage',
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    description: 'URL hash fragment',
    example: '#contact-form',
  })
  @IsOptional()
  @IsString()
  hash?: string;

  @ApiPropertyOptional({
    description: 'Host including port',
    example: 'example.com:443',
  })
  @IsOptional()
  @IsString()
  host?: string;

  @ApiPropertyOptional({
    description: 'Hostname without port',
    example: 'example.com',
  })
  @IsOptional()
  @IsString()
  hostname?: string;

  @ApiPropertyOptional({
    description: 'Port number',
    example: '443',
  })
  @IsOptional()
  @IsString()
  port?: string;

  @ApiPropertyOptional({
    description: 'Protocol',
    example: 'https:',
  })
  @IsOptional()
  @IsString()
  protocol?: string;

  @ApiPropertyOptional({
    description: 'Page title',
    example: 'Contact Us - Example Company',
  })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiPropertyOptional({
    description: 'Referrer URL',
    example: 'https://example.com/',
  })
  @IsOptional()
  @IsString()
  referrer?: string;

  @ApiPropertyOptional({
    description: 'Document domain',
    example: 'example.com',
  })
  @IsOptional()
  @IsString()
  domain?: string;

  @ApiPropertyOptional({
    description: 'Last modified date',
    example: '01/01/2024 12:00:00',
  })
  @IsOptional()
  @IsString()
  lastModified?: string;

  @ApiPropertyOptional({
    description: 'Character set',
    example: 'UTF-8',
  })
  @IsOptional()
  @IsString()
  charset?: string;

  @ApiPropertyOptional({
    description: 'Document ready state',
    example: 'complete',
  })
  @IsOptional()
  @IsString()
  readyState?: string;
}

/**
 * Time Information DTO
 * Captures timezone and locale data
 */
export class TimeInfoDto {
  @ApiPropertyOptional({
    description: 'Timezone identifier',
    example: 'Asia/Ho_Chi_Minh',
  })
  @IsOptional()
  @IsString()
  timezone?: string;

  @ApiPropertyOptional({
    description: 'Timezone offset in minutes',
    example: -420,
  })
  @IsOptional()
  @IsNumber()
  timezoneOffset?: number;

  @ApiPropertyOptional({
    description: 'Locale string',
    example: 'vi-VN',
  })
  @IsOptional()
  @IsString()
  locale?: string;

  @ApiPropertyOptional({
    description: 'Current timestamp',
    example: 1672531200000,
  })
  @IsOptional()
  @IsNumber()
  timestamp?: number;

  @ApiPropertyOptional({
    description: 'Current date string',
    example: '2024-01-01T12:00:00.000Z',
  })
  @IsOptional()
  @IsString()
  dateString?: string;
}

/**
 * Location Information DTO
 * Captures geolocation data (if available)
 */
export class LocationInfoDto {
  @ApiPropertyOptional({
    description: 'Latitude coordinate',
    example: 10.8231,
  })
  @IsOptional()
  @IsNumber()
  latitude?: number;

  @ApiPropertyOptional({
    description: 'Longitude coordinate',
    example: 106.6297,
  })
  @IsOptional()
  @IsNumber()
  longitude?: number;

  @ApiPropertyOptional({
    description: 'Location accuracy in meters',
    example: 10,
  })
  @IsOptional()
  @IsNumber()
  accuracy?: number;

  @ApiPropertyOptional({
    description: 'Altitude in meters',
    example: 5,
  })
  @IsOptional()
  @IsNumber()
  altitude?: number;

  @ApiPropertyOptional({
    description: 'Heading in degrees',
    example: 90,
  })
  @IsOptional()
  @IsNumber()
  heading?: number;

  @ApiPropertyOptional({
    description: 'Speed in meters per second',
    example: 0,
  })
  @IsOptional()
  @IsNumber()
  speed?: number;
}

/**
 * Client-side data DTO
 * Contains all client-side information collected from the browser
 */
export class ClientSideDataDto {
  @ApiPropertyOptional({
    description: 'Browser information',
    type: BrowserInfoDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => BrowserInfoDto)
  browserInfo?: BrowserInfoDto;

  @ApiPropertyOptional({
    description: 'Screen information',
    type: ScreenInfoDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => ScreenInfoDto)
  screenInfo?: ScreenInfoDto;

  @ApiPropertyOptional({
    description: 'Window information',
    type: WindowInfoDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => WindowInfoDto)
  windowInfo?: WindowInfoDto;

  @ApiPropertyOptional({
    description: 'Page information',
    type: PageInfoDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => PageInfoDto)
  pageInfo?: PageInfoDto;

  @ApiPropertyOptional({
    description: 'Time information',
    type: TimeInfoDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => TimeInfoDto)
  timeInfo?: TimeInfoDto;

  @ApiPropertyOptional({
    description: 'Location information (if permission granted)',
    type: LocationInfoDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => LocationInfoDto)
  location?: LocationInfoDto;
}

/**
 * Create Platform Data DTO
 * Used when creating a new website visitor session
 */
export class CreatePlatformDataDto {
  @ApiPropertyOptional({
    description: 'Client-side data collected from the browser',
    type: ClientSideDataDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => ClientSideDataDto)
  clientData?: ClientSideDataDto;

  @ApiPropertyOptional({
    description: 'Additional metadata',
    example: { source: 'contact_form', campaign: 'summer_sale' },
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * Update Platform Data DTO
 * Used when updating existing visitor context with new client-side data
 */
export class UpdatePlatformDataDto {
  @ApiPropertyOptional({
    description: 'Updated client-side data',
    type: ClientSideDataDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => ClientSideDataDto)
  clientData?: ClientSideDataDto;
}

/**
 * Platform Data Response DTO
 * Returns information about created/updated platform data
 */
export class PlatformDataResponseDto {
  @ApiProperty({
    description: 'ID of the UserConvertCustomer',
    example: 'customer_123e4567-e89b-12d3-a456-426614174000',
  })
  userConvertCustomerId: string;

  @ApiProperty({
    description: 'ID of the ExternalCustomerPlatformData (thread ID)',
    example: 'platform_987fcdeb-51a2-43d1-b2c3-456789abcdef',
  })
  externalCustomerPlatformDataId: string;

  @ApiProperty({
    description: 'Timestamp when the platform data was created',
    example: 1672531200000,
  })
  createdAt: number;

  @ApiPropertyOptional({
    description: 'Visitor information summary',
    example: {
      isNewVisitor: true,
      browserInfo: 'Chrome 120.0 on Windows',
      location: 'Ho Chi Minh City, Vietnam',
    },
  })
  @IsOptional()
  visitorSummary?: Record<string, any>;

  constructor(data: {
    userConvertCustomerId: string;
    externalCustomerPlatformDataId: string;
    createdAt?: number;
    visitorSummary?: Record<string, any>;
  }) {
    this.userConvertCustomerId = data.userConvertCustomerId;
    this.externalCustomerPlatformDataId = data.externalCustomerPlatformDataId;
    this.createdAt = data.createdAt || Date.now();
    this.visitorSummary = data.visitorSummary;
  }
}

/**
 * External Message Response DTO
 * Extends the standard MessageResponseDto for external messages
 */
export class ExternalMessageResponseDto {
  @ApiProperty({
    description: 'ID of the created external message',
    example: '0280c24b-c849-492d-b5fd-e1c927186272',
  })
  messageId: string | undefined;

  @ApiProperty({
    description: 'ID of the created run',
    example: 'run_123456-789-abc',
  })
  runId: string;

  @ApiProperty({
    description: 'Name of the website agent processing the message',
    example: 'Website Assistant',
  })
  agentName: string;

  @ApiProperty({
    description: 'Current status of the run',
    example: 'CREATED',
  })
  status: string;

  @ApiProperty({
    description: 'Timestamp when the run was created',
    example: 1672531200000,
  })
  createdAt: number;

  @ApiPropertyOptional({
    description: 'Array of deleted message IDs (for edit operations)',
    type: [String],
    example: ['msg_124', 'msg_125'],
  })
  deletedMessageIds?: string[];

  @ApiPropertyOptional({
    description: 'Debug information (development only)',
  })
  debug?: any;

  constructor(data: {
    messageId?: string;
    runId: string;
    agentName: string;
    status: string;
    createdAt: number;
    deletedMessageIds?: string[];
    debug?: any;
  }) {
    this.messageId = data.messageId;
    this.runId = data.runId;
    this.agentName = data.agentName;
    this.status = data.status;
    this.createdAt = data.createdAt;
    this.deletedMessageIds = data.deletedMessageIds;
    this.debug = data.debug;
  }
}