import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  ParseIntPipe,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { SimpleCustomerProductService } from '../services/product/simple-customer-product.service';
import { CustomerProductService } from '../services/product/customer-product.service';
import { SimpleCreateCustomerProductDto } from '../dto/customer-product/simple-create-customer-product.dto';
import { SimpleCustomerProductResponseDto } from '../dto/customer-product/simple-customer-product-response.dto';
import { QueryCustomerProductDto } from '../dto/customer-product/query-customer-product.dto';
import { BulkDeleteCustomerProductDto } from '../dto/customer-product/bulk-delete-customer-product.dto';

import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller xử lý các request cơ bản với Customer Product
 * Chỉ tập trung vào CRUD đơn giản với 3 fields cơ bản
 */
@ApiTags(SWAGGER_API_TAGS.USER_BUSINESS__SIMPLE_CUSTOMER_PRODUCT)
@Controller('user/simple-customer-products')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(
  ApiResponseDto,
  PaginatedResult,
  SimpleCreateCustomerProductDto,
  SimpleCustomerProductResponseDto,
  QueryCustomerProductDto,
  BulkDeleteCustomerProductDto,
)
export class SimpleCustomerProductController {
  constructor(
    private readonly simpleCustomerProductService: SimpleCustomerProductService,
    private readonly customerProductService: CustomerProductService,
  ) {}

  /**
   * Tạo sản phẩm khách hàng đơn giản
   * @param createDto DTO chứa 3 thông tin cơ bản
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin sản phẩm đã tạo
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Tạo sản phẩm khách hàng đơn giản',
    description:
      'Tạo sản phẩm khách hàng với 3 thông tin cơ bản: tên, mô tả, loại sản phẩm.',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Tạo sản phẩm khách hàng thành công',
    type: () => ApiResponseDto.getSchema(SimpleCustomerProductResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Không có quyền truy cập',
  })
  async create(
    @Body() createDto: SimpleCreateCustomerProductDto,
    @CurrentUser('id') userId: number,
  ) {
    const product =
      await this.simpleCustomerProductService.createSimpleCustomerProduct(
        createDto,
        userId,
      );
    return ApiResponseDto.created<SimpleCustomerProductResponseDto>(
      product,
      'Tạo sản phẩm khách hàng thành công',
    );
  }

  /**
   * Lấy danh sách sản phẩm khách hàng với phân trang
   * @param queryDto DTO chứa các tham số truy vấn
   * @param userId ID của người dùng hiện tại
   * @returns Danh sách sản phẩm với phân trang
   */
  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Lấy danh sách sản phẩm khách hàng',
    description:
      'Lấy danh sách sản phẩm khách hàng với phân trang, tìm kiếm và filter. Hỗ trợ filter theo danh sách ID sản phẩm cụ thể.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách sản phẩm thành công',
    type: () =>
      ApiResponseDto.getSchema(
        PaginatedResult<SimpleCustomerProductResponseDto>,
      ),
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Không có quyền truy cập',
  })
  async getList(
    @Query() queryDto: QueryCustomerProductDto,
    @CurrentUser('id') userId: number,
  ) {
    const result = await this.simpleCustomerProductService.getCustomerProducts(
      queryDto,
      userId,
    );
    return ApiResponseDto.success<
      PaginatedResult<SimpleCustomerProductResponseDto>
    >(result, 'Lấy danh sách sản phẩm khách hàng thành công');
  }

  /**
   * Lấy chi tiết sản phẩm khách hàng theo ID
   * @param id ID của sản phẩm
   * @param userId ID của người dùng hiện tại
   * @returns Chi tiết sản phẩm
   */
  @Get(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Lấy chi tiết sản phẩm khách hàng',
    description: 'Lấy thông tin chi tiết sản phẩm khách hàng theo ID.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của sản phẩm khách hàng',
    example: 123,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description:
      'Lấy chi tiết đầy đủ sản phẩm thành công - Response sẽ khác nhau tùy theo productType',
    schema: {
      oneOf: [
        { $ref: '#/components/schemas/CompletePhysicalProductResponseDto' },
        { $ref: '#/components/schemas/CompleteDigitalProductResponseDto' },
        { $ref: '#/components/schemas/CompleteServiceProductResponseDto' },
        { $ref: '#/components/schemas/CompleteEventProductResponseDto' },
        { $ref: '#/components/schemas/CompleteComboProductResponseDto' },
      ],
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy sản phẩm',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Không có quyền truy cập',
  })
  async getDetail(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number,
  ) {
    const product = await this.customerProductService.findByIdComplete(
      id,
      userId,
    );
    return ApiResponseDto.success(
      product,
      'Lấy chi tiết sản phẩm khách hàng thành công',
    );
  }

  /**
   * Xóa batch sản phẩm khách hàng
   * @param deleteDto DTO chứa danh sách ID cần xóa
   * @param userId ID của người dùng hiện tại
   * @returns Kết quả xóa đơn giản
   */
  @Delete()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Xóa batch sản phẩm khách hàng',
    description: 'Xóa nhiều sản phẩm khách hàng cùng lúc theo danh sách ID.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xóa sản phẩm thành công',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: {
          type: 'string',
          example: 'Xóa sản phẩm khách hàng thành công',
        },
        result: {
          type: 'object',
          properties: {
            deletedCount: { type: 'number', example: 2 },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Không có quyền truy cập',
  })
  async deleteBatch(
    @Body() deleteDto: BulkDeleteCustomerProductDto,
    @CurrentUser('id') userId: number,
  ) {
    const result =
      await this.simpleCustomerProductService.deleteCustomerProducts(
        deleteDto,
        userId,
      );
    return ApiResponseDto.success(result, 'Xóa sản phẩm khách hàng thành công');
  }
}
