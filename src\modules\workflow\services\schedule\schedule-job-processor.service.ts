/**
 * @file Schedule Job Processor
 * 
 * Bull queue processor cho scheduled workflows với recurring logic
 * Handle delayed jobs và tự động tạo next execution cho recurring schedules
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

import { Processor, Process, OnQueueCompleted, OnQueueFailed } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Job } from 'bull';
import { Node } from '../../entities/node.entity';
import { WorkflowExecutionUserService } from '../../user/services/workflow-execution-user.service';
import { EnhancedDelayedJobManagerService } from './enhanced-delayed-job-manager.service';
import { IScheduleParameters, EExecutionBehavior, EScheduleType } from '../../interfaces/core/utility/schedule.interface';

/**
 * Interface cho scheduled job data
 */
interface IScheduledJobData {
    nodeId: string;
    workflowId: string;
    scheduleConfig: IScheduleParameters;
    executionTime: number;
    isRecurring: boolean;
    scheduleType: EScheduleType;
    originalCronExpression?: string;
}

/**
 * Interface cho execution result
 */
interface IExecutionResult {
    executionId: string;
    status: 'success' | 'failed';
    startTime: number;
    endTime: number;
    duration: number;
    error?: string;
}

@Processor('workflow-execution')
@Injectable()
export class ScheduleJobProcessorService {
    private readonly logger = new Logger(ScheduleJobProcessorService.name);
    
    constructor(
        @InjectRepository(Node)
        private readonly nodeRepository: Repository<Node>,
        
        private readonly workflowExecutionService: WorkflowExecutionUserService,
        private readonly delayedJobManager: EnhancedDelayedJobManagerService
    ) {}
    
    /**
     * Process scheduled workflow job
     */
    @Process('execute-scheduled-workflow')
    async executeScheduledWorkflow(job: Job<IScheduledJobData>): Promise<IExecutionResult> {
        const startTime = Date.now();
        const {
            nodeId,
            workflowId,
            scheduleConfig,
            executionTime,
            isRecurring,
            scheduleType
        } = job.data;
        
        this.logger.log(`Processing scheduled workflow job: ${job.id} for workflow: ${workflowId}, node: ${nodeId}`);
        
        try {
            // Update job progress
            await job.progress(10);
            
            // Validate execution time (not too far in the past)
            const now = Date.now();
            const timeDiff = now - executionTime;
            const maxDelay = 5 * 60 * 1000; // 5 minutes tolerance
            
            if (timeDiff > maxDelay) {
                this.logger.warn(`Execution time too far in the past: ${timeDiff}ms for job: ${job.id}`);
            }
            
            await job.progress(20);
            
            // Execute workflow based on execution behavior
            const executionResult = await this.executeWorkflowByBehavior(
                scheduleConfig.execution_behavior,
                workflowId,
                scheduleConfig.target_node_id,
                scheduleConfig.event_name
            );
            
            await job.progress(80);
            
            const endTime = Date.now();
            const duration = endTime - startTime;
            
            // Update execution statistics in node parameters
            await this.updateExecutionStats(nodeId, true, duration);
            
            this.logger.log(`Scheduled workflow execution completed: ${workflowId} ` +
                          `(${duration}ms, job: ${job.id})`);
            
            await job.progress(90);
            
            // Handle recurring schedules - create next job
            if (isRecurring) {
                await this.createNextRecurringJob(job.data);
            } else {
                // For one-time schedules, clear job info
                await this.clearJobInfo(nodeId);
            }
            
            await job.progress(100);
            
            return {
                executionId: executionResult?.id || `exec-${job.id}`,
                status: 'success',
                startTime,
                endTime,
                duration
            };
            
        } catch (error) {
            const endTime = Date.now();
            const duration = endTime - startTime;
            
            this.logger.error(`Scheduled workflow execution failed: ${workflowId}, job: ${job.id}`, error);
            
            // Update failure statistics
            await this.updateExecutionStats(nodeId, false, duration, error.message);
            
            // For recurring schedules, still try to create next job despite failure
            if (isRecurring) {
                try {
                    await this.createNextRecurringJob(job.data);
                } catch (nextJobError) {
                    this.logger.error(`Failed to create next job after failure: ${job.id}`, nextJobError);
                }
            }
            
            throw error; // Re-throw để Bull có thể handle retry
        }
    }
    
    /**
     * Execute workflow based on execution behavior
     */
    private async executeWorkflowByBehavior(
        behavior: EExecutionBehavior,
        workflowId: string,
        targetNodeId?: string,
        eventName?: string
    ): Promise<any> {
        const SYSTEM_USER_ID = 0; // System user for scheduled executions
        
        switch (behavior) {
            case EExecutionBehavior.START_WORKFLOW:
                return await this.workflowExecutionService.executeWorkflow(
                    SYSTEM_USER_ID,
                    workflowId
                );
                
            case EExecutionBehavior.START_FROM_NODE:
                if (!targetNodeId) {
                    throw new Error('Target node ID is required for START_FROM_NODE behavior');
                }
                return await this.workflowExecutionService.executeWorkflowNode(
                    SYSTEM_USER_ID,
                    workflowId,
                    targetNodeId
                );
                
            case EExecutionBehavior.TRIGGER_EVENT:
                // Fallback to executeWorkflow since triggerEvent is not available
                this.logger.warn(`triggerEvent not implemented, falling back to executeWorkflow for event: ${eventName}`);
                return await this.workflowExecutionService.executeWorkflow(
                    SYSTEM_USER_ID,
                    workflowId
                );
                
            default:
                throw new Error(`Unsupported execution behavior: ${behavior}`);
        }
    }
    
    /**
     * Create next recurring job
     */
    private async createNextRecurringJob(originalJobData: IScheduledJobData): Promise<void> {
        try {
            const { nodeId, workflowId, scheduleConfig } = originalJobData;
            
            // Create next delayed job
            const result = await this.delayedJobManager.createNextRecurringJob(originalJobData);
            
            if (result.isValid) {
                // Update node parameters với job info mới
                await this.updateNodeJobInfo(nodeId, result.jobId, result.nextExecutionTime);
                
                this.logger.log(`Created next recurring job: ${result.jobId} for node: ${nodeId}`);
            } else {
                this.logger.error(`Failed to create next recurring job for node: ${nodeId}, error: ${result.error}`);
            }
            
        } catch (error) {
            this.logger.error(`Failed to create next recurring job for node: ${originalJobData.nodeId}`, error);
        }
    }
    
    /**
     * Update execution statistics trong node parameters
     */
    private async updateExecutionStats(
        nodeId: string,
        success: boolean,
        duration: number,
        errorMessage?: string
    ): Promise<void> {
        try {
            const node = await this.nodeRepository.findOne({ where: { id: nodeId } });
            
            if (node && node.parameters) {
                const currentParams = node.parameters as IScheduleParameters;
                
                const updatedParams: IScheduleParameters = {
                    ...currentParams,
                    executionCount: (currentParams.executionCount || 0) + 1,
                    lastExecutionTime: Date.now(),
                    lastExecutionStatus: success ? 'success' : 'failed'
                };
                
                await this.nodeRepository.update(nodeId, {
                    parameters: updatedParams
                });
                
                this.logger.debug(`Updated execution stats for node: ${nodeId}, success: ${success}, duration: ${duration}ms`);
            }
            
        } catch (error) {
            this.logger.error(`Failed to update execution stats for node: ${nodeId}`, error);
        }
    }
    
    /**
     * Update node job info
     */
    private async updateNodeJobInfo(
        nodeId: string,
        jobId: string,
        nextExecutionTime: number
    ): Promise<void> {
        try {
            const node = await this.nodeRepository.findOne({ where: { id: nodeId } });
            
            if (node && node.parameters) {
                const updatedParams: IScheduleParameters = {
                    ...node.parameters as IScheduleParameters,
                    jobId,
                    nextExecutionTime,
                    jobCreatedAt: Date.now()
                };
                
                await this.nodeRepository.update(nodeId, {
                    parameters: updatedParams
                });
            }
            
        } catch (error) {
            this.logger.error(`Failed to update node job info: ${nodeId}`, error);
        }
    }
    
    /**
     * Clear job info for one-time schedules
     */
    private async clearJobInfo(nodeId: string): Promise<void> {
        try {
            const node = await this.nodeRepository.findOne({ where: { id: nodeId } });
            
            if (node && node.parameters) {
                const updatedParams: IScheduleParameters = {
                    ...node.parameters as IScheduleParameters,
                    jobId: undefined,
                    nextExecutionTime: undefined,
                    is_active: false // Disable one-time schedules after execution
                };
                
                await this.nodeRepository.update(nodeId, {
                    parameters: updatedParams
                });
                
                this.logger.log(`Cleared job info and disabled one-time schedule: ${nodeId}`);
            }
            
        } catch (error) {
            this.logger.error(`Failed to clear job info for node: ${nodeId}`, error);
        }
    }
    
    /**
     * Handle successful job completion
     */
    @OnQueueCompleted()
    onCompleted(job: Job<IScheduledJobData>, result: IExecutionResult) {
        const { nodeId, workflowId, isRecurring, scheduleType } = job.data;
        
        this.logger.log(`Job ${job.id} completed successfully. ` +
                       `Workflow: ${workflowId}, Node: ${nodeId}, ` +
                       `Type: ${scheduleType}, Duration: ${result.duration}ms, ` +
                       `Recurring: ${isRecurring}`);
    }
    
    /**
     * Handle job failure
     */
    @OnQueueFailed()
    onFailed(job: Job<IScheduledJobData>, error: Error) {
        const { nodeId, workflowId, isRecurring, scheduleType } = job.data;
        
        this.logger.error(`Job ${job.id} failed. ` +
                         `Workflow: ${workflowId}, Node: ${nodeId}, ` +
                         `Type: ${scheduleType}, Recurring: ${isRecurring}, ` +
                         `Error: ${error.message}`);
    }
    
    /**
     * Get processor health status
     */
    getHealthStatus(): {
        isHealthy: boolean;
        processorName: string;
        lastProcessedAt?: Date;
        totalProcessed: number;
        totalFailed: number;
    } {
        // This would be implemented with actual metrics tracking
        return {
            isHealthy: true,
            processorName: 'ScheduleJobProcessor',
            lastProcessedAt: new Date(),
            totalProcessed: 0,
            totalFailed: 0
        };
    }
}
