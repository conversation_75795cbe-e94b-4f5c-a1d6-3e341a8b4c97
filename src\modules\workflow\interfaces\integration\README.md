# Integration Node Interfaces

Comprehensive type-safe interfaces cho workflow automation integration nodes, theo industry best practices từ Make.com và n8n.

## 📋 **Overview**

Module này cung cấp complete interfaces cho tất cả integration providers, bao gồm:

- **Facebook**: Page management, Ads management
- **Google**: Sheets, Docs, Gmail, Calendar
- **Type-safe parameters** với validation functions
- **Registry system** cho node management
- **Industry-standard patterns** từ Make.com/n8n

## 🏗️ **Architecture**

### **Base Integration Framework**

```typescript
// Base interface cho tất cả integration nodes
interface IBaseIntegrationParameters {
    integration_id: string;
    operation: EIntegrationOperationType;
    error_handling?: EErrorHandling;
    timeout?: number;
    retry_attempts?: number;
}
```

### **Operation Types (theo Make.com patterns)**

```typescript
enum EIntegrationOperationType {
    // Triggers (Watch/Polling)
    WATCH = 'watch',
    WEBHOOK = 'webhook',
    
    // Actions (CRUD operations)
    CREATE = 'create',
    UPDATE = 'update',
    DELETE = 'delete',
    GET = 'get',
    
    // Searches
    SEARCH = 'search',
    LIST = 'list',
    
    // Universal
    API_CALL = 'apiCall'
}
```

## 📚 **Provider Interfaces**

### **Facebook Page Operations**

```typescript
// Trigger: Watch Posts
interface IWatchPostsParameters extends ITriggerParameters {
    operation: EFacebookPageOperation.WATCH_POSTS;
    page_id: string;
    post_types?: EFacebookPostType[];
    include_comments?: boolean;
    limit?: number;
}

// Action: Create Post with Photo
interface ICreatePostWithPhotoParameters extends IActionParameters {
    operation: EFacebookPageOperation.CREATE_POST_WITH_PHOTO;
    page_id: string;
    message: string;
    photo: string;
    caption?: string;
    privacy?: EFacebookPostPrivacy;
}
```

### **Google Sheets Operations**

```typescript
// Action: Append Row
interface IAppendRowParameters extends IActionParameters {
    operation: EGoogleSheetsOperation.APPEND_ROW;
    spreadsheet_id: string;
    sheet_name: string;
    values: any[][];
    value_input_option?: EValueInputOption;
}

// Trigger: Watch Rows
interface IWatchRowsParameters extends ITriggerParameters {
    operation: EGoogleSheetsOperation.WATCH_ROWS;
    spreadsheet_id: string;
    sheet_name?: string;
    range?: string;
    limit?: number;
}
```

### **Google Gmail Operations**

```typescript
// Action: Send Email
interface ISendEmailParameters extends IActionParameters {
    operation: EGoogleGmailOperation.SEND_EMAIL;
    to: string[];
    cc?: string[];
    subject: string;
    body_html?: string;
    body_text?: string;
    attachments?: Array<{
        filename: string;
        content: string; // base64
        content_type: string;
    }>;
}
```

## 🔧 **Registry System**

### **Node Type Registry**

```typescript
enum EIntegrationNodeType {
    FACEBOOK_PAGE = 'facebook-page',
    FACEBOOK_ADS = 'facebook-ads',
    GOOGLE_SHEETS = 'google-sheets',
    GOOGLE_DOCS = 'google-docs',
    GOOGLE_GMAIL = 'google-gmail',
    GOOGLE_CALENDAR = 'google-calendar'
}
```

### **Validation Registry**

```typescript
// Validate parameters cho specific node type
const result = validateIntegrationNodeParameters(
    EIntegrationNodeType.FACEBOOK_PAGE,
    {
        integration_id: 'fb-123',
        operation: EFacebookPageOperation.CREATE_POST,
        page_id: 'page-456',
        message: 'Hello World!'
    }
);

if (!result.isValid) {
    console.log('Validation errors:', result.errors);
}
```

### **Properties Registry**

```typescript
// Get node properties cho UI rendering
const properties = getIntegrationNodeProperties(
    EIntegrationNodeType.GOOGLE_SHEETS
);

// Properties include field definitions, load options, validation rules
```

### **Credentials Registry**

```typescript
// Get credential definition
const credential = getIntegrationNodeCredential(
    EIntegrationNodeType.GOOGLE_GMAIL
);

// Returns: OAuth2 config, test URL, required scopes
```

## 🎯 **Usage Examples**

### **1. Facebook Page - Create Post with Photo**

```typescript
import { 
    EIntegrationNodeType,
    EFacebookPageOperation,
    ICreatePostWithPhotoParameters,
    validateIntegrationNodeParameters
} from './integration';

const params: ICreatePostWithPhotoParameters = {
    integration_id: 'facebook-integration-123',
    operation: EFacebookPageOperation.CREATE_POST_WITH_PHOTO,
    page_id: 'my-facebook-page-id',
    message: 'Check out this amazing photo!',
    photo: 'https://example.com/photo.jpg',
    privacy: EFacebookPostPrivacy.PUBLIC,
    error_handling: EErrorHandling.RETRY,
    retry_attempts: 3
};

// Validate parameters
const validation = validateIntegrationNodeParameters(
    EIntegrationNodeType.FACEBOOK_PAGE,
    params
);

if (validation.isValid) {
    // Execute node
    console.log('Parameters are valid, executing node...');
} else {
    console.error('Validation failed:', validation.errors);
}
```

### **2. Google Sheets - Append Row**

```typescript
import {
    EIntegrationNodeType,
    EGoogleSheetsOperation,
    IAppendRowParameters,
    EValueInputOption
} from './integration';

const params: IAppendRowParameters = {
    integration_id: 'google-sheets-integration-456',
    operation: EGoogleSheetsOperation.APPEND_ROW,
    spreadsheet_id: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
    sheet_name: 'Sheet1',
    values: [['John Doe', '<EMAIL>', '2024-01-15']],
    value_input_option: EValueInputOption.USER_ENTERED,
    include_values_in_response: true
};

// Validate and execute
const validation = validateIntegrationNodeParameters(
    EIntegrationNodeType.GOOGLE_SHEETS,
    params
);
```

### **3. Gmail - Send Email with Attachments**

```typescript
import {
    EIntegrationNodeType,
    EGoogleGmailOperation,
    ISendEmailParameters
} from './integration';

const params: ISendEmailParameters = {
    integration_id: 'gmail-integration-789',
    operation: EGoogleGmailOperation.SEND_EMAIL,
    to: ['<EMAIL>'],
    cc: ['<EMAIL>'],
    subject: 'Important Document',
    body_html: '<h1>Please find attached document</h1>',
    attachments: [{
        filename: 'document.pdf',
        content: 'base64-encoded-content-here',
        content_type: 'application/pdf'
    }],
    send_as: '<EMAIL>'
};
```

## 🔍 **Utility Functions**

### **Node Type Detection**

```typescript
// Get node type by operation
const nodeType = getNodeTypeByOperation('createPost');
// Returns: EIntegrationNodeType.FACEBOOK_PAGE

// Check if node is trigger
const isTrigger = isIntegrationTriggerNode(
    EIntegrationNodeType.GOOGLE_SHEETS,
    'watchRows'
); // Returns: true

// Get provider name
const provider = getProviderFromNodeType(
    EIntegrationNodeType.GOOGLE_GMAIL
); // Returns: 'google'
```

### **Operation Validation**

```typescript
// Check if operation is valid for node type
const isValid = isValidOperationForNodeType(
    EIntegrationNodeType.FACEBOOK_PAGE,
    EFacebookPageOperation.CREATE_POST
); // Returns: true

// Get all available operations
const operations = getIntegrationNodeOperations(
    EIntegrationNodeType.GOOGLE_CALENDAR
);
// Returns: ['watchEvents', 'createEvent', 'updateEvent', ...]
```

## 🚀 **Integration với Workflow System**

### **Node Definition Integration**

```typescript
// Trong NodeDefinition entity
{
    name: 'Facebook Page',
    type: EIntegrationNodeType.FACEBOOK_PAGE,
    group: ENodeGroup.INTEGRATION,
    properties: getIntegrationNodeProperties(EIntegrationNodeType.FACEBOOK_PAGE),
    credentials: [getIntegrationNodeCredential(EIntegrationNodeType.FACEBOOK_PAGE)],
    loadOptions: {
        // Dynamic loading cho pages, spreadsheets, etc.
    }
}
```

### **Execution Integration**

```typescript
// Trong workflow execution engine
async function executeIntegrationNode(
    nodeType: EIntegrationNodeType,
    parameters: IIntegrationParameters,
    input: IBaseIntegrationInput
): Promise<IBaseIntegrationOutput> {
    
    // Validate parameters
    const validation = validateIntegrationNodeParameters(nodeType, parameters);
    if (!validation.isValid) {
        throw new Error(`Invalid parameters: ${validation.errors.join(', ')}`);
    }
    
    // Execute based on node type and operation
    switch (nodeType) {
        case EIntegrationNodeType.FACEBOOK_PAGE:
            return await executeFacebookPageNode(parameters, input);
        case EIntegrationNodeType.GOOGLE_SHEETS:
            return await executeGoogleSheetsNode(parameters, input);
        // ... other cases
    }
}
```

## 📝 **Best Practices**

### **1. Parameter Validation**
- Always validate parameters trước khi execution
- Sử dụng type guards cho type safety
- Provide clear error messages

### **2. Error Handling**
- Implement retry logic cho network failures
- Handle rate limits appropriately
- Provide meaningful error responses

### **3. Type Safety**
- Sử dụng TypeScript interfaces consistently
- Leverage union types cho parameter validation
- Use type guards cho runtime checks

### **4. Extensibility**
- Follow established patterns khi add new providers
- Maintain backward compatibility
- Document breaking changes

## 🔗 **Related Files**

- `base/base-integration.interface.ts` - Base interfaces và enums
- `facebook/` - Facebook provider interfaces
- `google/` - Google provider interfaces
- `index.ts` - Registry system và exports
- `../core/` - Core workflow node interfaces
- `../../entities/` - Database entities
- `../../services/` - Service implementations
