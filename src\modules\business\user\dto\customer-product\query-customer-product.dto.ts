import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsOptional,
  IsString,
  IsNumber,
  Min,
  Max,
  IsArray,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ProductTypeEnum, EntityStatusEnum } from '@modules/business/enums';

/**
 * DTO cho việc truy vấn danh sách sản phẩm khách hàng
 */
export class QueryCustomerProductDto {
  @ApiProperty({
    description: 'Số trang',
    example: 1,
    minimum: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiProperty({
    description: 'Số lượng item trên mỗi trang',
    example: 10,
    minimum: 1,
    maximum: 100,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @ApiProperty({
    description: 'Từ khóa tìm kiếm (tìm trong tên sản phẩm)',
    example: 'áo thun',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({
    description: 'Danh sách ID sản phẩm khách hàng để lọc',
    example: [1, 2, 3],
    type: [Number],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @Type(() => Number)
  ids?: number[];

  @ApiProperty({
    description: 'Trạng thái sản phẩm',
    enum: EntityStatusEnum,
    example: EntityStatusEnum.APPROVED,
    required: false,
  })
  @IsOptional()
  @IsEnum(EntityStatusEnum)
  status?: EntityStatusEnum;

  @ApiProperty({
    description: 'Loại sản phẩm',
    enum: ProductTypeEnum,
    example: ProductTypeEnum.PHYSICAL,
    required: false,
  })
  @IsOptional()
  @IsEnum(ProductTypeEnum)
  productType?: ProductTypeEnum;

  @ApiProperty({
    description: 'Loại bỏ sản phẩm COMBO khỏi kết quả',
    example: true,
    required: false,
  })
  @IsOptional()
  @Type(() => Boolean)
  excludeCombo?: boolean;

  @ApiProperty({
    description: 'Sắp xếp theo trường',
    example: 'createdAt',
    enum: ['createdAt', 'updatedAt', 'name'],
    required: false,
  })
  @IsOptional()
  @IsString()
  sortBy?: 'createdAt' | 'updatedAt' | 'name' = 'createdAt';

  @ApiProperty({
    description: 'Thứ tự sắp xếp',
    example: 'DESC',
    enum: ['ASC', 'DESC'],
    required: false,
  })
  @IsOptional()
  @IsString()
  sortOrder?: 'ASC' | 'DESC' = 'DESC';
}
