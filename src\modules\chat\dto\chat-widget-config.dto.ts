import { DisplayMode, SideMode } from '@/modules/integration/entities/box-chat-config.entity';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsEnum, IsOptional, IsArray } from 'class-validator';


/**
 * Complete Chat Widget Configuration DTO
 * Contains all UI settings and customization options for website chat widget
 */
export class ChatWidgetConfigDto {
  @ApiPropertyOptional({
    description: 'Welcome message displayed when chat widget opens',
    example: 'Xin chào, tôi có thể giúp gì bạn?',
  })
  @IsOptional()
  @IsString()
  welcomeText?: string;

  @ApiPropertyOptional({
    description: 'Placeholder text for message input field',
    example: 'Nhập tin nhắn của bạn...',
  })
  @IsOptional()
  @IsString()
  placeholderMessage?: string;

  @ApiProperty({
    description: 'Chat widget display mode',
    enum: DisplayMode,
    example: DisplayMode.CENTER,
  })
  @IsEnum(DisplayMode)
  displayMode: DisplayMode;

  @ApiProperty({
    description: 'Chat widget positioning mode',
    enum: SideMode,
    example: SideMode.FLOATING,
  })
  @IsEnum(SideMode)
  sideMode: SideMode;

  @ApiProperty({
    description: 'Primary theme color for the widget (hex color)',
    example: '#3B82F6',
  })
  @IsString()
  colorPrimary: string | null;

  @ApiPropertyOptional({
    description: 'Array of widget components to display',
    type: 'array',
    items: { type: 'string' },
    example: ['header', 'input', 'quickReplies'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  components: string[] | null;

  @ApiPropertyOptional({
    description: 'Array of quick reply message strings for easy user interaction',
    type: 'array',
    items: { type: 'string' },
    example: ['Tôi cần hỗ trợ', 'Giá cả như thế nào?', 'Liên hệ hỗ trợ'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  quickMessages?: string[];

  @ApiProperty({
    description: 'CDN URL for chat widget avatar image',
    example: 'https://cdn.example.com/avatar.jpg?expires=1672531200',
  })
  @IsString()
  avatarUrl: string;

  @ApiProperty({
    description: 'CDN URL for chat widget icon image',
    example: 'https://cdn.example.com/icon.png?expires=1672531200',
  })
  @IsString()
  iconUrl: string;

  @ApiProperty({
    description: 'Display name of the agent/chatbot',
    example: 'Customer Support Assistant',
  })
  @IsString()
  agentName: string;

  @ApiPropertyOptional({
    description: 'CDN URL for agent avatar (fallback if widget avatar not set)',
    example: 'https://cdn.example.com/agent-avatar.jpg?expires=1672531200',
  })
  @IsOptional()
  @IsString()
  agentAvatarUrl?: string;

  @ApiPropertyOptional({
    description: 'Array of CDN URLs for slideshow images displayed in chat widget',
    type: 'array',
    items: { type: 'string' },
    example: [
      'https://cdn.example.com/slide1.jpg?expires=1672531200',
      'https://cdn.example.com/slide2.jpg?expires=1672531200'
    ],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  slideshowImages?: string[];

  constructor(data: {
    welcomeText?: string;
    placeholderMessage?: string;
    displayMode: DisplayMode;
    sideMode: SideMode;
    colorPrimary: string | null;
    components: string[] | null;
    quickMessages?: string[];
    avatarUrl: string;
    iconUrl: string;
    agentName: string;
    agentAvatarUrl?: string;
    slideshowImages?: string[];
  }) {
    this.welcomeText = data.welcomeText;
    this.placeholderMessage = data.placeholderMessage;
    this.displayMode = data.displayMode;
    this.sideMode = data.sideMode;
    this.colorPrimary = data.colorPrimary;
    this.components = data.components;
    this.quickMessages = data.quickMessages;
    this.avatarUrl = data.avatarUrl;
    this.iconUrl = data.iconUrl;
    this.agentName = data.agentName;
    this.agentAvatarUrl = data.agentAvatarUrl;
    this.slideshowImages = data.slideshowImages;
  }
}