import {
  registerDecorator,
  ValidationOptions,
  ValidationArguments,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  ValidateNested,
  IsArray,
  IsUUID,
  IsEnum,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ImageContentBlockDto } from './image-content-block.dto';
import { FileContentBlockDto } from './file-content-block.dto';
import { MessageContentType, AttachmentContentType } from './enums';

// Re-export enums for external message usage
export { MessageContentType, AttachmentContentType };

/**
 * Union type for attachment content blocks (external messages)
 */
export type ExternalAttachmentContentDto = ImageContentBlockDto | FileContentBlockDto;

/**
 * Content block DTO for external messages (website visitors)
 * Excludes tool call decisions which are not supported for website visitors
 */
export class ExternalContentBlockDto {
  @ApiProperty({
    description: 'The type of content block (external messages support TEXT and ATTACHMENT only)',
    enum: [MessageContentType.TEXT, MessageContentType.ATTACHMENT],
    example: MessageContentType.TEXT,
  })
  @IsEnum([MessageContentType.TEXT, MessageContentType.ATTACHMENT])
  @IsNotEmpty()
  type: MessageContentType.TEXT | MessageContentType.ATTACHMENT;

  @ApiProperty({
    description:
      'Text content - required and non-empty when type is "text", optional when type is "attachment"',
    example: 'Hello, I need help with my order.',
    required: false,
  })
  @IsOptional()
  @IsString()
  text?: string;

  @ApiProperty({
    description:
      'Array of attachments - required and non-empty when type is "attachment", must be undefined when type is "text"',
    type: 'array',
    items: {
      oneOf: [
        { $ref: '#/components/schemas/ImageContentBlockDto' },
        { $ref: '#/components/schemas/FileContentBlockDto' },
      ],
    },
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => Object, {
    keepDiscriminatorProperty: true,
    discriminator: {
      property: 'type',
      subTypes: [
        { value: ImageContentBlockDto, name: AttachmentContentType.IMAGE },
        { value: FileContentBlockDto, name: AttachmentContentType.FILE },
      ],
    },
  })
  attachments?: ExternalAttachmentContentDto[];
}

/**
 * Custom validator for external content block fields
 */
function ValidateExternalContentBlockFields(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'ValidateExternalContentBlockFields',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: ExternalContentBlockDto) {
          if (!value || typeof value !== 'object') return false;

          const { type, text, attachments } = value;

          switch (type) {
            case MessageContentType.TEXT:
              // For text type: text must be defined and non-empty, attachments must be undefined
              return (
                typeof text === 'string' &&
                text.trim().length > 0 &&
                attachments === undefined
              );

            case MessageContentType.ATTACHMENT:
              // For attachment type: attachments must be defined and non-empty, text is optional
              return (
                Array.isArray(attachments) &&
                attachments.length > 0
              );

            default:
              return false;
          }
        },
        defaultMessage() {
          return 'External content block fields must match the specified type requirements. For "text": only text field allowed (non-empty). For "attachment": attachments array required (non-empty), text optional.';
        },
      },
    });
  };
}

/**
 * Custom validator to ensure that when replyToMessageId exists, content type must be 'text'
 */
function ValidateExternalReplyToMessageRequiresTextType(
  validationOptions?: ValidationOptions,
) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'ValidateExternalReplyToMessageRequiresTextType',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          const messageRequest = args.object as ExternalMessageRequestDto;

          // If replyToMessageId exists, contentBlocks type must be 'text'
          if (messageRequest.replyToMessageId) {
            return (
              messageRequest.contentBlocks?.type === MessageContentType.TEXT
            );
          }

          // If no replyToMessageId, no restriction
          return true;
        },
        defaultMessage() {
          return 'When replyToMessageId is provided, contentBlocks type must be "text".';
        },
      },
    });
  };
}

// Validation function removed - message editing not supported for external messages

/**
 * External Message Request DTO for website visitors
 * Simplified version of MessageRequestDto without unsupported features:
 * - No tool call decisions
 * - No agent selection (uses website's configured agents)
 * - No alwaysApproveToolCall
 * - No webSearchEnabled
 * - No message editing (messageId field removed)
 */
export class ExternalMessageRequestDto {
  @ApiProperty({
    description:
      'Content block that makes up the message. Supports TEXT and ATTACHMENT types only.',
    type: ExternalContentBlockDto,
  })
  @ValidateNested()
  @Type(() => ExternalContentBlockDto)
  @ValidateExternalContentBlockFields({
    message: 'External content block fields must match the specified type requirements.',
  })
  @ValidateExternalReplyToMessageRequiresTextType({
    message:
      'When replyToMessageId is provided, contentBlocks type must be "text".',
  })
  contentBlocks: ExternalContentBlockDto;

  @ApiPropertyOptional({
    description:
      'Optional message ID to reply to. Message editing is not supported for website visitors.',
    example: 'd74374dc-3e67-4bda-922e-2f11b3f835a8',
  })
  @IsOptional()
  @IsUUID()
  replyToMessageId?: string;
}

/**
 * Example payloads for ExternalMessageRequestDto
 * Used in Swagger (OpenAPI) documentation
 */

// --- Reusable Mock IDs for Clarity ---
const EXTERNAL_REPLY_TO_MESSAGE_ID = 'a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6';
const EXTERNAL_FILE_ID_1 = 'f1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c6';
const EXTERNAL_IMAGE_ID_1 = 'img1-a1b2-c3d4-d5e6-f7a8b9c0d1e2';

// --- Individual Example Payloads ---

const externalSimpleTextMessage: Partial<ExternalMessageRequestDto> = {
  contentBlocks: {
    type: MessageContentType.TEXT,
    text: 'Hello, I need help with my order. Can you assist me?',
  },
};

const externalReplyMessage: Partial<ExternalMessageRequestDto> = {
  replyToMessageId: EXTERNAL_REPLY_TO_MESSAGE_ID,
  contentBlocks: {
    type: MessageContentType.TEXT,
    text: 'Thanks for the information! That helps a lot.',
  },
};

const externalMessageWithAttachments: Partial<ExternalMessageRequestDto> = {
  contentBlocks: {
    type: MessageContentType.ATTACHMENT,
    text: 'Here is the receipt you requested:',
    attachments: [
      {
        type: AttachmentContentType.IMAGE,
        fileId: EXTERNAL_IMAGE_ID_1,
      },
      {
        type: AttachmentContentType.FILE,
        fileId: EXTERNAL_FILE_ID_1,
      },
    ],
  },
};

// Message editing is not supported for external messages

// --- Assembled Examples for Swagger Decorator ---

export const ExternalMessageRequestExamples = {
  'Simple Text Message': {
    summary: 'Basic text message from website visitor',
    description: 'A simple message with just text content from a website visitor.',
    value: externalSimpleTextMessage,
  },
  'Reply Message': {
    summary: 'Reply to a previous message',
    description: 'Uses the replyToMessageId field to indicate a reply to a previous message.',
    value: externalReplyMessage,
  },
  'Message with Attachments': {
    summary: 'Message with attachments',
    description:
      'Contains text and attachments (image and file). Text is optional for attachment type.',
    value: externalMessageWithAttachments,
  },
};