/**
 * @file Google Sheets Integration Node Interface
 * 
 * Đ<PERSON><PERSON> nghĩa type-safe interface cho Google Sheets node operations
 * Theo patterns từ Make.com và n8n industry standards
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

import {
    IBaseIntegrationParameters,
    ITriggerParameters,
    IActionParameters,
    ISearchParameters,
    IBaseIntegrationInput,
    IBaseIntegrationOutput,
    EIntegrationOperationType,
    EIntegrationErrorHandling,
    IBaseIntegrationCredential
} from '../base/base-integration.interface';

import {
    ECredentialName,
    ENodeAuthType,
    EPropertyType,
    ELoadOptionsResource,
    ELoadOptionsMethod,
    INodeProperty
} from '../../node-manager.interface';

import {
    ITypedNodeExecution
} from '../../execute.interface';

// =================================================================
// SECTION 1: GOOGLE SHEETS ENUMS
// =================================================================

/**
 * Google Sheets specific operations
 */
export enum EGoogleSheetsOperation {
    // === TRIGGERS ===
    /** Watch for new rows */
    WATCH_ROWS = 'watchRows',
    /** Watch for updated rows */
    WATCH_UPDATED_ROWS = 'watchUpdatedRows',
    /** Watch for new spreadsheets */
    WATCH_SPREADSHEETS = 'watchSpreadsheets',

    // === DOCUMENT ACTIONS ===
    /** Create new spreadsheet */
    CREATE_SPREADSHEET = 'createSpreadsheet',
    /** Delete spreadsheet */
    DELETE_SPREADSHEET = 'deleteSpreadsheet',
    /** Get spreadsheet info */
    GET_SPREADSHEET = 'getSpreadsheet',

    // === SHEET ACTIONS ===
    /** Create new sheet */
    CREATE_SHEET = 'createSheet',
    /** Delete sheet */
    DELETE_SHEET = 'deleteSheet',
    /** Clear sheet */
    CLEAR_SHEET = 'clearSheet',

    // === ROW ACTIONS ===
    /** Append row */
    APPEND_ROW = 'appendRow',
    /** Update row */
    UPDATE_ROW = 'updateRow',
    /** Delete row */
    DELETE_ROW = 'deleteRow',
    /** Get row */
    GET_ROW = 'getRow',

    // === RANGE ACTIONS ===
    /** Read range */
    READ_RANGE = 'readRange',
    /** Update range */
    UPDATE_RANGE = 'updateRange',
    /** Clear range */
    CLEAR_RANGE = 'clearRange',

    // === SEARCHES ===
    /** Search rows */
    SEARCH_ROWS = 'searchRows',
    /** List all rows */
    LIST_ROWS = 'listRows',
    /** Get all sheets */
    GET_SHEETS = 'getSheets'
}

/**
 * Google Sheets value input options
 */
export enum EValueInputOption {
    /** Values will be parsed as if typed into the UI */
    USER_ENTERED = 'USER_ENTERED',
    /** Values will not be parsed and stored as-is */
    RAW = 'RAW'
}

/**
 * Google Sheets value render options
 */
export enum EValueRenderOption {
    /** Values will be calculated & formatted */
    FORMATTED_VALUE = 'FORMATTED_VALUE',
    /** Values will be calculated but not formatted */
    UNFORMATTED_VALUE = 'UNFORMATTED_VALUE',
    /** Values will not be calculated */
    FORMULA = 'FORMULA'
}

/**
 * Google Sheets dimension
 */
export enum EDimension {
    /** Rows */
    ROWS = 'ROWS',
    /** Columns */
    COLUMNS = 'COLUMNS'
}

// =================================================================
// SECTION 2: GOOGLE SHEETS PARAMETERS
// =================================================================

/**
 * Watch Rows trigger parameters
 */
export interface IWatchRowsParameters extends ITriggerParameters {
    operation: EGoogleSheetsOperation.WATCH_ROWS;

    /** Spreadsheet ID */
    spreadsheet_id: string;

    /** Sheet name */
    sheet_name?: string;

    /** Range to watch (A1 notation) */
    range?: string;

    /** Include header row */
    include_header?: boolean;

    /** Maximum rows per poll */
    limit?: number;
}

/**
 * Create Spreadsheet parameters
 */
export interface ICreateSpreadsheetParameters extends IActionParameters {
    operation: EGoogleSheetsOperation.CREATE_SPREADSHEET;

    /** Spreadsheet title */
    title: string;

    /** Initial sheet properties */
    sheets?: Array<{
        title: string;
        grid_properties?: {
            row_count?: number;
            column_count?: number;
        };
    }>;

    /** Locale */
    locale?: string;

    /** Time zone */
    time_zone?: string;
}

/**
 * Append Row parameters
 */
export interface IAppendRowParameters extends IActionParameters {
    operation: EGoogleSheetsOperation.APPEND_ROW;

    /** Spreadsheet ID */
    spreadsheet_id: string;

    /** Sheet name */
    sheet_name: string;

    /** Values to append */
    values: any[][];

    /** Value input option */
    value_input_option?: EValueInputOption;

    /** Insert data option */
    insert_data_option?: 'OVERWRITE' | 'INSERT_ROWS';

    /** Include values in response */
    include_values_in_response?: boolean;
}

/**
 * Update Row parameters
 */
export interface IUpdateRowParameters extends IActionParameters {
    operation: EGoogleSheetsOperation.UPDATE_ROW;

    /** Spreadsheet ID */
    spreadsheet_id: string;

    /** Sheet name */
    sheet_name: string;

    /** Row number (1-based) */
    row_number: number;

    /** Values to update */
    values: any[];

    /** Value input option */
    value_input_option?: EValueInputOption;
}

/**
 * Read Range parameters
 */
export interface IReadRangeParameters extends IActionParameters {
    operation: EGoogleSheetsOperation.READ_RANGE;

    /** Spreadsheet ID */
    spreadsheet_id: string;

    /** Range in A1 notation */
    range: string;

    /** Value render option */
    value_render_option?: EValueRenderOption;

    /** Date time render option */
    date_time_render_option?: 'SERIAL_NUMBER' | 'FORMATTED_STRING';

    /** Major dimension */
    major_dimension?: EDimension;
}

/**
 * Update Range parameters
 */
export interface IUpdateRangeParameters extends IActionParameters {
    operation: EGoogleSheetsOperation.UPDATE_RANGE;

    /** Spreadsheet ID */
    spreadsheet_id: string;

    /** Range in A1 notation */
    range: string;

    /** Values to update */
    values: any[][];

    /** Value input option */
    value_input_option?: EValueInputOption;

    /** Major dimension */
    major_dimension?: EDimension;
}

/**
 * Search Rows parameters
 */
export interface ISearchRowsParameters extends ISearchParameters {
    operation: EGoogleSheetsOperation.SEARCH_ROWS;

    /** Spreadsheet ID */
    spreadsheet_id: string;

    /** Sheet name */
    sheet_name: string;

    /** Search query */
    query?: string;

    /** Column to search in */
    search_column?: string;

    /** Include header row */
    include_header?: boolean;

    /** Return all matching rows */
    return_all_matches?: boolean;
}

/**
 * Union type cho tất cả Google Sheets parameters
 */
export type IGoogleSheetsParameters = 
    | IWatchRowsParameters
    | ICreateSpreadsheetParameters
    | IAppendRowParameters
    | IUpdateRowParameters
    | IReadRangeParameters
    | IUpdateRangeParameters
    | ISearchRowsParameters;

// =================================================================
// SECTION 3: INPUT/OUTPUT INTERFACES
// =================================================================

/**
 * Google Sheets input interface
 */
export interface IGoogleSheetsInput extends IBaseIntegrationInput {
    /** Spreadsheet data */
    spreadsheet?: {
        id?: string;
        title?: string;
        url?: string;
    };

    /** Sheet data */
    sheet?: {
        name?: string;
        id?: number;
        index?: number;
    };

    /** Row data */
    row?: {
        values?: any[];
        index?: number;
    };

    /** Range data */
    range?: {
        notation?: string;
        values?: any[][];
    };
}

/**
 * Google Sheets output interface
 */
export interface IGoogleSheetsOutput extends IBaseIntegrationOutput {
    /** Google Sheets specific data */
    google_sheets?: {
        spreadsheet_id?: string;
        spreadsheet_url?: string;
        sheet_id?: number;
        sheet_name?: string;
        range?: string;
        updated_rows?: number;
        updated_columns?: number;
        updated_cells?: number;
        values?: any[][];
    };
}

// =================================================================
// SECTION 4: NODE PROPERTIES DEFINITION
// =================================================================

/**
 * Google Sheets node properties
 */
export const GOOGLE_SHEETS_PROPERTIES: INodeProperty[] = [
    {
        name: 'operation',
        displayName: 'Operation',
        type: EPropertyType.Options,
        required: true,
        options: [
            // Triggers
            { name: 'Watch Rows', value: EGoogleSheetsOperation.WATCH_ROWS },
            { name: 'Watch Updated Rows', value: EGoogleSheetsOperation.WATCH_UPDATED_ROWS },
            
            // Document Actions
            { name: 'Create Spreadsheet', value: EGoogleSheetsOperation.CREATE_SPREADSHEET },
            { name: 'Get Spreadsheet', value: EGoogleSheetsOperation.GET_SPREADSHEET },
            
            // Row Actions
            { name: 'Append Row', value: EGoogleSheetsOperation.APPEND_ROW },
            { name: 'Update Row', value: EGoogleSheetsOperation.UPDATE_ROW },
            { name: 'Get Row', value: EGoogleSheetsOperation.GET_ROW },
            
            // Range Actions
            { name: 'Read Range', value: EGoogleSheetsOperation.READ_RANGE },
            { name: 'Update Range', value: EGoogleSheetsOperation.UPDATE_RANGE },
            
            // Searches
            { name: 'Search Rows', value: EGoogleSheetsOperation.SEARCH_ROWS },
            { name: 'List Rows', value: EGoogleSheetsOperation.LIST_ROWS }
        ]
    },
    {
        name: 'spreadsheet_id',
        displayName: 'Spreadsheet',
        type: EPropertyType.Options,
        required: true,
        loadOptions: {
            resource: ELoadOptionsResource.GOOGLE_SHEETS,
            method: ELoadOptionsMethod.GET_SPREADSHEETS,
            dependsOn: ['integration_id']
        }
    },
    {
        name: 'sheet_name',
        displayName: 'Sheet',
        type: EPropertyType.Options,
        loadOptions: {
            resource: ELoadOptionsResource.GOOGLE_SHEETS,
            method: ELoadOptionsMethod.GET_SHEETS,
            dependsOn: ['spreadsheet_id']
        }
    },
    {
        name: 'range',
        displayName: 'Range',
        type: EPropertyType.String,
        placeholder: 'A1:Z100',
        description: 'Range in A1 notation (e.g., A1:Z100)'
    },
    {
        name: 'values',
        displayName: 'Values',
        type: EPropertyType.Array,
        properties: [
            {
                name: 'value',
                displayName: 'Value',
                type: EPropertyType.String
            }
        ]
    },
    {
        name: 'value_input_option',
        displayName: 'Value Input Option',
        type: EPropertyType.Options,
        options: [
            { name: 'User Entered', value: EValueInputOption.USER_ENTERED },
            { name: 'Raw', value: EValueInputOption.RAW }
        ]
    },
    {
        name: 'include_header',
        displayName: 'Include Header Row',
        type: EPropertyType.Boolean
    },
    {
        name: 'limit',
        displayName: 'Limit',
        type: EPropertyType.Number,
        minValue: 1,
        maxValue: 1000
    }
];

// =================================================================
// SECTION 5: CREDENTIAL DEFINITION
// =================================================================

/**
 * Google Sheets credential definition
 */
export const GOOGLE_SHEETS_CREDENTIAL: IBaseIntegrationCredential = {
    provider: 'google',
    name: ECredentialName.GOOGLE_OAUTH,
    displayName: 'Google OAuth2',
    description: 'OAuth2 authentication for Google Sheets',
    required: true,
    authType: ENodeAuthType.OAUTH2,
    testable: true,
    testUrl: '/api/integrations/test-connection'
};

// =================================================================
// SECTION 6: VALIDATION FUNCTIONS
// =================================================================

/**
 * Validate Google Sheets parameters
 */
export function validateGoogleSheetsParameters(
    params: Partial<IGoogleSheetsParameters>
): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check spreadsheet_id for operations that need it
    const needsSpreadsheetId = params.operation !== EGoogleSheetsOperation.CREATE_SPREADSHEET;
    if (needsSpreadsheetId && !(params as any).spreadsheet_id) {
        errors.push('Spreadsheet ID is required');
    }

    // Operation specific validation
    if (params.operation === EGoogleSheetsOperation.APPEND_ROW ||
        params.operation === EGoogleSheetsOperation.UPDATE_ROW) {
        if (!(params as IAppendRowParameters).sheet_name) {
            errors.push('Sheet name is required');
        }
        if (!(params as IAppendRowParameters).values) {
            errors.push('Values are required');
        }
    }

    if (params.operation === EGoogleSheetsOperation.READ_RANGE ||
        params.operation === EGoogleSheetsOperation.UPDATE_RANGE) {
        if (!(params as IReadRangeParameters).range) {
            errors.push('Range is required');
        }
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}

/**
 * Type guard cho Google Sheets parameters
 */
export function isGoogleSheetsParameters(params: any): params is IGoogleSheetsParameters {
    return params && Object.values(EGoogleSheetsOperation).includes(params.operation);
}

/**
 * Type-safe node execution cho Google Sheets
 */
export type IGoogleSheetsNodeExecution = ITypedNodeExecution<
    IGoogleSheetsInput,
    IGoogleSheetsOutput,
    IGoogleSheetsParameters
>;
