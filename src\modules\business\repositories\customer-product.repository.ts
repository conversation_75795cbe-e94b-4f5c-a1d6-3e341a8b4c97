import { PaginatedResult } from '@common/response';
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Not, Repository } from 'typeorm';
import { CustomerProduct } from '../entities/customer-product.entity';
import { PhysicalProductVariant } from '../entities/physical-product-variant.entity';
import { EntityStatusEnum, ProductTypeEnum } from '../enums';

/**
 * Repository cho CustomerProduct entity
 * Xử lý các thao tác database cho sản phẩm khách hàng
 */
@Injectable()
export class CustomerProductRepository {
  private readonly logger = new Logger(CustomerProductRepository.name);

  constructor(
    @InjectRepository(CustomerProduct)
    private readonly repository: Repository<CustomerProduct>,
    @InjectRepository(PhysicalProductVariant)
    private readonly physicalProductVariantRepository: Repository<PhysicalProductVariant>,
  ) {}

  /**
   * <PERSON><PERSON><PERSON> sản phẩm khách hàng mới
   * @param data Dữ liệu sản phẩm
   * @returns Sản phẩm đã tạo
   */
  async create(data: Partial<CustomerProduct>): Promise<CustomerProduct> {
    const product = this.repository.create({
      ...data,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    return this.repository.save(product);
  }

  /**
   * Tìm sản phẩm theo ID (chỉ lấy sản phẩm chưa bị xóa)
   * @param id ID sản phẩm
   * @returns Sản phẩm hoặc null
   */
  async findById(id: number): Promise<CustomerProduct | null> {
    return this.repository.findOne({
      where: {
        id,
        status: Not(EntityStatusEnum.DELETED), // Loại trừ sản phẩm đã xóa
      },
    });
  }

  /**
   * Tìm sản phẩm theo ID và userId
   * @param id ID sản phẩm
   * @param userId ID người dùng
   * @returns Sản phẩm hoặc null
   */
  async findByIdAndUserId(
    id: number,
    userId: number,
  ): Promise<CustomerProduct | null> {
    this.logger.debug(`Tìm sản phẩm: id=${id}, userId=${userId}`);

    const result = await this.repository.findOne({
      where: {
        id,
        userId,
        status: EntityStatusEnum.APPROVED,
      },
    });

    this.logger.debug(
      `Kết quả tìm sản phẩm: ${result ? 'Tìm thấy' : 'Không tìm thấy'}`,
    );
    if (result) {
      this.logger.debug(
        `Sản phẩm tìm thấy: id=${result.id}, userId=${result.userId}, status=${result.status}`,
      );
    }

    return result;
  }

  /**
   * Tìm sản phẩm theo ID và userId (bao gồm tất cả status trừ DELETED)
   * @param id ID sản phẩm
   * @param userId ID người dùng
   * @returns Sản phẩm hoặc null
   */
  async findByIdAndUserIdAllStatus(
    id: number,
    userId: number,
  ): Promise<CustomerProduct | null> {
    this.logger.debug(`Tìm sản phẩm (all status): id=${id}, userId=${userId}`);

    const result = await this.repository.findOne({
      where: {
        id,
        userId,
        status: Not(EntityStatusEnum.DELETED), // Tất cả status trừ DELETED
      },
    });

    this.logger.debug(
      `Kết quả tìm sản phẩm (all status): ${result ? 'Tìm thấy' : 'Không tìm thấy'}`,
    );
    if (result) {
      this.logger.debug(
        `Sản phẩm tìm thấy: id=${result.id}, userId=${result.userId}, status=${result.status}`,
      );
    }

    return result;
  }

  /**
   * Lấy danh sách sản phẩm với phân trang và filter
   * @param query Tham số truy vấn
   * @returns Danh sách sản phẩm với phân trang
   */
  async findAll(query: {
    page?: number;
    limit?: number;
    search?: string;
    status?: EntityStatusEnum;
    productType?: string;
    userId?: number;
    excludeCombo?: boolean;
    ids?: number[];
  }): Promise<PaginatedResult<CustomerProduct>> {
    const {
      page = 1,
      limit = 10,
      search,
      status,
      productType,
      userId,
      excludeCombo,
      ids,
    } = query;

    const queryBuilder = this.repository.createQueryBuilder('product');

    // Filter theo userId nếu có
    if (userId) {
      queryBuilder.andWhere('product.userId = :userId', { userId });
    }

    // Filter theo status nếu có
    if (status) {
      queryBuilder.andWhere('product.status = :status', { status });
    } else {
      // Mặc định chỉ lấy sản phẩm đã được duyệt
      queryBuilder.andWhere('product.status = :approvedStatus', {
        approvedStatus: EntityStatusEnum.APPROVED,
      });
    }

    // Filter theo productType nếu có
    if (productType) {
      queryBuilder.andWhere('product.productType = :productType', {
        productType,
      });
    }

    // Loại bỏ sản phẩm COMBO nếu được yêu cầu
    if (excludeCombo) {
      queryBuilder.andWhere('product.productType != :comboType', {
        comboType: 'COMBO',
      });
    }

    // Tìm kiếm theo tên nếu có
    if (search) {
      queryBuilder.andWhere('product.name ILIKE :search', {
        search: `%${search}%`,
      });
    }

    // Filter theo danh sách IDs nếu có
    if (ids && ids.length > 0) {
      queryBuilder.andWhere('product.id IN (:...ids)', { ids });
    }

    // Sắp xếp theo thời gian tạo mới nhất
    queryBuilder.orderBy('product.createdAt', 'DESC');

    // Phân trang
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Thực hiện truy vấn
    const [items, total] = await queryBuilder.getManyAndCount();

    return {
      items,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Cập nhật sản phẩm
   * @param id ID sản phẩm
   * @param data Dữ liệu cập nhật
   * @returns Sản phẩm đã cập nhật
   */
  async update(
    id: number,
    data: Partial<CustomerProduct>,
  ): Promise<CustomerProduct> {
    await this.repository.update(id, {
      ...data,
      updatedAt: Date.now(),
    });

    const updatedProduct = await this.findById(id);
    if (!updatedProduct) {
      throw new Error(`CustomerProduct with ID ${id} not found after update`);
    }

    return updatedProduct;
  }

  /**
   * Xóa mềm sản phẩm (cập nhật status thành DELETED)
   * @param id ID sản phẩm
   */
  async softDelete(id: number): Promise<void> {
    await this.repository.update(id, {
      status: EntityStatusEnum.DELETED,
      updatedAt: Date.now(),
    });
  }

  /**
   * Xóa cứng sản phẩm khỏi database
   * @param id ID sản phẩm
   */
  async hardDelete(id: number): Promise<void> {
    await this.repository.delete(id);
  }

  /**
   * Đếm số lượng sản phẩm theo userId
   * @param userId ID người dùng
   * @returns Số lượng sản phẩm
   */
  async countByUserId(userId: number): Promise<number> {
    return this.repository.count({
      where: {
        userId,
        status: EntityStatusEnum.APPROVED, // Chỉ đếm sản phẩm đã duyệt
      },
    });
  }

  /**
   * Lưu sản phẩm
   * @param product Sản phẩm cần lưu
   * @returns Sản phẩm đã lưu
   */
  async save(product: CustomerProduct): Promise<CustomerProduct> {
    return this.repository.save(product);
  }

  /**
   * Tìm sản phẩm theo danh sách IDs (chỉ lấy sản phẩm đã được duyệt)
   * @param ids Danh sách ID
   * @returns Danh sách sản phẩm
   */
  async findByIds(ids: number[]): Promise<CustomerProduct[]> {
    if (!ids.length) return [];

    return this.repository.find({
      where: {
        id: In(ids),
        status: EntityStatusEnum.APPROVED, // Chỉ lấy sản phẩm đã được duyệt
      },
    });
  }

  /**
   * Tìm sản phẩm theo danh sách IDs và userId (chỉ lấy sản phẩm đã được duyệt)
   * @param ids Danh sách ID
   * @param userId ID người dùng
   * @returns Danh sách sản phẩm
   */
  async findByIdsAndUserId(
    ids: number[],
    userId: number,
  ): Promise<CustomerProduct[]> {
    if (!ids.length) return [];

    this.logger.debug(
      `Tìm sản phẩm theo IDs: ${ids.join(', ')} cho userId=${userId}`,
    );

    const products = await this.repository.find({
      where: {
        id: In(ids),
        userId,
        status: EntityStatusEnum.APPROVED, // Chỉ lấy sản phẩm đã được duyệt
      },
    });

    this.logger.debug(`Tìm thấy ${products.length} sản phẩm`);
    return products;
  }

  /**
   * Lấy tất cả customer product IDs của một người dùng (cho search auto-populate)
   * @param userId ID của người dùng
   * @returns Danh sách customer product IDs
   */
  async getAllCustomerProductIdsByUserId(userId: number): Promise<number[]> {
    try {
      this.logger.debug(`Lấy tất cả customer product IDs cho user ${userId}`);

      const result = await this.repository
        .createQueryBuilder('product')
        .select('product.id', 'id')
        .where('product.userId = :userId', { userId })
        .andWhere('product.status = :approvedStatus', {
          approvedStatus: EntityStatusEnum.APPROVED,
        })
        .getRawMany();

      const productIds = result.map((item) => parseInt(item.id));
      this.logger.debug(
        `Tìm thấy ${productIds.length} customer product IDs cho user ${userId}`,
      );

      return productIds;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy customer product IDs cho user ${userId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lấy sản phẩm với thông tin chi tiết từ các bảng con và variants
   * @param ids Danh sách ID sản phẩm
   * @returns Danh sách sản phẩm với thông tin chi tiết
   */
  async findByIdsWithDetailsAndVariants(
    ids: number[],
  ): Promise<CustomerProduct[]> {
    if (!ids.length) return [];

    const queryBuilder = this.repository
      .createQueryBuilder('product')
      .whereInIds(ids)
      .andWhere('product.status = :approvedStatus', {
        approvedStatus: EntityStatusEnum.APPROVED,
      }) // Chỉ lấy sản phẩm đã được duyệt
      .leftJoinAndSelect(
        'physical_products',
        'physical',
        'physical.id = product.id AND product.productType = :physicalType',
        { physicalType: 'PHYSICAL' },
      )
      .leftJoinAndSelect(
        'physical_product_variants',
        'variants',
        'variants.physical_product_id = product.id AND product.productType = :physicalType',
        { physicalType: 'PHYSICAL' },
      )
      .leftJoinAndSelect(
        'digital_products',
        'digital',
        'digital.id = product.id AND product.productType = :digitalType',
        { digitalType: 'DIGITAL' },
      )
      .leftJoinAndSelect(
        'event_products',
        'event',
        'event.id = product.id AND product.productType = :eventType',
        { eventType: 'EVENT' },
      )
      .leftJoinAndSelect(
        'service_products',
        'service',
        'service.id = product.id AND product.productType = :serviceType',
        { serviceType: 'SERVICE' },
      )
      .leftJoinAndSelect(
        'combo_products',
        'combo',
        'combo.id = product.id AND product.productType = :comboType',
        { comboType: 'COMBO' },
      );

    return queryBuilder.getMany();
  }

  /**
   * Lấy sản phẩm với variant cụ thể
   * @param productId ID sản phẩm
   * @param variantId ID variant
   * @param userId ID người dùng
   * @returns Sản phẩm và variant hoặc null
   */
  async findByIdWithVariant(
    productId: number,
    variantId: number,
    userId: number,
  ): Promise<{
    product: CustomerProduct;
    variant: PhysicalProductVariant;
  } | null> {
    const product = await this.findByIdAndUserId(productId, userId);
    if (!product || product.productType !== ProductTypeEnum.PHYSICAL) {
      return null;
    }

    const variant = await this.physicalProductVariantRepository.findOne({
      where: { id: variantId, physicalProductId: productId },
    });

    if (!variant) {
      return null;
    }

    return { product, variant };
  }

  /**
   * Lấy danh sách sản phẩm theo userId với phân trang
   * @param userId ID người dùng
   * @param query Tham số truy vấn
   * @returns Danh sách sản phẩm với phân trang
   */
  async findByUserIdWithPagination(
    userId: number,
    query: any,
  ): Promise<PaginatedResult<CustomerProduct>> {
    return this.findAll({ ...query, userId });
  }

  /**
   * Xóa mềm nhiều sản phẩm theo IDs và userId
   * @param ids Danh sách ID sản phẩm
   * @param userId ID người dùng
   * @returns Số lượng sản phẩm đã xóa thành công
   */
  async softDeleteByIdsAndUserId(
    ids: number[],
    userId: number,
  ): Promise<{
    deletedCount: number;
    failedIds: number[];
    failedReasons?: string[];
  }> {
    try {
      this.logger.debug(
        `Attempting to delete products: ${ids.join(', ')} for userId: ${userId}`,
      );

      // Xóa trực tiếp các sản phẩm thuộc về user (bao gồm cả REJECTED)
      const updateResult = await this.repository.update(
        {
          id: In(ids),
          userId,
          status: In([EntityStatusEnum.APPROVED, EntityStatusEnum.REJECTED]),
        },
        {
          status: EntityStatusEnum.DELETED,
          updatedAt: Date.now(),
        },
      );

      const deletedCount = updateResult.affected || 0;
      this.logger.debug(`Successfully deleted ${deletedCount} products`);

      return {
        deletedCount,
        failedIds: [],
        failedReasons: [],
      };
    } catch (error) {
      this.logger.error(`Lỗi khi xóa batch sản phẩm: ${error.message}`);
      throw error;
    }
  }
}
