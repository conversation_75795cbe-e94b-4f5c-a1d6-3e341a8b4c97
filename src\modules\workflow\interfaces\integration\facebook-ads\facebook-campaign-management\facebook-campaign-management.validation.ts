/**
 * @file Facebook Ads Campaign Management Validation & Credentials
 * 
 * <PERSON><PERSON><PERSON> nghĩa validation functions và credentials cho Facebook Ads Campaign Management integration
 * Theo patterns từ Make.com chuẩn
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

import {
    IBaseIntegrationCredential
} from '../../base/base-integration.interface';

import {
    ECredentialName,
    ENodeAuthType
} from '../../../node-manager.interface';

import {
    ITypedNodeExecution
} from '../../../execute.interface';

import {
    EFacebookCampaignManagementOperation
} from './facebook-campaign-management.types';

import {
    IFacebookCampaignManagementInput,
    IFacebookCampaignManagementOutput,
    IFacebookCampaignManagementParameters,
    IListCampaignsParameters,
    IUpdateCampaignParameters,
    IListAdSetsParameters,
    IUpdateAdSetParameters,
    IListAdsParameters,
    IUpdateAdParameters,
    IGetReachEstimateParameters,
    ISearchAdInterestsParameters,
    ISearchLocationsParameters
} from './facebook-campaign-management.interface';

// =================================================================
// CREDENTIAL DEFINITION
// =================================================================

/**
 * Facebook Campaign Management credential definition
 */
export const FACEBOOK_CAMPAIGN_MANAGEMENT_CREDENTIAL: IBaseIntegrationCredential = {
    provider: 'facebook',
    name: ECredentialName.FACEBOOK_OAUTH,
    displayName: 'Facebook Ads Campaign Management OAuth2',
    description: 'OAuth2 authentication for Facebook Ads Campaign Management',
    required: true,
    authType: ENodeAuthType.OAUTH2,
    testable: true,
    testUrl: '/api/integrations/test-connection'
};

// =================================================================
// VALIDATION FUNCTIONS
// =================================================================

/**
 * Validate Facebook Campaign Management parameters
 */
export function validateFacebookCampaignManagementParameters(
    params: Partial<IFacebookCampaignManagementParameters>
): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check required fields - Kiểm tra các trường bắt buộc
    if (!params.operation) {
        errors.push('Operation is required');
    }

    // Operation specific validation - Xác thực theo từng thao tác
    switch (params.operation) {
        case EFacebookCampaignManagementOperation.LIST_CAMPAIGNS:
            const listCampaignsParams = params as IListCampaignsParameters;
            if (!listCampaignsParams.business_id) {
                errors.push('Business ID is required for List Campaigns operation');
            }
            break;

        case EFacebookCampaignManagementOperation.UPDATE_CAMPAIGN:
            const updateCampaignParams = params as IUpdateCampaignParameters;
            if (!updateCampaignParams.business_id) {
                errors.push('Business ID is required for Update Campaign operation');
            }
            break;

        case EFacebookCampaignManagementOperation.LIST_AD_SETS:
            const listAdSetsParams = params as IListAdSetsParameters;
            if (!listAdSetsParams.business_id) {
                errors.push('Business ID is required for List Ad Sets operation');
            }
            break;

        case EFacebookCampaignManagementOperation.UPDATE_AD_SET:
            const updateAdSetParams = params as IUpdateAdSetParameters;
            if (!updateAdSetParams.business_id) {
                errors.push('Business ID is required for Update Ad Set operation');
            }
            break;

        case EFacebookCampaignManagementOperation.LIST_ADS:
            const listAdsParams = params as IListAdsParameters;
            if (!listAdsParams.business_id) {
                errors.push('Business ID is required for List Ads operation');
            }
            break;

        case EFacebookCampaignManagementOperation.UPDATE_AD:
            const updateAdParams = params as IUpdateAdParameters;
            if (!updateAdParams.business_id) {
                errors.push('Business ID is required for Update Ad operation');
            }
            break;

        case EFacebookCampaignManagementOperation.GET_REACH_ESTIMATE:
            const getReachEstimateParams = params as IGetReachEstimateParameters;
            if (!getReachEstimateParams.business_id) {
                errors.push('Business ID is required for Get Reach Estimate operation');
            }
            break;

        case EFacebookCampaignManagementOperation.SEARCH_AD_INTERESTS:
            const searchAdInterestsParams = params as ISearchAdInterestsParameters;
            if (!searchAdInterestsParams.name) {
                errors.push('Name is required for Search Ad Interests operation');
            }
            break;

        case EFacebookCampaignManagementOperation.SEARCH_LOCATIONS:
            const searchLocationsParams = params as ISearchLocationsParameters;
            if (!searchLocationsParams.location_types || searchLocationsParams.location_types.length === 0) {
                errors.push('Location Types is required for Search Locations operation');
            }
            break;
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}

/**
 * Type guard cho Facebook Campaign Management parameters
 */
export function isFacebookCampaignManagementParameters(params: any): params is IFacebookCampaignManagementParameters {
    return params && Object.values(EFacebookCampaignManagementOperation).includes(params.operation);
}

/**
 * Type-safe node execution cho Facebook Campaign Management
 */
export type IFacebookCampaignManagementNodeExecution = ITypedNodeExecution<
    IFacebookCampaignManagementInput,
    IFacebookCampaignManagementOutput,
    IFacebookCampaignManagementParameters
>;
