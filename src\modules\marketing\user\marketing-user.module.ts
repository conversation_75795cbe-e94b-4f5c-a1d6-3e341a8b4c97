import {
  Module,
  forwardRef,
  NestModule,
  MiddlewareConsumer,
} from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { BullModule } from '@nestjs/bullmq';
import { HttpModule } from '@nestjs/axios';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ScheduleModule } from '@nestjs/schedule';
import * as entities from './entities';
import * as repositories from './repositories';
import * as services from './services';
import * as controllers from './controllers';
import { UserTwilioSmsController } from './controllers/user-twilio-sms.controller';
import { UserTwilioEmailController } from './controllers/user-twilio-email.controller';
import { UserSegmentTagController } from './controllers/user-segment-tag.controller';
import { GmailMarketingController } from './controllers/gmail-marketing.controller';
import { ZaloContentController } from './controllers/zalo-content.controller';

// Import new webhook services and handlers
import { ZaloEventRouterService } from './controllers/zalo-webhook/shared/services/zalo-event-router.service';
import { ZaloMessageSenderService } from './controllers/zalo-webhook/shared/services/zalo-message-sender.service';
import { ZaloApiClient } from '@shared/services/zalo/zalo-api.client';
import { ZaloModule } from '@shared/services/zalo/zalo.module';
import { ZaloPersonalModule } from '@/shared/services/zalo-personal';
import { SmsModule } from '@/shared/services/sms';
import { EmailModule } from '@/shared/services/email';
import { ServicesModule } from '@/shared/services/services.module';
import { QueueName } from '@shared/queue/queue.constants';
import { IntegrationUserModule } from '@/modules/integration/user/integration-user.module';
import { IntegrationModule } from '@/modules/integration/integration.module';
import { ZaloUploadLoggerMiddleware } from './middleware/zalo-upload-logger.middleware';
// ❌ REMOVED: import { SubscriptionUserModule } - Gây circular dependency
import {
  AddonHandlerFactory,
  ADDON_HANDLERS,
} from '@/modules/subscription/services/addon-usage-handlers'; // ✅ ADDED: Direct import
import {
  UserAddonUsageRepository,
  UsageLogRepository,
} from '@/modules/subscription/repositories'; // ✅ ADDED: Repositories cần thiết
import { UsageLog, UserAddonUsage } from '@/modules/subscription/entities'; // ✅ ADDED: Entities cho TypeORM

// Import các repositories và services cụ thể
import { UserTagRepository } from './repositories/user-tag.repository';
import { UserSegmentRepository } from './repositories/user-segment.repository';
import { UserAudienceRepository } from './repositories/user-audience.repository';
import { UserAudienceCustomFieldRepository } from './repositories/user-audience-custom-field.repository';
import { UserAudienceCustomFieldDefinitionRepository } from './repositories/user-audience-custom-field-definition.repository';
import { UserAudienceHasTagRepository } from './repositories/user-audience-has-tag.repository';
import { UserCampaignRepository } from './repositories/user-campaign.repository';
import { UserCampaignHistoryRepository } from './repositories/user-campaign-history.repository';
import { UserTemplateEmailRepository } from './repositories/user-template-email.repository';
import { UserTemplateSmsRepository } from './repositories/user-template-sms.repository';
import { SmsCampaignUserRepository } from './repositories/sms-campaign-user.repository';
import { SystemTemplateEmailRepository } from '../admin/repositories/system-template-email.repository';
import { SystemTemplateEmail } from '../admin/entities/system-template-email.entity';
import { UserConvertCustomerRepository } from '@modules/business/repositories/user-convert-customer.repository';
import { UserConvertCustomer } from '@modules/business/entities/user-convert-customer.entity';
import { UserOrderRepository } from '@modules/business/repositories/user-order.repository';
import { UserOrder } from '@modules/business/entities/user-order.entity';
import { MediaRepository } from '@/modules/data/media/repositories/media.repository';
import { Media } from '@/modules/data/media/entities/media.entity';
import { Integration } from '@/modules/integration/entities/integration.entity';
import { IntegrationProvider } from '@/modules/integration/entities/integration-provider.entity';
import { UserResource } from '@/modules/user/entities/user-resource.entity';

import { UserTagService } from './services/user-tag.service';
import { UserSegmentService } from './services/user-segment.service';
import { GmailMarketingService } from './services/gmail-marketing.service';
import { UserAudienceService } from './services/user-audience.service';
import { UserCampaignService } from './services/user-campaign.service';
import { CampaignDataTransformerService } from './services/campaign-data-transformer.service';
import { UserTemplateEmailService } from './services/user-template-email.service';
import { UserSystemTemplateEmailService } from './services/user-system-template-email.service';
import { UserMarketingStatisticsService } from './services/user-marketing-statistics.service';
import { UserAudienceCustomFieldDefinitionService } from './services/user-audience-custom-field-definition.service';
import { UserAudienceCustomFieldService } from './services/user-audience-custom-field.service';
import { UserSegmentTagService } from './services/user-segment-tag.service';
import { MarketingOverviewService } from './services/marketing-overview.service';
import { ZaloService } from './services/zalo.service';
import { ZaloZnsService } from './services/zalo-zns.service';
import { ZaloZnsUserFeedbackService } from './services/zalo-zns-user-feedback.service';
import { ZaloZnsCampaignService } from './services/zalo-zns-campaign.service';
import { ZnsPersonalizationService } from './services/zns-personalization.service';
import { ZaloTokenService } from './services/zalo-token.service';
import { EmailMarketingService } from './services/email-marketing.service';
import { UserTemplateSmsService } from './services/user-template-sms.service';
import { SmsCampaignService } from './services/sms-campaign.service';
import { SmsServersService } from './services/sms-servers.service';
import { UserTwilioSmsService } from './services/user-twilio-sms.service';
import { UserTwilioEmailService } from './services/user-twilio-email.service';
import { ZaloOaMessageCampaignService } from './services/zalo-oa-message-campaign.service';
import { ZaloAudienceSyncService } from './services/zalo-audience-sync.service';
import { ZaloUserSyncService } from './services/zalo-user-sync.service';
import { ZaloStatisticsService } from './services/zalo-statistics.service';
import { ZaloGroupManagementService } from './services/zalo-group-management.service';

import { ZaloGroupMessageService } from './services/zalo-group-message.service';
import { ZaloArticleManagementService } from './services/zalo-article-management.service';
import { ZaloGroupSyncService } from './services/zalo-group-sync.service';
import { ZaloAiResponseQueueService } from './services/zalo-ai-response-queue.service';
import { ZaloWebhookQueueService } from './services/zalo-webhook-queue.service';
import { ZaloMessageSseService } from './services/zalo-message-sse.service';
import { ZaloConversationExportService } from './services/zalo-conversation-export.service';
import { ZaloQRCodeSseService } from './services/zalo-qr-code-sse.service';

// Import shared Zalo services
import { ZaloZnsInfoService } from '@shared/services/zalo/zalo-zns-info.service';
import { RedisService } from '@shared/services/redis.service';

import { UserTagController } from './controllers/user-tag.controller';
import { UserAudienceController } from './controllers/user-audience.controller';
import { UserSegmentController } from './controllers/user-segment.controller';
import { UserCampaignController } from './controllers/user-campaign.controller';
import { UserMarketingStatisticsController } from './controllers/user-marketing-statistics.controller';
import { UserTemplateEmailController } from './controllers/user-template-email.controller';
import { UserSystemTemplateEmailController } from './controllers/user-system-template-email.controller';
import { UserAudienceCustomFieldDefinitionController } from './controllers/user-audience-custom-field-definition.controller';
import { UserAudienceCustomFieldController } from './controllers/user-audience-custom-field.controller';
import { MarketingOverviewController } from './controllers/marketing-overview.controller';
import { ZaloController } from './controllers/zalo.controller';
import { ZaloPersonalController } from './controllers/zalo-personal.controller';
import { ZaloBroadcastController } from './controllers/zalo-broadcast.controller';
import { ZaloUnifiedMessageController } from './controllers/zalo-unified-message.controller';
import { ZaloWebhookController } from './controllers/zalo-webhook/zalo-webhook.controller';
import { ZaloConsultationController } from './controllers/zalo-consultation.controller';
import { ZaloConversationController } from './controllers/zalo-conversation.controller';
import { ZaloRecentChatController } from './controllers/zalo-recent-chat.controller';
import { ZaloTransactionController } from './controllers/zalo-transaction.controller';
import { ZaloPromotionController } from './controllers/zalo-promotion.controller';
import { ZaloUnifiedMessageCampaignController } from './controllers/zalo-unified-message-campaign.controller';
import { ZaloZnsController } from './controllers/zalo-zns.controller';
import { ZaloZnsImageController } from './controllers/zalo-zns-image.controller';
import { ZaloZnsTemplateController } from './controllers/zalo-zns-template.controller';
import { ZaloZnsCampaignController } from './controllers/zalo-zns-campaign.controller';
import { ZaloSegmentController } from './controllers/zalo-segment.controller';
import { ZaloFollowerController } from './controllers/zalo-follower.controller';
import { ZaloCampaignController } from './controllers/zalo-campaign.controller';
import { ZaloAutomationController } from './controllers/zalo-automation.controller';
import { ZaloTagController } from './controllers/zalo-tag.controller';
import { ZaloTemplateController } from './controllers/zalo-template.controller';
import { ZaloIntegrationController } from './controllers/zalo-integration.controller';
import { ZaloOAuthController } from './controllers/zalo-oauth.controller';
import { EmailCampaignController } from './controllers/email-campaign.controller';
import { SmsTemplateController } from './controllers/sms-template.controller';
import { SmsCampaignController } from './controllers/sms-campaign.controller';
import { SmsServersController } from './controllers/sms-servers.controller';
import { ZaloOaMessageCampaignController } from './controllers/zalo-oa-message-campaign.controller';
import { ZaloAudienceSyncController } from './controllers/zalo-audience-sync.controller';
import { ZaloAudienceSyncSseController } from './controllers/zalo-audience-sync-sse.controller';
import { ZaloStatisticsController } from './controllers/zalo-statistics.controller';
import { ZaloGroupManagementController } from './controllers/zalo-group-management.controller';

import { ZaloGroupMessageController } from './controllers/zalo-group-message.controller';
import { ZaloGroupSyncController } from './controllers/zalo-group-sync.controller';
import { ZaloMessageSseController } from './controllers/zalo-message-sse.controller';
import { ZaloVideoUploadController } from './controllers/zalo-video-upload.controller';
import { ZaloArticleManagementController } from './controllers/zalo-article-management.controller';
import { ZaloArticleSyncController } from './controllers/zalo-article-sync.controller';
import { ZaloZnsQualityController } from './controllers/zalo-zns-quality.controller';
import { ZaloQRCodeSseController } from './controllers/zalo-qr-code-sse.controller';

// Import worker modules
import { FollowEventHandler } from '@modules/marketing/user/controllers/zalo-webhook/follow/follow-event.handler';
import { GroupEventHandler } from '@modules/marketing/user/controllers/zalo-webhook/group/group-event.handler';
import { ZnsEventHandler } from '@modules/marketing/user/controllers/zalo-webhook/zns/zns-event.handler';
import { MessageEventHandler } from '@modules/marketing/user/controllers/zalo-webhook/messages/message-event.handler';
import { UserInfoEventHandler } from '@modules/marketing/user/controllers/zalo-webhook/user-info/user-info-event.handler';

/**
 * Module quản lý marketing user
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      ...Object.values(entities),
      UsageLog, // ✅ ADDED: Entity cho CloudStorageHandler
      UserAddonUsage, // ✅ ADDED: Entity cho CloudStorageHandler
      SystemTemplateEmail, // ✅ ADDED: Entity cho SystemTemplateEmail
      UserConvertCustomer, // ✅ ADDED: Entity cho UserConvertCustomer segment
      UserOrder, // ✅ ADDED: Entity cho UserOrder demographics analysis
      Media, // ✅ ADDED: Entity cho Media để xử lý media_id
      Integration, // ✅ ADDED: Entity cho ZaloGroupSyncService
      IntegrationProvider, // ✅ ADDED: Entity cho ZaloGroupSyncService
      UserResource, // ✅ ADDED: Entity cho ZaloConversationExportService
    ]),
    BullModule.registerQueue({
      name: QueueName.EMAIL_MARKETING,
    }),
    BullModule.registerQueue({
      name: QueueName.SMS_MARKETING,
    }),
    BullModule.registerQueue({
      name: QueueName.ZALO_ZNS,
    }),
    BullModule.registerQueue({
      name: QueueName.ZALO_WEBHOOK,
    }),
    BullModule.registerQueue({
      name: QueueName.ZALO_AI_RESPONSE,
    }),
    forwardRef(() => IntegrationUserModule),
    // ❌ REMOVED: SubscriptionUserModule - Gây circular dependency
    ZaloModule,
    ZaloPersonalModule,
    SmsModule,
    EmailModule,
    ServicesModule, // Required for KeyPairEncryptionService
    HttpModule,
    EventEmitterModule.forRoot({
      wildcard: true,
      delimiter: '.',
      maxListeners: 20,
    }),
    ScheduleModule.forRoot(),
  ],
  controllers: [
    UserTagController,
    UserAudienceController,
    UserSegmentController,
    UserCampaignController,
    UserMarketingStatisticsController,
    UserTemplateEmailController,
    UserSystemTemplateEmailController,
    UserAudienceCustomFieldDefinitionController,
    UserAudienceCustomFieldController,
    MarketingOverviewController,
    ZaloController,
    ZaloPersonalController,
    ZaloBroadcastController,
    ZaloUnifiedMessageController,
    ZaloWebhookController,
    ZaloConsultationController,
    ZaloConversationController,
    ZaloRecentChatController,
    ZaloTransactionController,
    ZaloPromotionController,
    ZaloUnifiedMessageCampaignController,
    ZaloZnsController,
    ZaloZnsImageController,
    ZaloZnsTemplateController,
    ZaloZnsCampaignController,
    ZaloSegmentController,
    ZaloFollowerController,
    ZaloCampaignController,
    ZaloAutomationController,
    ZaloTagController,
    ZaloTemplateController,
    ZaloIntegrationController,
    ZaloOAuthController,
    EmailCampaignController,
    SmsTemplateController,
    SmsCampaignController,
    SmsServersController,
    ZaloOaMessageCampaignController,
    ZaloAudienceSyncController,
    ZaloAudienceSyncSseController,
    ZaloStatisticsController,
    ZaloGroupManagementController,
    ZaloContentController,
    ZaloGroupMessageController,
    ZaloGroupSyncController,
    ZaloMessageSseController,
    ZaloVideoUploadController,
    controllers.ZaloArticleController,
    controllers.ZaloArticleManagementController,
    ZaloArticleSyncController,
    UserTwilioSmsController,
    UserTwilioEmailController,
    controllers.ZaloUploadController,
    ZaloZnsQualityController,
    UserSegmentTagController,
    GmailMarketingController,
    ZaloQRCodeSseController,
  ],
  providers: [
    // ✅ ADDED: AddonHandlerFactory và handlers để UsageSecurityGuard hoạt động
    UserAddonUsageRepository, // ✅ ADDED: Repository cho CloudStorageHandler
    UsageLogRepository, // ✅ ADDED: Repository cho CloudStorageHandler
    ...ADDON_HANDLERS,
    AddonHandlerFactory,

    // Repositories
    UserTagRepository,
    UserSegmentRepository,
    UserAudienceRepository,
    UserAudienceCustomFieldRepository,
    UserAudienceCustomFieldDefinitionRepository,
    UserAudienceHasTagRepository,
    UserCampaignRepository,
    UserCampaignHistoryRepository,
    UserTemplateEmailRepository,
    UserTemplateSmsRepository,
    SmsCampaignUserRepository,
    SystemTemplateEmailRepository, // ✅ ADDED: Repository cho SystemTemplateEmail
    UserConvertCustomerRepository, // ✅ ADDED: Repository cho UserConvertCustomer segment
    UserOrderRepository, // ✅ ADDED: Repository cho UserOrder demographics analysis
    {
      provide: MediaRepository,
      useFactory: (dataSource: DataSource) => {
        return new MediaRepository(dataSource);
      },
      inject: [DataSource],
    }, // ✅ ADDED: Repository cho Media để xử lý media_id

    // Zalo repositories
    // repositories.ZaloOfficialAccountRepository, // Đã migrate sang Integration
    repositories.ZaloZnsTemplateRepository,
    repositories.ZaloZnsImageRepository,
    repositories.ZaloMessageRepository,
    repositories.ZaloZnsMessageRepository,
    repositories.ZaloZnsUserFeedbackRepository,
    repositories.ZaloZnsCampaignRepository,
    repositories.ZaloFollowerRepository,
    repositories.ZaloWebhookLogRepository,
    repositories.ZaloSegmentRepository,
    repositories.ZaloCampaignRepository,
    repositories.ZaloCampaignLogRepository,
    repositories.ZaloAutomationRepository,
    repositories.ZaloAutomationLogRepository,
    repositories.ZaloMessageTemplateRepository,
    repositories.ZaloOaMessageCampaignRepository,
    repositories.ZaloVideoUploadRepository,
    repositories.ZaloUploadRepository,

    // Zalo Personal repositories
    repositories.ZaloPersonalCampaignRepository,
    repositories.ZaloPersonalCampaignLogRepository,

    // Zalo Group & Content repositories
    repositories.ZaloGroupRepository,
    repositories.ZaloGroupMemberRepository,
    repositories.ZaloGroupMessageRepository,
    repositories.ZaloArticleRepository,

    // Zalo Ads repositories
    repositories.ZaloAdsAccountRepository,
    repositories.ZaloAdsCampaignRepository,
    repositories.ZaloAdsPerformanceRepository,

    // Services
    UserTagService,
    UserSegmentService,
    UserAudienceService,
    UserCampaignService,
    UserTemplateEmailService,
    UserSystemTemplateEmailService, // ✅ ADDED: Service cho SystemTemplateEmail
    UserMarketingStatisticsService,
    UserAudienceCustomFieldDefinitionService,
    UserAudienceCustomFieldService,
    UserSegmentTagService,
    CampaignDataTransformerService,
    MarketingOverviewService,

    // Zalo services
    ZaloApiClient,
    ZaloService,
    ZaloZnsService,
    ZaloZnsUserFeedbackService,
    ZaloZnsCampaignService,
    ZnsPersonalizationService,
    ZaloTokenService,
    ZaloZnsInfoService,
    ZaloOaMessageCampaignService,
    ZaloAudienceSyncService,
    ZaloUserSyncService,
    ZaloStatisticsService,

    // Zalo Personal services
    services.ZaloPersonalCampaignService,

    // Zalo Group & Content services
    ZaloGroupManagementService,
    ZaloGroupMessageService,
    ZaloArticleManagementService,
    ZaloGroupSyncService,

    // Email Marketing service
    EmailMarketingService,
    services.UserEmailCampaignStatusSyncService,
    services.EmailJobEventListenerService,

    // SMS services
    UserTemplateSmsService,
    SmsCampaignService,
    SmsServersService,
    UserTwilioSmsService,

    // Email services
    UserTwilioEmailService,

    // Gmail services
    GmailMarketingService,

    // Webhook event handlers
    MessageEventHandler,
    FollowEventHandler,
    GroupEventHandler,
    ZnsEventHandler,
    UserInfoEventHandler,

    // Webhook services
    ZaloEventRouterService,
    ZaloMessageSenderService,

    // Queue services
    ZaloAiResponseQueueService,
    ZaloWebhookQueueService,

    // SSE services
    ZaloMessageSseService,
    ZaloQRCodeSseService,

    // Export services
    ZaloConversationExportService,

    // Shared services
    RedisService,

    // Middleware
    ZaloUploadLoggerMiddleware,
  ],
  exports: [
    TypeOrmModule,
    // Repositories
    UserTagRepository,
    UserSegmentRepository,
    UserAudienceRepository,
    UserAudienceCustomFieldRepository,
    UserAudienceCustomFieldDefinitionRepository,
    UserAudienceHasTagRepository,
    UserCampaignRepository,
    UserCampaignHistoryRepository,
    UserTemplateEmailRepository,
    UserTemplateSmsRepository,
    SmsCampaignUserRepository,
    SystemTemplateEmailRepository, // ✅ ADDED: Repository cho SystemTemplateEmail

    // Zalo repositories
    // repositories.ZaloOfficialAccountRepository, // Đã migrate sang Integration
    repositories.ZaloZnsTemplateRepository,
    repositories.ZaloZnsImageRepository,
    repositories.ZaloMessageRepository,
    repositories.ZaloZnsMessageRepository,
    repositories.ZaloZnsUserFeedbackRepository,
    repositories.ZaloZnsCampaignRepository,
    repositories.ZaloFollowerRepository,
    repositories.ZaloWebhookLogRepository,
    repositories.ZaloSegmentRepository,
    repositories.ZaloCampaignRepository,
    repositories.ZaloCampaignLogRepository,
    repositories.ZaloAutomationRepository,
    repositories.ZaloAutomationLogRepository,
    repositories.ZaloMessageTemplateRepository,
    repositories.ZaloOaMessageCampaignRepository,

    // Zalo Group & Content repositories
    repositories.ZaloGroupRepository,
    repositories.ZaloGroupMemberRepository,
    repositories.ZaloGroupMessageRepository,

    // Zalo Ads repositories
    repositories.ZaloAdsAccountRepository,
    repositories.ZaloAdsCampaignRepository,
    repositories.ZaloAdsPerformanceRepository,

    // Services
    UserTagService,
    UserSegmentService,
    UserAudienceService,
    UserCampaignService,
    UserTemplateEmailService,
    UserSystemTemplateEmailService, // ✅ ADDED: Service cho SystemTemplateEmail
    UserMarketingStatisticsService,
    UserAudienceCustomFieldDefinitionService,
    UserSegmentTagService,
    CampaignDataTransformerService,

    // Zalo services
    ZaloService,
    ZaloZnsService,
    ZaloZnsUserFeedbackService,
    ZaloZnsCampaignService,
    ZaloTokenService,
    ZaloOaMessageCampaignService,
    ZaloAudienceSyncService,

    // Zalo Group & Content services
    ZaloGroupManagementService,
    ZaloGroupMessageService,
    ZaloArticleManagementService,
    ZaloGroupSyncService,

    // SMS services
    UserTwilioSmsService,

    // Email services
    UserTwilioEmailService,

    // Queue services
    ZaloAiResponseQueueService,

    // Legacy wrapper services - imported from IntegrationUserModule
    // ZaloOALegacyWrapperService, // Được inject từ IntegrationUserModule
  ],
})
export class MarketingUserModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(ZaloUploadLoggerMiddleware)
      .forRoutes('marketing/zalo/upload');
  }
}
