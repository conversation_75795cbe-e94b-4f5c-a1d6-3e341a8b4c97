import { ErrorCode } from '@common/exceptions/app.exception';
import { HttpStatus } from '@nestjs/common';
import { WORKFLOW_ERROR_CODES } from './workflow-error.code';

/**
 * Error codes cho Workflow Admin module
 * Range: 50101-50200
 * Kế thừa từ WORKFLOW_ERROR_CODES và bổ sung thêm các mã lỗi đặc thù cho admin
 */
export const WORKFLOW_ADMIN_ERROR_CODES = {
  ...WORKFLOW_ERROR_CODES,

  // ========== ADMIN SPECIFIC ERRORS (50101-50120) ==========
  /**
   * Lỗi khi admin không có quyền chỉnh sửa workflow của admin khác
   */
  ADMIN_WORKFLOW_EDIT_PERMISSION_DENIED: new ErrorCode(
    50101,
    'Admin không có quyền chỉnh sửa workflow của admin khác',
    HttpStatus.FORBIDDEN,
  ),

  /**
   * Lỗi khi admin không có quyền xóa workflow của admin khác
   */
  ADMIN_WORKFLOW_DELETE_PERMISSION_DENIED: new ErrorCode(
    50102,
    'Admin không có quyền xóa workflow của admin khác',
    HttpStatus.FORBIDDEN,
  ),

  /**
   * Lỗi khi admin gán workflow cho user không hợp lệ
   */
  ADMIN_WORKFLOW_INVALID_USER_ASSIGNMENT: new ErrorCode(
    50103,
    'Không thể gán workflow cho user không tồn tại',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi employee ID không hợp lệ
   */
  ADMIN_WORKFLOW_INVALID_EMPLOYEE_ID: new ErrorCode(
    50104,
    'Employee ID không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi admin bulk delete có lỗi ownership
   */
  ADMIN_WORKFLOW_BULK_DELETE_OWNERSHIP_ERROR: new ErrorCode(
    50121,
    'Không thể xóa workflow không thuộc về admin hiện tại',
    HttpStatus.FORBIDDEN,
  ),

  /**
   * Lỗi khi admin bulk operation thất bại một phần
   */
  ADMIN_WORKFLOW_BULK_OPERATION_PARTIAL_FAILURE: new ErrorCode(
    50122,
    'Một số workflow không thể được xử lý',
    HttpStatus.PARTIAL_CONTENT,
  ),

  /**
   * Lỗi khi thực thi workflow thất bại (Admin)
   */
  WORKFLOW_EXECUTION_ERROR: new ErrorCode(
    50123,
    'Lỗi khi thực thi workflow',
    HttpStatus.BAD_REQUEST,
  ),
} as const;

/**
 * Type helper để đảm bảo type safety khi sử dụng error codes
 */
export type WorkflowErrorCode = typeof WORKFLOW_ERROR_CODES[keyof typeof WORKFLOW_ERROR_CODES];
export type WorkflowAdminErrorCode = typeof WORKFLOW_ADMIN_ERROR_CODES[keyof typeof WORKFLOW_ADMIN_ERROR_CODES];
