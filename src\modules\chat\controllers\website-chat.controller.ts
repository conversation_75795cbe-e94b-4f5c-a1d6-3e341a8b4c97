import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  Query,
  UseGuards,
  ParseUUI<PERSON>ipe,
  HttpCode,
  HttpStatus,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Lo<PERSON>,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBody,
  ApiExtraModels,
  ApiHeader,
} from '@nestjs/swagger';
import { Request, Response } from 'express';
import { WebsiteKeyGuard } from '../guards/website-key.guard';
import { ApiResponseDto } from '@common/response/api-response-dto';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { PayloadLiveChatKey } from '@modules/integration/interfaces/website.interface';
import { WebsiteVisitor } from '../decorators';
import { MessageRole } from '@/shared';
import { MessageContentType } from '../dto';

// Import DTOs
import {
  ExternalMessageRequestDto,
  ExternalMessageRequestExamples,
  ExternalMessageWithAttachmentsDto,
} from '../dto';

// Import services
import { WebsiteChatService } from '../services/website-chat.service';

/**
 * Website Chat Controller
 * Handles conversation thread management for website visitors using encrypted key authentication
 * Route: /website/chat
 */
@ApiTags('Website Chat')
@Controller('website/chat')
@UseGuards(WebsiteKeyGuard)
@ApiHeader({
  name: 'x-website-key',
  description: 'Encrypted website key (format: redai_<encrypted_payload>)',
  required: true,
  example: 'redai_eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
})
@ApiExtraModels(ExternalMessageWithAttachmentsDto)
export class WebsiteChatController {
  private readonly logger = new Logger(WebsiteChatController.name);

  constructor(
    private readonly websiteChatService: WebsiteChatService,
  ) {}

  /**
   * Send a message in a website conversation thread
   */
  @Post('threads/:id/messages')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Send message in website chat',
    description: `Send a new message in a website chat conversation. Supported content types:
    - Text: Regular text messages
    - Image: Image attachments with fileId and metadata
    - File: File attachments with fileId and metadata
    - Reply: Reply to specific messages
    
    Note: Tool call decisions, agent selection, and message editing are not supported for website chat.
    Each message creates a new conversation entry - no modification of existing messages is allowed.`,
  })
  @ApiParam({
    name: 'id',
    description: 'ID of the external platform data (thread equivalent for website)',
    type: String,
    example: '0a983b62-cdd0-4ebe-9d9d-b259f97194ac',
  })
  @ApiBody({
    type: ExternalMessageRequestDto,
    description: 'Message data to send (external format without tool calls or agent selection)',
    examples: {
      'Simple Text Message': ExternalMessageRequestExamples['Simple Text Message'],
      'Reply Message': ExternalMessageRequestExamples['Reply Message'],
      'Message with Attachments': ExternalMessageRequestExamples['Message with Attachments'],
    },
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Message sent successfully',
    schema: ApiResponseDto.getSchema(ExternalMessageWithAttachmentsDto),
  })
  @ApiErrorResponse()
  async sendMessage(
    @Param('id', ParseUUIDPipe) threadId: string,
    @Body() messageRequest: ExternalMessageRequestDto,
    @WebsiteVisitor() websitePayload: PayloadLiveChatKey,
  ): Promise<ApiResponseDto<ExternalMessageWithAttachmentsDto>> {
    this.logger.log('Processing website chat message', {
      threadId,
      contentType: messageRequest.contentBlocks.type,
    });

    // Process the message using the website chat service
    const result = await this.websiteChatService.processMessage({
      messageRequest,
      websitePayload,
      threadId,
    });

    // Create response DTO with basic message info
    const response = new ExternalMessageWithAttachmentsDto({
      messageId: result.messageId,
      messageText: messageRequest.contentBlocks.text || '',
      messageCreatedAt: Date.now(),
      hasAttachments: messageRequest.contentBlocks.type === MessageContentType.ATTACHMENT,
      role: MessageRole.USER,
      attachments: undefined, // Attachments will be populated by getMessages endpoint
      runId: result.runId,
    });

    this.logger.log('Website chat message processed successfully', {
      threadId,
      messageId: result.messageId,
      runId: result.runId,
      websiteId: websitePayload.websiteId,
    });

    return ApiResponseDto.created(response, 'Website message sent successfully');
  }

  /**
   * Cancel active processing for a website conversation thread
   */
  @Post('threads/:id/cancel')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Cancel active processing',
    description: 'Cancel ongoing AI processing for a website chat conversation',
  })
  @ApiParam({
    name: 'id',
    description: 'ID of the external platform data (thread equivalent for website)',
    type: 'string',
    format: 'uuid',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Cancellation processed successfully',
    schema: ApiResponseDto.getSchema({
      success: { type: 'boolean' },
      message: { type: 'string' },
    }),
  })
  @ApiErrorResponse()
  async cancelRun(
    @Param('id', ParseUUIDPipe) threadId: string,
    @WebsiteVisitor() websitePayload: PayloadLiveChatKey,
  ): Promise<ApiResponseDto<{ success: boolean; message: string }>> {
    this.logger.log('Cancelling website chat processing', { threadId });

    // For now, return a simple response indicating cancellation is not implemented
    // This can be implemented later if needed for website chat
    const result = {
      success: false,
      message: 'Cancellation not implemented for website chat',
    };

    this.logger.log('Website chat cancellation response', {
      threadId,
      success: result.success,
      websiteId: websitePayload.websiteId,
    });

    return ApiResponseDto.success(
      result,
      result.success
        ? 'Processing cancelled successfully'
        : 'No active processing found or cancellation not supported',
    );
  }

  /**
   * Stream chat events via Server-Sent Events for website chat
   */
  @Get('threads/:id/stream/:runId')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Stream website chat events via Server-Sent Events',
    description:
      'Establishes an SSE connection to stream real-time chat events for a specific website thread and run.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID of the external platform data (thread equivalent for website)',
    type: 'string',
    format: 'uuid',
  })
  @ApiParam({
    name: 'runId',
    description: 'ID of the run to stream events for',
    type: 'string',
    format: 'uuid',
  })
  @ApiQuery({
    name: 'from',
    description: 'Optional message ID to resume from (Last-Event-ID)',
    required: false,
    type: 'string',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'SSE stream established successfully',
    headers: {
      'Content-Type': { description: 'text/event-stream' },
      'Cache-Control': { description: 'no-cache' },
      Connection: { description: 'keep-alive' },
      'Access-Control-Allow-Origin': { description: '*' },
    },
  })
  @ApiErrorResponse()
  async streamEvents(
    @Param('id', ParseUUIDPipe) threadId: string,
    @Param('runId', ParseUUIDPipe) runId: string,
    @WebsiteVisitor() websitePayload: PayloadLiveChatKey,
    @Req() req: Request,
    @Res() res: Response,
    @Query('from') fromMessageId?: string,
  ): Promise<void> {
    try {
      this.logger.log('Starting website chat SSE stream', {
        threadId,
        runId,
        fromMessageId,
      });

      // Handle client disconnect
      req.on('close', () => {
        this.logger.log(`🔌 Website client disconnected from thread ${threadId}`, {
          websiteId: websitePayload.websiteId,
          runId,
        });
      });

      req.on('error', (error) => {
        this.logger.error(`🔥 Website SSE request error for thread ${threadId}:`, {
          error: error.message,
          websiteId: websitePayload.websiteId,
          runId,
        });
      });

      // Delegate to website chat service
      await this.websiteChatService.streamChatEvents(
        res,
        threadId,
        runId,
        websitePayload,
        fromMessageId,
      );

      this.logger.log('Website chat SSE stream completed', {
        threadId,
        runId,
        websiteId: websitePayload.websiteId,
      });

    } catch (error) {
      this.logger.error(`💥 Website chat SSE controller error for thread ${threadId}:`, {
        error: error.message,
        runId,
        stack: error.stack,
      });

      // Send error response if headers haven't been sent yet
      if (!res.headersSent && !res.writableEnded) {
        try {
          res.status(500).json({
            error: 'Internal server error',
            message: 'Failed to establish website chat SSE stream',
            threadId,
            runId,
          });
        } catch (responseError) {
          this.logger.error(
            `Failed to send error response for website thread ${threadId}:`,
            responseError,
          );
        }
      }
    }
  }
}