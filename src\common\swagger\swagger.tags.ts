/**
 * Constant object for Swagger API tags
 * Used to ensure consistency in API documentation
 */
export const SwaggerApiTag = {
  USERS: 'Users',
  LOCATION: 'Location',
  COMMON: 'Common',
  ADMIN_USERS: 'Admin - Users',
  EMPLOYEES: 'Employees',
  TESTS: 'Tests',
  ADMIN_TESTS: 'Admin - Tests',
  ADMIN_FILES: 'Admin - Files',
  SETTINGS: 'Settings',
  ADMIN_SETTINGS: 'Admin - Settings',
  RESOURCES: 'Resources',
  BLOGS: 'Blogs',
  BLOG_COMMENTS: 'Blog Comments',
  BLOG_PURCHASES: 'Blog Purchases',
  ADMIN_BLOGS: 'Admin - Blogs',
  ADMIN_MEDIA: 'Admin - Media',
  ADMIN_URL: 'Admin - URL',
  MODEL_TRAINING: 'Model Training',
  ADMIN_MODEL_TRAINING: 'Admin - Model Training',
  USER_SUBSCRIPTIONS: 'User - Subscriptions',
  ADMIN_SUBSCRIPTION_PLAN: 'Admin - Subscription Plans',
  ADMIN_SUBSCRIPTION_PLAN_PRICING: 'Admin - Subscription Plan Pricing',
  ADMIN_SUBSCRIPTION: 'Admin - Subscriptions',
  ADMIN_SUBSCRIPTION_ORDER: 'Admin - Subscription Orders',
  ADMIN_SUBSCRIPTION_PAYMENT: 'Admin - Subscription Payments',
  USER_STRATEGY: 'User Strategy',
  ADMIN_STRATEGY: 'Admin - Strategy',
  INTEGRATION: 'Integration',
  INTEGRATION_SMS: 'Integration - SMS',
  INTEGRATION_ADMIN: 'Admin - Integration',
  USER_KNOWLEDGE_FILES: 'User - Knowledge Files',
  USER_VECTOR_STORE: 'User - Vector Store',
  ADMIN_KNOWLEDGE_FILES: 'Admin - Knowledge Files',
  BANKS: 'Banks',
  USER_BANKS: 'User Banks',
  USER_AUDIENCE: 'User - Audience',
  USER_CAMPAIGN: 'User - Campaign',
  USER_EMAIL_CAMPAIGN: 'User - Email Campaign',
  USER_TEMPLATE_EMAIL: 'User - Template Email',
  ADMIN_TEMPLATE_SMS: 'Admin - Template SMS',
  USER_SEGMENT: 'User - Segment',
  USER_TAG: 'User - Tag',
  USER_SMS_CAMPAIGN: 'User - SMS Campaign',
  USER_ACCOUNT: 'User - Account',
  USER_AFFILIATE_STATISTICS: 'User - Affiliate Statistics',
  USER_AFFILIATE_ACCOUNT: 'User - Affiliate Account',
  USER_AFFILIATE_ORDER: 'User - Affiliate Order',
  USER_AFFILIATE_CUSTOMER: 'User - Affiliate Customer',
  USER_AFFILIATE_POINT_CONVERSION: 'User - Affiliate Point Conversion',
  USER_AFFILIATE_REFERRAL_LINK: 'User - Affiliate Referral Link',
  ADMIN_AFFILIATE_ACCOUNT: 'Admin - Affiliate Account',
  ADMIN_AFFILIATE_ORDER: 'Admin - Affiliate Order',
  ADMIN_AFFILIATE_WITHDRAWAL: 'Admin - Affiliate Withdrawal',
  ADMIN_AFFILIATE_CUSTOMER: 'Admin - Affiliate Customer',
  ADMIN_AFFILIATE_OVERVIEW: 'Admin - Affiliate Overview',
  ADMIN_AFFILIATE_RANK: 'Admin - Affiliate Rank',
  USER_MARKETING_STATISTICS: 'User - Marketing Statistics',
  USER_AFFILIATE_WITHDRAWAL: 'User - Affiliate Withdrawal',
  USER_AFFILIATE_UPLOAD: 'User - Affiliate Upload',
  USER_AFFILIATE_BUSINESS: 'User - Affiliate Business',
  USER_AFFILIATE_REGISTRATION: 'User - Affiliate Registration',
  ZALO_OA: 'User - Zalo OA',
  ZALO_ZNS: 'User - Zalo ZNS',
  ZALO_ZNS_CAMPAIGN: 'User - Zalo ZNS Campaign',
  ZALO_TAG: 'User - Zalo Tag',
  ZALO_SEGMENT: 'User - Zalo Segment',
  ZALO_SEGMENTS: 'User - Zalo Segments',
  ZALO_FOLLOWERS: 'User - Zalo Followers',
  ZALO_CAMPAIGN: 'User - Zalo Campaign',
  ZALO_OA_MESSAGE_CAMPAIGN: 'User - Zalo OA Message Campaign',
  ZALO_AUTOMATION: 'User - Zalo Automation',
  ZALO_INTEGRATION: 'User - Zalo Integration',
  ZALO_AUDIENCE_SYNC: 'User - Zalo Audience Sync',
  ZALO_STATISTICS: 'User - Zalo Statistics',
  ZALO_CONSULTATION: 'User - Zalo Consultation',
  ZALO_CONTENT_MANAGEMENT: 'User - Zalo Content Management',
  ZALO_CONTENT: 'User - Zalo Content',
  ZALO_PROMOTION: 'User - Zalo Promotion',
  ZALO_TEMPLATE: 'User - Zalo Template',
  ZALO_TRANSACTION: 'User - Zalo Transaction',
  USER_EMAIL_TRACKING: 'User - Email Tracking',
  ADMIN_AFFILIATE_CONTRACT: 'Admin - Affiliate Contract',
  EMAIL: 'Email',
  DATA_STATISTICS: 'Data - Statistics',

  // ADMIN
  ADMIN_USER: 'Admin - User',
  ADMIN_SEGMENT: 'Admin - Segment',
  ADMIN_TAG: 'Admin - Tag',
  ADMIN_AUDIENCE: 'Admin - Audience',
  ADMIN_TEMPLATE_EMAIL: 'Admin - Template Email',
  ADMIN_CAMPAIGN: 'Admin - Campaign',
  USER_TYPE_AGENT: 'User - Type Agent',
  ADMIN_TYPE_AGENT: 'Admin - Type Agent',
  ADMIN_AFFILIATE: 'Admin - Affiliate',
  ADMIN_AFFILIATE_CLICK: 'Admin - Affiliate Click',
  ADMIN_AFFILIATE_POINT_CONVERSION: 'Admin - Affiliate Point Conversion',
  USER_TOOL: 'User - Tool',
  ADMIN_TOOL: 'Admin - Tool',
  ADMIN_AGENT_SYSTEM: 'Admin - Agent System',
  ADMIN_AGENT_BASE: 'Admin - Agent Base',
  ADMIN_AGENT_TEMPLATE: 'Admin - Agent Template',
  ADMIN_AGENT_RESOURCES: 'Admin - Agent Resources',
  ADMIN_AGENT_ROLE: 'Admin - Agent Role',
  USER_AGENT: 'User - Agent',
  USER_AGENT_RESOURCES: 'User - Agent Resources',
  ADMIN_AGENT_PERMISSION: 'Admin - Agent Permission',
  ADMIN_BUSINESS: 'Admin - Business',
  ADMIN_BUSINESS_WAREHOUSE: 'Admin - Business - Warehouse',
  ADMIN_BUSINESS_FILE: 'Admin - Business - File',
  ADMIN_BUSINESS_FOLDER: 'Admin - Business - Folder',
  // Marketplace tags
  USER_MARKETPLACE_PRODUCTS: 'Marketplace - Products',
  USER_MARKETPLACE_CART: 'Marketplace - Cart',
  USER_MARKETPLACE_ORDERS: 'Marketplace - Orders',
  MARKETPLACE_FLASH_SALE: 'Marketplace - Flash Sale',
  ADMIN_MARKETPLACE_PRODUCTS: 'Admin - Marketplace Products',
  ADMIN_MARKETPLACE_CART: 'Admin - Marketplace Cart',
  ADMIN_MARKETPLACE_ORDERS: 'Admin - Marketplace Orders',
  ADMIN_MARKETPLACE_FLASH_SALE: 'Admin - Marketplace Flash Sale',
  ADMIN_MARKETPLACE_ANALYTICS: 'Admin - Marketplace Analytics',
  // Business tags
  USER_BUSINESS: 'User - Business',
  USER_BUSINESS_PRODUCT: 'User - Business Products',
  ADMIN_GROUP_TOOL: 'Admin - Group Tool',
  ADMIN_VERSION_TOOL: 'Admin - Version Tool',
  USER_VERSION_TOOL: 'User - Version Tool',
  USER_GROUP_TOOL: 'User - Group Tool',
  USER_ORDER: 'User - Order',
  USER_CONVERT: 'User - Convert',
  USER_CONVERT_CUSTOMER: 'User - Convert Customer',
  USER_BUSINESS_REPORT: 'User - Business Report',
  USER_ADDRESS: 'User - Address',
  // Warehouse
  USER_WAREHOUSE_PHYSICAL: 'User Physical Warehouse',
  USER_WAREHOUSE_VIRTUAL: 'User Virtual Warehouse',
  USER_WAREHOUSE_VIRTUAL_FILES: 'User Virtual Warehouse Files',
  USER_WAREHOUSE_VIRTUAL_FOLDERS: 'User Virtual Warehouse Folders',
  USER_WAREHOUSE: 'User Warehouses',
  USER_WAREHOUSE_CUSTOM_FIELD: 'User Warehouse Custom Field',
  // Marketing
  USER_TOOL_INTEGRATION: 'User - Tool Integration',
  MARKETING_ADMIN: 'Admin - Marketing',
  MARKETING_USER: 'User - Marketing',
  ADMIN_INTEGRATION_SMS_TEST: 'Admin - Integration - SMS Test',
  USER_URL: 'User - URL',
  AUTH: 'Authentication',
  USER_MARKETPLACE_PAYMENT: 'User - Marketplace Payment',
  R_POINT_ADMIN_COUPONS: 'R-Point - Admin Coupons',
  R_POINT_ADMIN_POINTS: 'R-Point - Admin Points',
  ADMIN_PROVIDER_MODEL: 'Admin - Provider Model',
  USER_PROVIDER_MODEL: 'User - Provider Model',
  PAYMENT_R_POINT_USER: 'Payment - R-Point - User',
  ADMIN_AGENT_RANK: 'Admin - Agent Rank',
  ADMIN_TASK: 'Admin - Task',
  USER_TASK: 'User - Task',
  ADMIN_AGENT: 'Admin - Agent User',
  EMPLOYEE_ROLES: 'Employee - Roles',
  EMPLOYEE_PERMISSIONS: 'Employee - Permissions',
  // INTEGRATION
  ADMIN_INTEGRATION: 'Admin - Integration',
  USER_INTEGRATION_FACEBOOK: 'User - Integration Facebook',
  USER_INTEGRATION_WEBSITE: 'User - Integration Website',
  USER_MEDIA: 'User - Media',
  ADMIN_RULE_CONTRACT: 'Admin - Rule Contract',

  USER_TASK_EXECUTION: 'User - Task Execution',
  USER_TASK_STEP: 'User - Task Step',
  USER_TASK_STEP_CONNECTION: 'User - Task Step Connection',
  // MODEL TRAINING
  USER_BASE_MODEL: 'User - Base Model',
  ADMIN_BASE_MODEL: 'Admin - Base Model',
  USER_FINETUNING: 'User - Finetuning',
  ADMIN_FINETUNING: 'Admin - Finetuning',
  USER_FINETUNING_DATA: 'User - Finetuning Data',
  ADMIN_FINETUNING_DATA: 'Admin - Finetuning Data',
  ADMIN_FINETUNING_JOB: 'Admin - Finetuning Job',
  ADMIN_MODEL_FINE_TUNING: 'Admin - Model Finetuning ',
  ADMIN_BASE_MODEL_TRASH: 'Admin - Base Model Trash',
  USER_INTEGRATION_SHIPMENT: 'User - Integration Shipment',
  ADDRESS: 'Address',
  USER_LOCATION: 'User - Location',
  GHN_ADDRESS: 'GHN - Address',
  GHN_SHIPMENT: 'GHN - Shipment',
  GHTK_SHIPMENT: 'GHTK - Shipment',
  USER_INVENTORY: 'User - Inventory',
  USER_ORDER_TRACKING: 'User - Order Tracking',
  USER_SHOP_ADDRESS: 'User - Shop Address',
  USER_PHYSICAL_WAREHOUSE: 'User - Physical Warehouse',
  USER_MARKETING: 'User - Marketing',
  ADMIN_API_KEY_MODEL: 'Admin - API Key Model',
  ADMIN_MODEL_REGISTRY: 'Admin - Model Registry',
  USER_API_KEY_MODEL: 'User - API Key Model',
  USER_MODEL_FINE_TUNING: 'User - Model Finetuning',
  ADMIN_SYSTEM_MODELS: 'Admin - System Models',
  ADMIN_MODEL_PERFORMANCE: 'Admin - Model Performance',
  ADMIN_AGENT_MCP_SYSTEM: 'Admin - Agent MCP System',
  USER_FINETUNING_JOB: 'User - Finetuning Job',
  ADMIN_AGENT_STRATEGY: 'Admin - Agent Strategy',
  USER_AGENT_STRATEGY: 'User - Agent Strategy',
  USER_MARKETING_CUSTOM_FIELD: 'User - Marketing Custom Field',
  USER_MARKETING_CUSTOM_FIELD_DEFINITION:
    'User - Marketing Custom Field Definition',
  ADMIN_MARKETING_CUSTOM_FIELD: 'Admin - Marketing Custom Field',
  R_POINT_USER: 'R-Point - User',
  USER_BUSINESS_ENTITY_HAS_MEDIA: 'User - Business Entity Has Media',
  USER_BUSINESS_CUSTOMER_PRODUCT: 'User - Business Customer Product',
  USER_BUSINESS__SIMPLE_CUSTOMER_PRODUCT:
    'User - Business Simple Customer Product',
  USER_BUSINESS_VARIANT_PRODUCT: 'User - Business Variant Product',
  USER_BUSINESS_PHYSICAL_PRODUCT: 'User - Business Physical Product',
  USER_INTEGRATION: 'User - Integration',
  USER_INTEGRATION_TWILIO_SMS: 'User - Integration - Twilio SMS',
  ZALO_OAUTH_INTEGRATION: 'Zalo OAuth Integration',
  ZALO_WEBHOOK: 'Zalo Webhook',
  USER_DEVICE_INFO: 'User - Device Info',
  USER_AUTH_VERIFICATION_LOG: 'User - Auth Verification Log',
  PAYMENT_INTEGRATION: 'Payment - Integration',
  USER_AGENT_MEMORIES: 'User - Agent Memories',
  USER_AGENT_KNOWLEDGE_FILES: 'User - Agent Knowledge Files',
  USER_MEMORIES: 'User - Memories',
  ADMIN_AGENT_MEMORIES: 'Admin - Agent Memories',
  USER_FINETUNING_HISTORY: 'User - Finetuning History',
  ADMIN_USER_ANALYTICS: 'Admin - User Analytics',
  ADMIN_SYSTEM_CONFIGURATION: 'Admin - System Configuration',
  USER_AGENT_MCP: 'User - Agent MCP',
  USER_MCP: 'User - MCP',
  USER_SMS_TEMPLATE: 'User - SMS Template',
  USER_SMS_SERVERS: 'User - SMS Servers',
  ZALO_GROUP_MANAGEMENT: 'Zalo Group Management',
  ZALO_GROUP_MESSAGE: 'Zalo Group Message',
  ZALO_ARTICLE: 'Zalo Article',
  INTEGRATION_GMAIL: 'Integration - Gmail',
  MARKETING_GMAIL: 'Marketing - Gmail',
  ZALO_VIDEO_UPLOAD: 'Zalo Video Upload',
  ZALO_UPLOAD: 'Zalo Upload',
  USER_HELP_CENTER: 'User - Help Center',
  ADMIN_HELP_CENTER: 'Admin - Help Center',
  USER_SUBSCRIPTION_PAYMENT: 'User - Subscription Payment',
  ADMIN_AFFILIATE_REGISTRATION: 'Admin - Affiliate Registration',
  PAYMENT_MB_INTEGRATION: 'Payment - MB Integration',
  PAYMENT_KLB_INTEGRATION: 'Payment - KLB Integration',
  PAYMENT_OCB_INTEGRATION: 'Payment - OCB Integration',
  PAYMENT_ACB_INTEGRATION: 'Payment - ACB Integration',
  USER_RULE_CONTRACT: 'User - Rule Contract',
  ADMIN_EMAIL_CAMPAIGN: 'Admin - Email Campaign',
  ADMIN_MCP_SYSTEM: 'Admin - MCP System',
  USER_INTEGRATION_FPT_SMS_BRAND_NAME:
    'User - Integration - FPT SMS Brand Name',
  USER_DEVICE_SESSION: 'User - Device Session',
  ADMIN_DEVICE_SESSION: 'Admin - Device Session',
  USER_WORKFLOW: 'User - Workflow',
  ADMIN_WORKFLOW: 'Admin - Workflow',
  WORKFLOW: 'Workflow',
  EMPLOYEE_AUTH: 'Employee - Auth',
  USER_BUSINESS_DIGITAL_PRODUCT: 'User - Business Digital Product',
  USER_BUSINESS_SERVICE_PRODUCT: 'User - Business Service Product',
  USER_BUSINESS_EVENT_PRODUCT: 'User - Business Event Product',
  USER_BUSINESS_COMBO_PRODUCT: 'User - Business Combo Product',
  ZALO_AUDIENCE_SYNC_SSE: 'Zalo Audience Sync SSE',
  ADMIN_ZALO: 'Admin - Zalo',
  R_POINT_WEBHOOK_USER: 'Webhook User',
  ADMIN_SUBSCRIPTION_ADDON: 'Admin - Subscription Addon',
  AHAMOVE_WEBHOOK: 'Ahamove Webhook',
  USER_MODELS: 'User - Models',
  ADMIN_MODELS: 'Admin - Models',
  INTEGRATION_EMAIL_SERVER: 'Integration - Email Server',
  ADMIN_INTEGRATION_EMAIL_SMTP: 'Admin - Integration - Email SMTP',
  ADMIN_SYSTEM_TEMPLATE_EMAIL: 'Admin - System Template Email',
  USER_SYSTEM_TEMPLATE_EMAIL: 'User - System Template Email',
  ADMIN_MARKETING_CUSTOM_FIELD_DEFINITION:
    'Admin - Marketing Custom Field Definition',
  MARKETING_TWILIO_EMAIL_USER: 'User - Marketing - Twilio Email',
  MARKETING_TWILIO_SMS_USER: 'User - Marketing - Twilio SMS',
  USER_ADDON_USAGE_SUBSCRIPTIONS: 'User - Addon Usage Subscriptions',
  ADMIN_SMS_CAMPAIGN: 'Admin - SMS Campaign',
  ZALO_ZNS_IMAGE: 'User - Zalo ZNS Image',
  ADMIN_AGENT_SUPERVISOR: 'Admin - Agent Supervisor',
  ZALO_ZNS_TEMPLATE: 'User - Zalo ZNS Template',
  ZALO_ZNS_QUALITY: 'User - Zalo ZNS Quality',
  ZALO_CONVERSATION: 'User - Zalo Conversation',
  ZALO_MESSAGE_SSE: 'User - Zalo Message SSE',
  ZALO_QR_CODE_SSE: 'User - Zalo QR Code SSE',
  ZALO_UNIFIED_CAMPAIGN: 'User - Zalo Unified Campaign',
  ZALO_UNIFIED_MESSAGE: 'User - Zalo Unified Message',
  ZALO_BROADCAST: 'User - Zalo Broadcast Message',
  DASHBOARD_WIDGET: 'Dashboard Widget',
  DASHBOARD_PAGE: 'Dashboard Page',
  USER_SUBSCRIPTIONS_USAGE: 'User - Subscriptions Usage',
  ADMIN_WEBHOOK_EVENTS: 'Admin - Webhook Events',
  ROUTER: 'Page - Router',
  ROUTER_SSE: 'Page - Router SSE',
  ADMIN_INTEGRATION_SMS_SERVER: 'Admin - Integration - SMS Server',
  USER_SIGNATURE: 'User - Signature',
  ADMIN_SIGNATURE: 'Admin - Signature',
  I18N_EXCEPTION_EXAMPLE: 'I18n Exception Example',
  APP_EXCEPTION_DEMO: 'App Exception Demo',
  USER_CONVERT_CUSTOMER_MERGE_RECOMMENDATION:
    'User - Convert Customer Merge Recommendation',
  GOOGLE_PICKER: 'Google Picker',
  MEDIA_TRACKING: 'Rag - Tracking',
  URL_TRACKING: 'URL - Tracking',
  INTEGRATION_GOOGLE_CALENDAR: 'Integration - Google Calendar',
  CALENDAR_REMINDERS: 'Calendar Reminders',
  CALENDAR_TASKS: 'Calendar Tasks',
  CALENDAR_REPORTS: 'Calendar Reports',
  CALENDAR_EVENTS: 'Calendar Events',
  CALENDAR_ATTENDEES: 'Calendar Attendees',
  CALENDAR_RESOURCES: 'Calendar Resources',
  CALENDAR_RECURRANCES: 'Calendar Recurrances',
  ZALO_PERSONAL: 'User - Zalo Personal',
  // Analytics tags
  BUSINESS_ANALYTICS_DASHBOARD: 'Business Analytics - Dashboard',
  ADMIN_ANALYTICS_DASHBOARD: 'Admin Analytics - Dashboard',
  BUSINESS_ANALYTICS_SALES: 'Business Analytics - Sales',
  ADMIN_ANALYTICS_SALES: 'Admin Analytics - Sales',
  ZALO_CAMPAIGN_OA: 'User - Zalo Campaign OA',
  GOOGLE_SHEETS_INTEGRATION: 'User - Google Sheets Integration',
  USER_INTEGRATION_FACEBOOK_ADS: 'User - Integration - Facebook Ads',
  ZALO_CAMPAIGN_PERSONAL: 'User - Zalo Campaign Personal',
  RPOINT_DASHBOARD: 'R-Point - Dashboard',
  R_POINT_ADMIN_DASHBOARD: 'R-Point - Admin Dashboard',
} as const;

export const SWAGGER_API_TAGS = SwaggerApiTag;
