import {
  Controller,
  Get,
  Patch,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
  ApiBody,
} from '@nestjs/swagger';
import { NodeDefinitionAdminService } from '../services';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { ApiResponseDto, PaginatedResult } from '@common/response/api-response-dto';
import {
  QueryNodeDefinitionDto,
  NodeDefinitionResponseDto,
  NodeGroupResponseDto,
  UpdateNodeDefinitionDto,
} from '../../dto';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { ApiErrorResponseDto } from '@common/dto/api-error-response.dto';
import { WORKFLOW_ADMIN_ERROR_CODES } from '../../exceptions/workflow.exception';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { PermissionsGuard } from '@/modules/auth/guards/permissions.guard';

/**
 * Controller xử lý các API liên quan đến node definition cho admin
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_WORKFLOW)
@ApiExtraModels(
  ApiResponseDto,
  NodeDefinitionResponseDto,
  NodeGroupResponseDto,
  QueryNodeDefinitionDto,
  UpdateNodeDefinitionDto,
  PaginatedResult,
  ApiErrorResponseDto,
)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard, PermissionsGuard)
@Controller('admin/node-definition')
export class NodeDefinitionAdminController {
  constructor(private readonly nodeDefinitionAdminService: NodeDefinitionAdminService) {}

  /**
   * Lấy danh sách node definitions với phân trang và filter (admin có thể xem tất cả)
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách node definitions với phân trang và filter' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách node definitions',
    schema: ApiResponseDto.getPaginatedSchema(NodeDefinitionResponseDto),
  })
  @ApiErrorResponse(
    WORKFLOW_ADMIN_ERROR_CODES.NODE_DEFINITION_FETCH_ERROR,
    WORKFLOW_ADMIN_ERROR_CODES.WORKFLOW_INVALID_INPUT,
  )
  async getNodeDefinitions(
    @CurrentEmployee('id') employeeId: number,
    @Query() queryDto: QueryNodeDefinitionDto,
  ): Promise<ApiResponseDto<PaginatedResult<NodeDefinitionResponseDto>>> {
    const result = await this.nodeDefinitionAdminService.getNodeDefinitions(employeeId, queryDto);
    return ApiResponseDto.success(result, 'Lấy danh sách node definitions thành công');
  }

  /**
   * Lấy danh sách unique group names
   */
  @Get('groups')
  @ApiOperation({ summary: 'Lấy danh sách unique group names' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách unique groups',
    schema: {
      allOf: [
        { $ref: '#/components/schemas/ApiResponseDto' },
        {
          properties: {
            result: {
              type: 'array',
              items: { $ref: '#/components/schemas/NodeGroupResponseDto' }
            }
          }
        }
      ]
    }
  })
  @ApiErrorResponse(
    WORKFLOW_ADMIN_ERROR_CODES.NODE_DEFINITION_GROUPS_FETCH_ERROR,
    WORKFLOW_ADMIN_ERROR_CODES.WORKFLOW_INVALID_INPUT,
  )
  async getUniqueGroups(
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<NodeGroupResponseDto[]>> {
    const result = await this.nodeDefinitionAdminService.getUniqueGroups(employeeId);
    return ApiResponseDto.success(result, 'Lấy danh sách unique groups thành công');
  }

  /**
   * Cập nhật node definition (chỉ admin)
   */
  @Patch(':id')
  @ApiOperation({ summary: 'Cập nhật node definition (chỉ description và displayName)' })
  @ApiParam({
    name: 'id',
    description: 'ID của node definition',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiBody({ type: UpdateNodeDefinitionDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Node definition đã được cập nhật',
    schema: ApiResponseDto.getSchema(NodeDefinitionResponseDto),
  })
  @ApiErrorResponse(
    WORKFLOW_ADMIN_ERROR_CODES.NODE_DEFINITION_NOT_FOUND,
    WORKFLOW_ADMIN_ERROR_CODES.NODE_DEFINITION_UPDATE_FAILED,
    WORKFLOW_ADMIN_ERROR_CODES.NODE_DEFINITION_INVALID_ID,
    WORKFLOW_ADMIN_ERROR_CODES.WORKFLOW_INVALID_INPUT,
  )
  async updateNodeDefinition(
    @CurrentEmployee('id') employeeId: number,
    @Param('id', ParseUUIDPipe) nodeDefinitionId: string,
    @Body() updateDto: UpdateNodeDefinitionDto,
  ): Promise<ApiResponseDto<NodeDefinitionResponseDto>> {
    const result = await this.nodeDefinitionAdminService.updateNodeDefinition(
      employeeId,
      nodeDefinitionId,
      updateDto,
    );
    return ApiResponseDto.success(result, 'Cập nhật node definition thành công');
  }
}
