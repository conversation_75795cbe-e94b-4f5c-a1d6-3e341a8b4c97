import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Sse,
  UseGuards,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiBody,
  getSchemaPath,
} from '@nestjs/swagger';
import { Observable } from 'rxjs';
import { JwtUserGuard } from '@/modules/auth/guards';
import { CurrentUser } from '@/modules/auth/decorators';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { SseCorsHeaders } from '@/common/decorators/cors-headers.decorator';
import { ApiResponseDto } from '@/common/response';
import { ZaloQRCodeSseService } from '../services/zalo-qr-code-sse.service';
import {
  CreateQRCodeSessionDto,
  QRCodeSessionResponseDto,
  QRCodeSessionStatusDto,
  QRCodeSseMessage,
} from '../dto/zalo-qr-code-sse.dto';

/**
 * Controller xử lý QR Code SSE cho Zalo login
 */
@ApiTags(SWAGGER_API_TAGS.ZALO_QR_CODE_SSE || 'Zalo QR Code SSE')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('marketing/zalo/qr-code')
export class ZaloQRCodeSseController {
  private readonly logger = new Logger(ZaloQRCodeSseController.name);

  constructor(private readonly qrCodeSseService: ZaloQRCodeSseService) {}

  /**
   * Tạo QR code session mới
   */
  @Post('session')
  @ApiOperation({
    summary: 'Tạo QR code session mới',
    description:
      'Tạo session QR code để đăng nhập Zalo thông qua automation-web',
  })
  @ApiBody({
    type: CreateQRCodeSessionDto,
    examples: {
      basic: {
        summary: 'Tạo QR code session',
        description: 'Ví dụ tạo QR code session với integration ID',
        value: {
          integrationId: 'uuid-integration-id-123',
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Tạo QR code session thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(QRCodeSessionResponseDto) },
          },
        },
      ],
    },
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu đầu vào không hợp lệ' })
  @ApiResponse({ status: 500, description: 'Lỗi server hoặc automation-web' })
  async createQRCodeSession(
    @CurrentUser() user: JwtPayload,
    @Body() createDto: CreateQRCodeSessionDto,
  ): Promise<ApiResponseDto<QRCodeSessionResponseDto>> {
    this.logger.log(
      `User ${user.id} creating QR code session for integration: ${createDto.integrationId}`,
    );

    const result = await this.qrCodeSseService.createQRCodeSession(
      user.id,
      createDto,
    );

    return ApiResponseDto.success(result, 'Tạo QR code session thành công');
  }

  /**
   * Lấy trạng thái session
   */
  @Get('session/:sessionId/status')
  @ApiOperation({
    summary: 'Lấy trạng thái QR code session',
    description: 'Kiểm tra trạng thái hiện tại của QR code session',
  })
  @ApiParam({
    name: 'sessionId',
    description: 'ID của session cần kiểm tra',
    example: 'session-uuid-123',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy trạng thái session thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(QRCodeSessionStatusDto) },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Session không tồn tại hoặc đã hết hạn',
  })
  async getSessionStatus(
    @CurrentUser() user: JwtPayload,
    @Param('sessionId') sessionId: string,
  ): Promise<ApiResponseDto<QRCodeSessionStatusDto | null>> {
    this.logger.log(
      `User ${user.id} checking status for session: ${sessionId}`,
    );

    const status = await this.qrCodeSseService.getSessionStatus(sessionId);

    if (!status) {
      return ApiResponseDto.error('Session không tồn tại hoặc đã hết hạn');
    }

    return ApiResponseDto.success(status, 'Lấy trạng thái session thành công');
  }

  /**
   * SSE stream để theo dõi trạng thái QR code real-time
   */
  @Get('session/:sessionId/stream')
  @SseCorsHeaders()
  @ApiOperation({
    summary: 'SSE stream theo dõi trạng thái QR code',
    description: `SSE endpoint để theo dõi trạng thái QR code real-time. 
    
    **Events được gửi:**
    - \`connected\`: Kết nối thành công
    - \`qr_generated\`: QR code đã được tạo
    - \`login_success\`: Đăng nhập thành công
    - \`qr_expired\`: QR code đã hết hạn
    - \`heartbeat\`: Heartbeat để duy trì kết nối
    - \`error\`: Có lỗi xảy ra
    
    **Cách sử dụng:**
    \`\`\`javascript
    const eventSource = new EventSource('/api/v1/marketing/zalo/qr-code/session/{sessionId}/stream');
    
    eventSource.onmessage = (event) => {
      const data = JSON.parse(event.data);
      console.log('Event:', data.event, 'Data:', data.data);
      
      if (data.event === 'login_success') {
        console.log('Đăng nhập thành công!', data.data);
        eventSource.close();
      }
    };
    \`\`\``,
  })
  @ApiParam({
    name: 'sessionId',
    description: 'ID của session cần theo dõi',
    example: 'session-uuid-123',
  })
  @ApiResponse({
    status: 200,
    description: 'SSE stream được thiết lập thành công',
    content: {
      'text/event-stream': {
        schema: {
          type: 'string',
          example:
            'data: {"event":"login_success","data":{"session_id":"...","zalo_uid":"..."},"timestamp":"2024-01-01T10:00:00.000Z"}\n\n',
        },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Session không tồn tại' })
  @Sse()
  streamQRCodeStatus(
    @CurrentUser() user: JwtPayload,
    @Param('sessionId') sessionId: string,
  ): Observable<QRCodeSseMessage> {
    this.logger.log(
      `User ${user.id} starting SSE stream for session: ${sessionId}`,
    );

    return this.qrCodeSseService.createSseStream(sessionId);
  }
}
